// src/components/admin/DocumentAnalysisViewer.tsx
'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FileText, 
  Eye, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Brain,
  Scan,
  Database,
  Shield,
  Clock
} from 'lucide-react';

interface DocumentAnalysis {
  documentType: string;
  extractedData: Record<string, any>;
  confidence: number;
  isValid: boolean;
  issues: string[];
  ocrText: string;
  processingTime?: number;
  timestamp?: Date;
}

interface DocumentAnalysisViewerProps {
  analyses: DocumentAnalysis[];
  entityType: 'merchant' | 'representative';
  className?: string;
}

export function DocumentAnalysisViewer({ 
  analyses, 
  entityType, 
  className = '' 
}: DocumentAnalysisViewerProps) {
  const [selectedAnalysis, setSelectedAnalysis] = useState<DocumentAnalysis | null>(null);
  const [showOCRText, setShowOCRText] = useState(false);

  const getDocumentTypeArabic = (documentType: string): string => {
    const translations = {
      'commercial_registration': 'السجل التجاري',
      'freelance_document': 'وثيقة العمل الحر',
      'driving_license': 'رخصة القيادة',
      'vehicle_inspection': 'شهادة الفحص الدوري',
      'national_id': 'الهوية الوطنية'
    };
    return translations[documentType as keyof typeof translations] || documentType;
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 90) return 'bg-green-100 text-green-800 border-green-200';
    if (confidence >= 70) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  const getValidityIcon = (isValid: boolean) => {
    return isValid ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const formatFieldName = (fieldName: string): string => {
    const fieldTranslations = {
      'ownerName': 'اسم المالك',
      'businessName': 'اسم المنشأة',
      'registrationNumber': 'رقم التسجيل',
      'documentNumber': 'رقم المستند',
      'issueDate': 'تاريخ الإصدار',
      'expiryDate': 'تاريخ الانتهاء',
      'issuingAuthority': 'جهة الإصدار',
      'businessActivity': 'النشاط التجاري',
      'licenseClass': 'فئة الرخصة',
      'vehicleInfo': 'معلومات المركبة',
      'plateNumber': 'رقم اللوحة',
      'model': 'الموديل',
      'year': 'سنة الصنع'
    };
    return fieldTranslations[fieldName as keyof typeof fieldTranslations] || fieldName;
  };

  const formatFieldValue = (value: any): string => {
    if (value instanceof Date) {
      return value.toLocaleDateString('ar-SA');
    }
    if (typeof value === 'object' && value !== null) {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  if (analyses.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-muted-foreground text-center">
            لا توجد مستندات محللة بعد
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* نظرة عامة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-500" />
            نتائج تحليل المستندات
          </CardTitle>
          <CardDescription>
            تحليل {analyses.length} مستند بواسطة الذكاء الاصطناعي
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{analyses.length}</div>
              <div className="text-sm text-muted-foreground">مستندات محللة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {analyses.filter(a => a.isValid).length}
              </div>
              <div className="text-sm text-muted-foreground">مستندات صحيحة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {(analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">متوسط الثقة</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* قائمة المستندات */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {analyses.map((analysis, index) => (
          <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  {getDocumentTypeArabic(analysis.documentType)}
                </CardTitle>
                <div className="flex items-center gap-2">
                  {getValidityIcon(analysis.isValid)}
                  <Badge className={getConfidenceColor(analysis.confidence)}>
                    {analysis.confidence}%
                  </Badge>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* شريط الثقة */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>نسبة الثقة</span>
                  <span>{analysis.confidence}%</span>
                </div>
                <Progress value={analysis.confidence} />
              </div>

              {/* المشاكل */}
              {analysis.issues.length > 0 && (
                <div>
                  <h4 className="text-sm font-semibold mb-2 text-red-600 flex items-center gap-1">
                    <AlertTriangle className="h-3 w-3" />
                    المشاكل المكتشفة:
                  </h4>
                  <ul className="space-y-1">
                    {analysis.issues.slice(0, 2).map((issue, issueIndex) => (
                      <li key={issueIndex} className="text-sm text-red-600 flex items-start gap-2">
                        <span className="w-1 h-1 bg-current rounded-full mt-2 flex-shrink-0" />
                        {issue}
                      </li>
                    ))}
                    {analysis.issues.length > 2 && (
                      <li className="text-sm text-muted-foreground">
                        و {analysis.issues.length - 2} مشاكل أخرى...
                      </li>
                    )}
                  </ul>
                </div>
              )}

              {/* البيانات المستخرجة (معاينة) */}
              <div>
                <h4 className="text-sm font-semibold mb-2">البيانات المستخرجة:</h4>
                <div className="space-y-1">
                  {Object.entries(analysis.extractedData).slice(0, 3).map(([key, value]) => (
                    <div key={key} className="flex justify-between text-sm">
                      <span className="text-muted-foreground">{formatFieldName(key)}:</span>
                      <span className="font-medium truncate max-w-32">
                        {formatFieldValue(value)}
                      </span>
                    </div>
                  ))}
                  {Object.keys(analysis.extractedData).length > 3 && (
                    <div className="text-sm text-muted-foreground">
                      و {Object.keys(analysis.extractedData).length - 3} حقول أخرى...
                    </div>
                  )}
                </div>
              </div>

              {/* أزرار العمل */}
              <div className="flex gap-2">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1"
                      onClick={() => setSelectedAnalysis(analysis)}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      عرض التفاصيل
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        تفاصيل تحليل {getDocumentTypeArabic(analysis.documentType)}
                      </DialogTitle>
                      <DialogDescription>
                        نتائج مفصلة لتحليل المستند بواسطة الذكاء الاصطناعي
                      </DialogDescription>
                    </DialogHeader>
                    
                    <Tabs defaultValue="data" className="h-full">
                      <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="data">البيانات المستخرجة</TabsTrigger>
                        <TabsTrigger value="analysis">تحليل الجودة</TabsTrigger>
                        <TabsTrigger value="ocr">النص المستخرج</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="data" className="mt-4">
                        <ScrollArea className="h-96">
                          <div className="space-y-4">
                            {Object.entries(analysis.extractedData).map(([key, value]) => (
                              <div key={key} className="border rounded-lg p-3">
                                <div className="font-semibold text-sm mb-1">
                                  {formatFieldName(key)}
                                </div>
                                <div className="text-sm bg-muted p-2 rounded">
                                  {formatFieldValue(value)}
                                </div>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </TabsContent>
                      
                      <TabsContent value="analysis" className="mt-4">
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <Card>
                              <CardHeader className="pb-2">
                                <CardTitle className="text-sm flex items-center gap-2">
                                  <Shield className="h-4 w-4" />
                                  نسبة الثقة
                                </CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold">{analysis.confidence}%</div>
                                <Progress value={analysis.confidence} className="mt-2" />
                              </CardContent>
                            </Card>
                            
                            <Card>
                              <CardHeader className="pb-2">
                                <CardTitle className="text-sm flex items-center gap-2">
                                  <Clock className="h-4 w-4" />
                                  وقت المعالجة
                                </CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold">
                                  {analysis.processingTime || 'غير محدد'}
                                </div>
                                <div className="text-sm text-muted-foreground">ثانية</div>
                              </CardContent>
                            </Card>
                          </div>
                          
                          <div>
                            <h4 className="font-semibold mb-2">حالة الصحة:</h4>
                            <div className="flex items-center gap-2">
                              {getValidityIcon(analysis.isValid)}
                              <span className={analysis.isValid ? 'text-green-600' : 'text-red-600'}>
                                {analysis.isValid ? 'مستند صحيح' : 'مستند غير صحيح'}
                              </span>
                            </div>
                          </div>
                          
                          {analysis.issues.length > 0 && (
                            <div>
                              <h4 className="font-semibold mb-2 text-red-600">المشاكل المكتشفة:</h4>
                              <ul className="space-y-2">
                                {analysis.issues.map((issue, issueIndex) => (
                                  <li key={issueIndex} className="flex items-start gap-2 text-sm">
                                    <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                                    {issue}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="ocr" className="mt-4">
                        <ScrollArea className="h-96">
                          <div className="bg-muted p-4 rounded-lg">
                            <h4 className="font-semibold mb-2 flex items-center gap-2">
                              <Scan className="h-4 w-4" />
                              النص المستخرج من المستند:
                            </h4>
                            <pre className="text-sm whitespace-pre-wrap font-mono">
                              {analysis.ocrText || 'لا يوجد نص مستخرج'}
                            </pre>
                          </div>
                        </ScrollArea>
                      </TabsContent>
                    </Tabs>
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
