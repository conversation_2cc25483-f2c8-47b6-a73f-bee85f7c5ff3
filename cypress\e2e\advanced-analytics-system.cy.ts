/// <reference types="cypress" />

describe('🔬 نظام التحليلات المتقدمة للتجار', () => {
  beforeEach(() => {
    // تسجيل دخول وهمي كتاجر
    cy.mockLogin('merchant');
    
    // إعداد بيانات وهمية للاختبار
    cy.window().then((win) => {
      // بيانات وهمية للطلبات
      const mockOrders = [
        {
          id: 'order-1',
          merchantId: 'test-merchant-uid',
          customerId: 'customer-1',
          totalAmount: 250,
          status: 'completed',
          createdAt: { toDate: () => new Date('2024-01-15') },
          items: [
            { productId: 'prod-1', productName: 'منتج 1', quantity: 2, price: 100, category: 'إلكترونيات' },
            { productId: 'prod-2', productName: 'منتج 2', quantity: 1, price: 50, category: 'ملابس' }
          ]
        },
        {
          id: 'order-2',
          merchantId: 'test-merchant-uid',
          customerId: 'customer-2',
          totalAmount: 180,
          status: 'completed',
          createdAt: { toDate: () => new Date('2024-01-20') },
          items: [
            { productId: 'prod-1', productName: 'منتج 1', quantity: 1, price: 100, category: 'إلكترونيات' },
            { productId: 'prod-3', productName: 'منتج 3', quantity: 1, price: 80, category: 'كتب' }
          ]
        }
      ];

      // بيانات وهمية للمنتجات
      const mockProducts = [
        { id: 'prod-1', name: 'منتج 1', category: 'إلكترونيات', merchantId: 'test-merchant-uid' },
        { id: 'prod-2', name: 'منتج 2', category: 'ملابس', merchantId: 'test-merchant-uid' },
        { id: 'prod-3', name: 'منتج 3', category: 'كتب', merchantId: 'test-merchant-uid' }
      ];

      // بيانات وهمية للعملاء
      const mockCustomers = [
        { id: 'customer-1', displayName: 'أحمد محمد', email: '<EMAIL>' },
        { id: 'customer-2', displayName: 'فاطمة علي', email: '<EMAIL>' }
      ];

      // حفظ البيانات الوهمية
      win.localStorage.setItem('mockOrders', JSON.stringify(mockOrders));
      win.localStorage.setItem('mockProducts', JSON.stringify(mockProducts));
      win.localStorage.setItem('mockCustomers', JSON.stringify(mockCustomers));
    });
  });

  afterEach(() => {
    // تنظيف البيانات الوهمية
    cy.window().then((win) => {
      win.localStorage.removeItem('mockOrders');
      win.localStorage.removeItem('mockProducts');
      win.localStorage.removeItem('mockCustomers');
    });
    cy.mockLogout();
  });

  describe('📊 صفحة التقارير الرئيسية', () => {
    it('يجب أن تعرض صفحة التقارير بشكل صحيح', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود العنوان الرئيسي
      cy.contains('التقارير والتحليلات').should('be.visible');
      
      // التحقق من وجود أدوات التحكم
      cy.get('[data-testid="time-range-selector"]').should('be.visible');
      cy.get('[data-testid="refresh-button"]').should('be.visible');
      cy.get('[data-testid="export-button"]').should('be.visible');
      
      // التحقق من وجود التبويبات
      cy.contains('نظرة عامة').should('be.visible');
      cy.contains('المبيعات').should('be.visible');
      cy.contains('العملاء').should('be.visible');
      cy.contains('المنتجات').should('be.visible');
    });

    it('يجب أن تعمل أدوات التحكم بالفترة الزمنية', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // اختبار تغيير الفترة الزمنية
      cy.get('[data-testid="time-range-selector"]').click();
      cy.contains('7 أيام').click();
      
      // التحقق من تحديث البيانات
      cy.get('[data-testid="loading-indicator"]').should('be.visible');
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 10000 });
      
      // اختبار فترات أخرى
      cy.get('[data-testid="time-range-selector"]').click();
      cy.contains('90 يوم').click();
      
      cy.get('[data-testid="time-range-selector"]').click();
      cy.contains('سنة').click();
    });

    it('يجب أن يعمل زر التحديث', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // النقر على زر التحديث
      cy.get('[data-testid="refresh-button"]').click();
      
      // التحقق من ظهور مؤشر التحميل
      cy.get('[data-testid="loading-indicator"]').should('be.visible');
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 10000 });
    });
  });

  describe('📈 مؤشرات الأداء الرئيسية (KPI)', () => {
    it('يجب أن تعرض مؤشرات الأداء الرئيسية', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود مؤشرات الأداء الأساسية
      cy.contains('إجمالي الإيرادات').should('be.visible');
      cy.contains('إجمالي الطلبات').should('be.visible');
      cy.contains('إجمالي العملاء').should('be.visible');
      cy.contains('متوسط قيمة الطلب').should('be.visible');
      
      // التحقق من وجود مؤشرات إضافية
      cy.contains('معدل التحويل').should('be.visible');
      cy.contains('معدل الاحتفاظ').should('be.visible');
      cy.contains('رضا العملاء').should('be.visible');
      cy.contains('المنتجات النشطة').should('be.visible');
    });

    it('يجب أن تعرض القيم والنمو بشكل صحيح', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود القيم الرقمية
      cy.get('[data-testid="kpi-revenue"]').within(() => {
        cy.get('[data-testid="kpi-value"]').should('contain.text', 'ريال');
        cy.get('[data-testid="kpi-growth"]').should('be.visible');
        cy.get('[data-testid="kpi-progress"]').should('be.visible');
      });
      
      cy.get('[data-testid="kpi-orders"]').within(() => {
        cy.get('[data-testid="kpi-value"]').should('match', /\d+/);
        cy.get('[data-testid="kpi-growth"]').should('be.visible');
      });
    });

    it('يجب أن تعرض حالات المؤشرات بالألوان الصحيحة', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود شارات الحالة
      cy.get('[data-testid="kpi-status-badge"]').should('have.length.at.least', 4);
      
      // التحقق من الألوان المختلفة للحالات
      cy.get('[data-testid="kpi-status-excellent"]').should('exist');
      cy.get('[data-testid="kpi-status-good"]').should('exist');
    });
  });

  describe('📊 الرسوم البيانية المتقدمة', () => {
    it('يجب أن تعرض الرسوم البيانية التفاعلية', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود لوحة الرسوم البيانية
      cy.get('[data-testid="advanced-charts-panel"]').should('be.visible');
      
      // التحقق من أدوات التحكم في نوع الرسم البياني
      cy.get('[data-testid="chart-type-selector"]').should('be.visible');
      cy.get('[data-testid="chart-type-selector"]').click();
      
      // اختبار أنواع الرسوم البيانية المختلفة
      cy.contains('خطي').click();
      cy.get('[data-testid="line-chart"]').should('be.visible');
      
      cy.get('[data-testid="chart-type-selector"]').click();
      cy.contains('أعمدة').click();
      cy.get('[data-testid="bar-chart"]').should('be.visible');
      
      cy.get('[data-testid="chart-type-selector"]').click();
      cy.contains('دائري').click();
      cy.get('[data-testid="pie-chart"]').should('be.visible');
    });

    it('يجب أن تعمل الإحصائيات السريعة', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود الإحصائيات السريعة
      cy.get('[data-testid="quick-stats"]').should('be.visible');
      cy.get('[data-testid="quick-stats"]').within(() => {
        cy.contains('إجمالي الإيرادات').should('be.visible');
        cy.contains('إجمالي الطلبات').should('be.visible');
        cy.contains('إجمالي العملاء').should('be.visible');
        cy.contains('متوسط قيمة الطلب').should('be.visible');
      });
    });
  });

  describe('💰 تبويب المبيعات', () => {
    it('يجب أن يعرض تحليلات المبيعات التفصيلية', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // النقر على تبويب المبيعات
      cy.contains('المبيعات').click();
      
      // التحقق من وجود ملخص المبيعات
      cy.get('[data-testid="sales-summary"]').should('be.visible');
      cy.get('[data-testid="sales-summary"]').within(() => {
        cy.contains('إجمالي الإيرادات').should('be.visible');
        cy.contains('إجمالي الطلبات').should('be.visible');
        cy.contains('متوسط قيمة الطلب').should('be.visible');
        cy.contains('معدل التحويل').should('be.visible');
      });
      
      // التحقق من وجود الرسم البياني للمبيعات
      cy.get('[data-testid="sales-chart"]').should('be.visible');
    });

    it('يجب أن تعرض مؤشرات النمو', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.contains('المبيعات').click();
      
      // التحقق من وجود مؤشرات النمو
      cy.get('[data-testid="growth-indicator"]').should('have.length.at.least', 1);
      cy.get('[data-testid="growth-percentage"]').should('be.visible');
    });
  });

  describe('👥 تبويب العملاء', () => {
    it('يجب أن يعرض تحليلات العملاء', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // النقر على تبويب العملاء
      cy.contains('العملاء').click();
      
      // التحقق من وجود إحصائيات العملاء
      cy.get('[data-testid="customer-stats"]').should('be.visible');
      cy.get('[data-testid="customer-stats"]').within(() => {
        cy.contains('إجمالي العملاء').should('be.visible');
        cy.contains('عملاء جدد').should('be.visible');
        cy.contains('معدل الاحتفاظ').should('be.visible');
        cy.contains('القيمة الدائمة للعميل').should('be.visible');
      });
    });

    it('يجب أن يعرض قائمة أفضل العملاء', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.contains('العملاء').click();
      
      // التحقق من وجود قائمة أفضل العملاء
      cy.contains('أفضل العملاء').should('be.visible');
      cy.get('[data-testid="top-customers-list"]').should('be.visible');
      
      // التحقق من وجود بيانات العملاء
      cy.get('[data-testid="customer-item"]').should('have.length.at.least', 1);
      cy.get('[data-testid="customer-item"]').first().within(() => {
        cy.get('[data-testid="customer-name"]').should('be.visible');
        cy.get('[data-testid="customer-orders"]').should('be.visible');
        cy.get('[data-testid="customer-spent"]').should('be.visible');
      });
    });
  });

  describe('📦 تبويب المنتجات', () => {
    it('يجب أن يعرض أداء المنتجات', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // النقر على تبويب المنتجات
      cy.contains('المنتجات').click();
      
      // التحقق من وجود قائمة أداء المنتجات
      cy.contains('أداء المنتجات').should('be.visible');
      cy.get('[data-testid="product-performance-list"]').should('be.visible');
      
      // التحقق من وجود بيانات المنتجات
      cy.get('[data-testid="product-item"]').should('have.length.at.least', 1);
      cy.get('[data-testid="product-item"]').first().within(() => {
        cy.get('[data-testid="product-name"]').should('be.visible');
        cy.get('[data-testid="product-category"]').should('be.visible');
        cy.get('[data-testid="product-revenue"]').should('be.visible');
        cy.get('[data-testid="product-rating"]').should('be.visible');
      });
    });
  });

  describe('📤 وظائف التصدير', () => {
    it('يجب أن تعمل وظيفة التصدير', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // النقر على زر التصدير
      cy.get('[data-testid="export-button"]').click();
      
      // التحقق من ظهور رسالة التأكيد أو التحميل
      cy.contains('سيتم تطبيق تصدير التقارير').should('be.visible');
    });
  });

  describe('🔄 التحديث التلقائي', () => {
    it('يجب أن تتحدث البيانات عند تغيير الفترة الزمنية', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // حفظ القيمة الأولية
      cy.get('[data-testid="kpi-revenue"]').find('[data-testid="kpi-value"]').invoke('text').as('initialRevenue');
      
      // تغيير الفترة الزمنية
      cy.get('[data-testid="time-range-selector"]').click();
      cy.contains('7 أيام').click();
      
      // انتظار التحديث
      cy.wait(2000);
      
      // التحقق من تحديث البيانات (قد تتغير أو تبقى نفسها حسب البيانات الوهمية)
      cy.get('[data-testid="kpi-revenue"]').find('[data-testid="kpi-value"]').should('be.visible');
    });
  });

  describe('📱 التجاوب مع الأجهزة المختلفة', () => {
    it('يجب أن تعمل على الأجهزة المحمولة', () => {
      cy.viewport('iphone-x');
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من ظهور المحتوى بشكل صحيح على الهاتف
      cy.contains('التقارير والتحليلات').should('be.visible');
      cy.get('[data-testid="kpi-revenue"]').should('be.visible');
      
      // التحقق من عمل التبويبات على الهاتف
      cy.contains('المبيعات').click();
      cy.get('[data-testid="sales-summary"]').should('be.visible');
    });

    it('يجب أن تعمل على الأجهزة اللوحية', () => {
      cy.viewport('ipad-2');
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من التخطيط على الجهاز اللوحي
      cy.contains('التقارير والتحليلات').should('be.visible');
      cy.get('[data-testid="advanced-charts-panel"]').should('be.visible');
    });
  });

  describe('🌐 دعم اللغة العربية', () => {
    it('يجب أن تعرض النصوص باللغة العربية', () => {
      cy.visitWithLocale('/merchant/reports', 'ar');
      
      // التحقق من النصوص العربية
      cy.contains('التقارير والتحليلات').should('be.visible');
      cy.contains('إجمالي الإيرادات').should('be.visible');
      cy.contains('إجمالي الطلبات').should('be.visible');
      cy.contains('نظرة عامة').should('be.visible');
      cy.contains('المبيعات').should('be.visible');
      cy.contains('العملاء').should('be.visible');
      cy.contains('المنتجات').should('be.visible');
    });

    it('يجب أن تدعم التخطيط من اليمين إلى اليسار (RTL)', () => {
      cy.visitWithLocale('/merchant/reports', 'ar');
      
      // التحقق من اتجاه النص
      cy.get('html').should('have.attr', 'dir', 'rtl');
      cy.get('body').should('have.css', 'direction', 'rtl');
    });
  });

  describe('⚡ الأداء والتحميل', () => {
    it('يجب أن تحمل الصفحة في وقت معقول', () => {
      const startTime = Date.now();
      
      cy.visitWithLocale('/merchant/reports');
      cy.contains('التقارير والتحليلات').should('be.visible');
      
      cy.then(() => {
        const loadTime = Date.now() - startTime;
        expect(loadTime).to.be.lessThan(5000); // أقل من 5 ثوان
      });
    });

    it('يجب أن تعرض مؤشرات التحميل', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من ظهور مؤشر التحميل في البداية
      cy.get('[data-testid="loading-indicator"]').should('be.visible');
      
      // التحقق من اختفاء مؤشر التحميل بعد التحميل
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 10000 });
    });
  });
});
