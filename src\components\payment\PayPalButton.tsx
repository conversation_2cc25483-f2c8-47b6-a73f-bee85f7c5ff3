"use client";

import React, { useEffect, useState } from 'react';
import { PayPalScriptProvider, PayPalButtons } from '@paypal/react-paypal-js';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';

interface PayPalButtonProps {
  orderId: string;
  amount: number;
  currency?: string;
  description?: string;
  onSuccess: (details: any) => void;
  onError: (error: string) => void;
  onCancel?: () => void;
  disabled?: boolean;
  className?: string;
}

interface PayPalInitialOptions {
  clientId: string;
  currency: string;
  intent: 'capture';
  components: string;
  locale: string;
}

export default function PayPalButton({
  orderId,
  amount,
  currency = 'USD',
  description,
  onSuccess,
  onError,
  onCancel,
  disabled = false,
  className
}: PayPalButtonProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID;

  // التحقق من وجود Client ID
  useEffect(() => {
    if (!clientId) {
      setError('PayPal Client ID غير مُكوّن');
      setIsLoading(false);
      return;
    }
    setIsLoading(false);
  }, [clientId]);

  // إعدادات PayPal الأولية
  const initialOptions: PayPalInitialOptions = {
    clientId: clientId || '',
    currency: currency,
    intent: 'capture',
    components: 'buttons',
    locale: 'ar_SA'
  };

  // إنشاء الطلب
  const createOrder = async () => {
    try {
      setIsProcessing(true);
      
      const response = await fetch('/api/paypal/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          amount,
          currency,
          description
        }),
      });

      if (!response.ok) {
        throw new Error('فشل في إنشاء طلب PayPal');
      }

      const data = await response.json();
      return data.id;
    } catch (error) {
      console.error('خطأ في إنشاء طلب PayPal:', error);
      const errorMessage = error instanceof Error ? error.message : 'خطأ في إنشاء الطلب';
      setError(errorMessage);
      onError(errorMessage);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  // تأكيد الدفع
  const onApprove = async (data: any) => {
    try {
      setIsProcessing(true);
      
      const response = await fetch('/api/paypal/capture-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderID: data.orderID,
          orderId: orderId
        }),
      });

      if (!response.ok) {
        throw new Error('فشل في تأكيد الدفع');
      }

      const details = await response.json();
      
      if (details.status === 'COMPLETED') {
        toast.success('تم الدفع بنجاح عبر PayPal');
        onSuccess(details);
      } else {
        throw new Error('لم يتم إكمال الدفع');
      }
    } catch (error) {
      console.error('خطأ في تأكيد الدفع:', error);
      const errorMessage = error instanceof Error ? error.message : 'خطأ في تأكيد الدفع';
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  // معالجة الأخطاء
  const onErrorHandler = (error: any) => {
    console.error('خطأ PayPal:', error);
    const errorMessage = 'حدث خطأ في PayPal';
    setError(errorMessage);
    onError(errorMessage);
    toast.error(errorMessage);
  };

  // معالجة الإلغاء
  const onCancelHandler = () => {
    console.log('تم إلغاء الدفع');
    toast.info('تم إلغاء عملية الدفع');
    if (onCancel) {
      onCancel();
    }
  };

  // عرض حالة التحميل
  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <Loader2 className="w-8 h-8 animate-spin mx-auto text-blue-500" />
            <p className="text-sm text-muted-foreground">جاري تحميل PayPal...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // عرض الخطأ
  if (error) {
    return (
      <Card className={className}>
        <CardContent className="py-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            variant="outline" 
            className="w-full mt-4"
            onClick={() => {
              setError(null);
              setIsLoading(true);
            }}
          >
            إعادة المحاولة
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="py-6">
        <div className="space-y-4">
          {/* معلومات الدفع */}
          <div className="text-center space-y-2">
            <h3 className="font-semibold">الدفع عبر PayPal</h3>
            <p className="text-2xl font-bold text-blue-600">
              {new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: currency
              }).format(amount)}
            </p>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
          </div>

          {/* حالة المعالجة */}
          {isProcessing && (
            <Alert>
              <Loader2 className="h-4 w-4 animate-spin" />
              <AlertDescription>
                جاري معالجة الدفع... يرجى عدم إغلاق هذه الصفحة
              </AlertDescription>
            </Alert>
          )}

          {/* أزرار PayPal */}
          <PayPalScriptProvider options={initialOptions}>
            <PayPalButtons
              style={{
                layout: 'vertical',
                color: 'blue',
                shape: 'rect',
                label: 'paypal',
                height: 45
              }}
              disabled={disabled || isProcessing}
              createOrder={createOrder}
              onApprove={onApprove}
              onError={onErrorHandler}
              onCancel={onCancelHandler}
            />
          </PayPalScriptProvider>

          {/* معلومات الأمان */}
          <div className="text-center text-xs text-muted-foreground">
            <div className="flex items-center justify-center gap-1 mb-1">
              <CheckCircle className="w-3 h-3" />
              <span>محمي بأمان PayPal</span>
            </div>
            <p>
              جميع المعاملات مشفرة وآمنة. لا نحتفظ ببيانات الدفع.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
