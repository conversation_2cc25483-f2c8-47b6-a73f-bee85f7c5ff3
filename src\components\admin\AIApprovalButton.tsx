// src/components/admin/AIApprovalButton.tsx
'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Brain, Zap, CheckCircle, XCircle, AlertTriangle, Eye, FileText } from 'lucide-react';

interface AIApprovalButtonProps {
  entityId: string;
  entityType: 'merchant' | 'representative';
  onApproval: (result: AIApprovalResult) => void;
  disabled?: boolean;
  className?: string;
}

interface AIApprovalResult {
  decision: 'approve' | 'reject' | 'manual_review';
  confidence: number;
  reasons: string[];
  riskFactors: string[];
  recommendations: string[];
  extractedData: Record<string, any>;
}

export function AIApprovalButton({ 
  entityId, 
  entityType, 
  onApproval, 
  disabled = false,
  className = '' 
}: AIApprovalButtonProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<AIApprovalResult | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleAIApproval = async () => {
    setIsProcessing(true);
    setProgress(0);
    setResult(null);

    try {
      // محاكاة تقدم المعالجة
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 300);

      // استدعاء API المناسب حسب نوع الكيان
      const apiEndpoint = entityType === 'merchant' 
        ? '/api/ai/auto-approve-merchant'
        : '/api/ai/auto-approve-representative';

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          [entityType === 'merchant' ? 'merchantUid' : 'representativeUid']: entityId 
        })
      });

      clearInterval(progressInterval);
      setProgress(100);

      if (!response.ok) {
        throw new Error('فشل في الموافقة الذكية');
      }

      const aiResult = await response.json();
      setResult(aiResult);
      
      // إشعار المكون الأب بالنتيجة
      onApproval(aiResult);

    } catch (error) {
      console.error('خطأ في الموافقة الذكية:', error);
      setResult({
        decision: 'manual_review',
        confidence: 0,
        reasons: ['حدث خطأ في النظام'],
        riskFactors: ['خطأ تقني'],
        recommendations: ['مراجعة يدوية مطلوبة'],
        extractedData: {}
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const getDecisionIcon = (decision: string) => {
    switch (decision) {
      case 'approve':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'reject':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'manual_review':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      default:
        return <Brain className="h-5 w-5" />;
    }
  };

  const getDecisionText = (decision: string) => {
    switch (decision) {
      case 'approve':
        return 'موافقة تلقائية';
      case 'reject':
        return 'رفض تلقائي';
      case 'manual_review':
        return 'مراجعة يدوية';
      default:
        return 'غير محدد';
    }
  };

  const getDecisionColor = (decision: string) => {
    switch (decision) {
      case 'approve':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'reject':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'manual_review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* زر الموافقة الذكية */}
      <Button
        onClick={handleAIApproval}
        disabled={disabled || isProcessing}
        className="w-full bg-purple-600 hover:bg-purple-700 text-white"
      >
        {isProcessing ? (
          <>
            <Zap className="h-4 w-4 mr-2 animate-spin" />
            جاري التحليل الذكي...
          </>
        ) : (
          <>
            <Brain className="h-4 w-4 mr-2" />
            موافقة ذكية بالـ AI
          </>
        )}
      </Button>

      {/* شريط التقدم */}
      {isProcessing && (
        <div className="space-y-2">
          <Progress value={progress} className="w-full" />
          <p className="text-sm text-muted-foreground text-center">
            {progress < 30 && 'تحليل المستندات...'}
            {progress >= 30 && progress < 60 && 'التحقق من البيانات...'}
            {progress >= 60 && progress < 90 && 'حساب المخاطر...'}
            {progress >= 90 && 'اتخاذ القرار...'}
          </p>
        </div>
      )}

      {/* عرض النتيجة */}
      {result && (
        <Card className="border-2">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-lg">
                {getDecisionIcon(result.decision)}
                نتيجة التحليل الذكي
              </CardTitle>
              <Badge className={getDecisionColor(result.decision)}>
                {getDecisionText(result.decision)}
              </Badge>
            </div>
            <CardDescription>
              نسبة الثقة: {result.confidence}%
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* الأسباب */}
            <div>
              <h4 className="font-semibold text-sm mb-2">الأسباب:</h4>
              <ul className="space-y-1">
                {result.reasons.map((reason, index) => (
                  <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                    <span className="w-1 h-1 bg-current rounded-full mt-2 flex-shrink-0" />
                    {reason}
                  </li>
                ))}
              </ul>
            </div>

            {/* عوامل المخاطر */}
            {result.riskFactors.length > 0 && (
              <div>
                <h4 className="font-semibold text-sm mb-2 text-red-600">عوامل المخاطر:</h4>
                <ul className="space-y-1">
                  {result.riskFactors.map((factor, index) => (
                    <li key={index} className="text-sm text-red-600 flex items-start gap-2">
                      <AlertTriangle className="w-3 h-3 mt-0.5 flex-shrink-0" />
                      {factor}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* التوصيات */}
            {result.recommendations.length > 0 && (
              <div>
                <h4 className="font-semibold text-sm mb-2 text-blue-600">التوصيات:</h4>
                <ul className="space-y-1">
                  {result.recommendations.map((recommendation, index) => (
                    <li key={index} className="text-sm text-blue-600 flex items-start gap-2">
                      <span className="w-1 h-1 bg-current rounded-full mt-2 flex-shrink-0" />
                      {recommendation}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* زر عرض التفاصيل */}
            <Dialog open={showDetails} onOpenChange={setShowDetails}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full">
                  <Eye className="h-4 w-4 mr-2" />
                  عرض البيانات المستخرجة
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    البيانات المستخرجة من المستندات
                  </DialogTitle>
                  <DialogDescription>
                    البيانات التي تم استخراجها بواسطة الذكاء الاصطناعي
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  {Object.entries(result.extractedData).map(([documentType, data]) => (
                    <Card key={documentType}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">
                          {documentType === 'commercial_registration' && 'السجل التجاري'}
                          {documentType === 'freelance_document' && 'وثيقة العمل الحر'}
                          {documentType === 'driving_license' && 'رخصة القيادة'}
                          {documentType === 'vehicle_inspection' && 'شهادة الفحص الدوري'}
                          {documentType === 'national_id' && 'الهوية الوطنية'}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          {Object.entries(data as Record<string, any>).map(([key, value]) => (
                            <div key={key} className="space-y-1">
                              <span className="font-medium text-muted-foreground">{key}:</span>
                              <span className="block">{String(value)}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
