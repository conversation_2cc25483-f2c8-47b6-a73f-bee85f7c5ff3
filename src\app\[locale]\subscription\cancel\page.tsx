"use client";

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { XCircle, ArrowLeft, RefreshCw, Home } from "lucide-react";
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { merchantPlans, customerPlans, representativePlans } from '@/constants/plans';
import type { SubscriptionPlan } from '@/types';

export default function SubscriptionCancelPage() {
  const { t, locale } = useLocale();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);

  useEffect(() => {
    const planId = searchParams.get('plan');
    
    if (planId) {
      const allPlans = [...merchantPlans, ...customerPlans, ...representativePlans];
      const plan = allPlans.find(p => p.id === planId);
      setSelectedPlan(plan || null);
    }
  }, [searchParams]);

  const formatPrice = (plan: SubscriptionPlan) => {
    if (plan.priceDisplayKey === 'free') {
      return t('free');
    }
    if (plan.priceValue !== undefined && plan.currencyKey && plan.periodKey) {
      return t(plan.priceDisplayKey, {
        price: plan.priceValue,
        currency: t(plan.currencyKey),
        period: t(plan.periodKey)
      });
    }
    return '';
  };

  const handleTryAgain = () => {
    if (selectedPlan) {
      router.push(`/${locale}/subscription?plan=${selectedPlan.id}`);
    } else {
      router.push(`/${locale}/pricing`);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-2xl mx-auto">
        {/* العودة للخلف */}
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          {t('back')}
        </Button>

        {/* رسالة الإلغاء */}
        <Card>
          <CardContent className="text-center py-12">
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-2">{t('subscriptionCancelled')}</h1>
            <p className="text-muted-foreground mb-6">
              {t('subscriptionCancelledMessage')}
            </p>

            {selectedPlan && (
              <div className="bg-muted p-4 rounded-lg mb-6 max-w-sm mx-auto">
                <h3 className="font-semibold mb-1">{t(selectedPlan.nameKey)}</h3>
                <p className="text-sm text-muted-foreground">
                  {formatPrice(selectedPlan)}
                </p>
              </div>
            )}

            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {t('noChargesApplied')}
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  onClick={handleTryAgain}
                  className="bg-primary hover:bg-primary/90"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  {t('tryAgain')}
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={() => router.push(`/${locale}/pricing`)}
                >
                  {t('viewAllPlans')}
                </Button>
                
                <Button 
                  variant="ghost" 
                  onClick={() => router.push(`/${locale}`)}
                >
                  <Home className="w-4 h-4 mr-2" />
                  {t('backToHome')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* معلومات إضافية */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="text-lg">{t('needHelp')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground">
                {t('subscriptionHelpMessage')}
              </p>
              
              <ul className="text-sm space-y-2">
                <li>• {t('contactSupport')}</li>
                <li>• {t('checkPaymentMethod')}</li>
                <li>• {t('tryDifferentPaymentMethod')}</li>
                <li>• {t('checkInternetConnection')}</li>
              </ul>
              
              <div className="pt-4">
                <Button variant="outline" size="sm">
                  {t('contactSupport')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
