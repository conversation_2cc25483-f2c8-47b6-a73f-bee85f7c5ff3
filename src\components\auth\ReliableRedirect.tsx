// src/components/auth/ReliableRedirect.tsx
"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ReliableRedirectProps {
  redirectPath: string;
  delay?: number;
  maxAttempts?: number;
  fallbackDelay?: number;
  onRedirectStart?: () => void;
  onRedirectSuccess?: () => void;
  onRedirectFailed?: () => void;
  children?: React.ReactNode;
}

export default function ReliableRedirect({
  redirectPath,
  delay = 1000,
  maxAttempts = 3,
  fallbackDelay = 2000,
  onRedirectStart,
  onRedirectSuccess,
  onRedirectFailed,
  children
}: ReliableRedirectProps) {
  const router = useRouter();
  const [attempts, setAttempts] = useState(0);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [redirectStatus, setRedirectStatus] = useState<'pending' | 'redirecting' | 'success' | 'failed'>('pending');

  useEffect(() => {
    let isMounted = true;
    let timeoutId: NodeJS.Timeout;

    const performRedirect = async () => {
      if (!isMounted || attempts >= maxAttempts) {
        if (attempts >= maxAttempts) {
          setRedirectStatus('failed');
          onRedirectFailed?.();
        }
        return;
      }

      setIsRedirecting(true);
      setRedirectStatus('redirecting');
      onRedirectStart?.();

      try {
        console.log(`🔄 Redirect attempt ${attempts + 1}/${maxAttempts} to:`, redirectPath);

        // المحاولة الأولى: استخدام Next.js router
        if (attempts === 0) {
          router.replace(redirectPath);

          // التحقق من نجاح التوجيه بعد تأخير
          timeoutId = setTimeout(() => {
            if (!isMounted) return;

            if (window.location.pathname === redirectPath || window.location.pathname.includes(redirectPath.split('?')[0])) {
              console.log('✅ Router redirect successful');
              setRedirectStatus('success');
              onRedirectSuccess?.();
            } else {
              console.log('⚠️ Router redirect failed, trying again...');
              setAttempts(prev => prev + 1);
            }
          }, fallbackDelay);
        }
        // المحاولة الثانية: استخدام window.location.replace
        else if (attempts === 1) {
          window.location.replace(redirectPath);
        }
        // المحاولة الثالثة: استخدام window.location.href
        else {
          window.location.href = redirectPath;
        }

      } catch (error) {
        console.error(`❌ Redirect attempt ${attempts + 1} failed:`, error);
        if (isMounted) {
          setAttempts(prev => prev + 1);
        }
      }
    };

    if (redirectStatus === 'pending' || (redirectStatus === 'redirecting' && attempts < maxAttempts)) {
      timeoutId = setTimeout(performRedirect, delay);
    }

    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [attempts, redirectPath, router, delay, maxAttempts, fallbackDelay, redirectStatus, onRedirectStart, onRedirectSuccess, onRedirectFailed]);

  // التوجيه اليدوي كحل أخير
  const handleManualRedirect = () => {
    console.log('🔄 Manual redirect triggered');
    window.location.href = redirectPath;
  };

  if (children) {
    return <>{children}</>;
  }

  return (
    <div className="flex flex-col items-center justify-center space-y-4 text-center">
      {redirectStatus === 'redirecting' && (
        <>
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">
            جاري التوجيه... (المحاولة {attempts + 1}/{maxAttempts})
          </p>
        </>
      )}

      {redirectStatus === 'success' && (
        <>
          <CheckCircle className="h-8 w-8 text-green-500" />
          <p className="text-sm text-green-600">تم التوجيه بنجاح!</p>
        </>
      )}

      {redirectStatus === 'failed' && (
        <>
          <AlertCircle className="h-8 w-8 text-red-500" />
          <p className="text-sm text-red-600 mb-2">فشل في التوجيه التلقائي</p>
          <Button
            onClick={handleManualRedirect}
            variant="default"
            size="sm"
            data-testid="manual-redirect-button"
          >
            الانتقال يدوياً
          </Button>
        </>
      )}
    </div>
  );
}
