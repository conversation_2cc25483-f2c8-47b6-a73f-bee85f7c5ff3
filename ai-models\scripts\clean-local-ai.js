#!/usr/bin/env node

// سكريبت تنظيف نظام الذكاء الاصطناعي المحلي
const fs = require('fs').promises;
const path = require('path');

class LocalAICleaner {
  constructor() {
    this.startTime = Date.now();
    this.deletedFiles = [];
    this.deletedDirs = [];
    this.freedSpace = 0;
  }

  /**
   * تشغيل التنظيف الكامل
   */
  async run() {
    try {
      console.log('🧹 بدء تنظيف نظام الذكاء الاصطناعي المحلي...');
      console.log('🗑️ حذف النماذج والملفات المؤقتة');
      console.log('=====================================\n');

      await this.cleanModels();
      await this.cleanCache();
      await this.cleanLogs();
      await this.cleanTempFiles();

      this.generateReport();
      console.log('✅ تم التنظيف بنجاح!');

    } catch (error) {
      console.error('❌ خطأ في التنظيف:', error);
      process.exit(1);
    }
  }

  /**
   * تنظيف النماذج
   */
  async cleanModels() {
    console.log('🤖 تنظيف النماذج...');

    const modelDirs = [
      'models/ocr',
      'models/nlp',
      'models/classification',
      'models/validation'
    ];

    for (const dir of modelDirs) {
      const dirPath = path.join(__dirname, '..', dir);
      await this.cleanDirectory(dirPath, '*.onnx');
      await this.cleanDirectory(dirPath, '*.traineddata');
      await this.cleanDirectory(dirPath, '*.pth');
      await this.cleanDirectory(dirPath, '*.br');
    }

    console.log('✅ تم تنظيف النماذج\n');
  }

  /**
   * تنظيف التخزين المؤقت
   */
  async cleanCache() {
    console.log('💾 تنظيف التخزين المؤقت...');

    const cacheDirs = [
      'cache',
      'engines/onnx-runtime/cache',
      'engines/tensorflow-js/cache',
      'engines/tesseract-js/cache'
    ];

    for (const dir of cacheDirs) {
      const dirPath = path.join(__dirname, '..', dir);
      await this.cleanDirectory(dirPath);
    }

    console.log('✅ تم تنظيف التخزين المؤقت\n');
  }

  /**
   * تنظيف السجلات
   */
  async cleanLogs() {
    console.log('📋 تنظيف السجلات...');

    const logDirs = [
      'logs',
      'logs/privacy',
      'logs/performance',
      'logs/errors'
    ];

    for (const dir of logDirs) {
      const dirPath = path.join(__dirname, '..', dir);
      await this.cleanDirectory(dirPath, '*.log');
      await this.cleanDirectory(dirPath, '*.txt');
    }

    console.log('✅ تم تنظيف السجلات\n');
  }

  /**
   * تنظيف الملفات المؤقتة
   */
  async cleanTempFiles() {
    console.log('🗂️ تنظيف الملفات المؤقتة...');

    const tempPatterns = [
      '*.tmp',
      '*.temp',
      '*.bak',
      '*.old',
      '.DS_Store',
      'Thumbs.db'
    ];

    const searchDirs = [
      '.',
      'models',
      'utils',
      'workers',
      'configs',
      'scripts'
    ];

    for (const dir of searchDirs) {
      const dirPath = path.join(__dirname, '..', dir);
      for (const pattern of tempPatterns) {
        await this.cleanDirectory(dirPath, pattern);
      }
    }

    console.log('✅ تم تنظيف الملفات المؤقتة\n');
  }

  /**
   * تنظيف مجلد
   */
  async cleanDirectory(dirPath, pattern = '*') {
    try {
      // التحقق من وجود المجلد
      try {
        await fs.access(dirPath);
      } catch (error) {
        // المجلد غير موجود
        return;
      }

      const files = await fs.readdir(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = await fs.stat(filePath);

        if (stats.isDirectory()) {
          // تنظيف المجلدات الفرعية
          await this.cleanDirectory(filePath, pattern);
          
          // حذف المجلد إذا كان فارغاً
          try {
            const subFiles = await fs.readdir(filePath);
            if (subFiles.length === 0) {
              await fs.rmdir(filePath);
              this.deletedDirs.push(filePath);
              console.log(`🗑️ حذف مجلد فارغ: ${path.relative(path.join(__dirname, '..'), filePath)}`);
            }
          } catch (error) {
            // تجاهل أخطاء حذف المجلدات
          }
        } else if (stats.isFile()) {
          // فحص الملف حسب النمط
          if (this.matchesPattern(file, pattern)) {
            await fs.unlink(filePath);
            this.deletedFiles.push(filePath);
            this.freedSpace += stats.size;
            console.log(`🗑️ حذف ملف: ${path.relative(path.join(__dirname, '..'), filePath)} (${this.formatSize(stats.size)})`);
          }
        }
      }

    } catch (error) {
      console.warn(`⚠️ تحذير في تنظيف ${dirPath}: ${error.message}`);
    }
  }

  /**
   * فحص تطابق النمط
   */
  matchesPattern(filename, pattern) {
    if (pattern === '*') {
      return true;
    }

    // تحويل نمط wildcard إلى regex
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');

    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(filename);
  }

  /**
   * تنسيق الحجم
   */
  formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * إنشاء تقرير التنظيف
   */
  generateReport() {
    const duration = Date.now() - this.startTime;
    const durationSeconds = (duration / 1000).toFixed(2);

    console.log('\n🧹 تقرير التنظيف:');
    console.log('================');
    console.log(`🗑️ ملفات محذوفة: ${this.deletedFiles.length}`);
    console.log(`📁 مجلدات محذوفة: ${this.deletedDirs.length}`);
    console.log(`💾 مساحة محررة: ${this.formatSize(this.freedSpace)}`);
    console.log(`⏱️ وقت التنظيف: ${durationSeconds} ثانية`);

    if (this.deletedFiles.length > 0) {
      console.log('\n🗑️ الملفات المحذوفة:');
      this.deletedFiles.slice(0, 10).forEach(file => {
        console.log(`  - ${path.relative(path.join(__dirname, '..'), file)}`);
      });
      
      if (this.deletedFiles.length > 10) {
        console.log(`  ... و ${this.deletedFiles.length - 10} ملف آخر`);
      }
    }

    if (this.deletedDirs.length > 0) {
      console.log('\n📁 المجلدات المحذوفة:');
      this.deletedDirs.forEach(dir => {
        console.log(`  - ${path.relative(path.join(__dirname, '..'), dir)}`);
      });
    }

    console.log('\n🎯 ما تم تنظيفه:');
    console.log('================');
    console.log('🤖 النماذج المحلية');
    console.log('💾 ملفات التخزين المؤقت');
    console.log('📋 ملفات السجلات');
    console.log('🗂️ الملفات المؤقتة');
    console.log('📁 المجلدات الفارغة');

    console.log('\n✨ النظام نظيف ومحسن للأداء!');
  }

  /**
   * تنظيف انتقائي للنماذج فقط
   */
  async cleanModelsOnly() {
    console.log('🤖 تنظيف النماذج فقط...');
    await this.cleanModels();
    this.generateReport();
  }

  /**
   * تنظيف انتقائي للتخزين المؤقت فقط
   */
  async cleanCacheOnly() {
    console.log('💾 تنظيف التخزين المؤقت فقط...');
    await this.cleanCache();
    this.generateReport();
  }

  /**
   * تنظيف انتقائي للسجلات فقط
   */
  async cleanLogsOnly() {
    console.log('📋 تنظيف السجلات فقط...');
    await this.cleanLogs();
    this.generateReport();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  const args = process.argv.slice(2);
  const cleaner = new LocalAICleaner();

  if (args.includes('--models-only')) {
    cleaner.cleanModelsOnly().catch(error => {
      console.error('❌ خطأ في تنظيف النماذج:', error);
      process.exit(1);
    });
  } else if (args.includes('--cache-only')) {
    cleaner.cleanCacheOnly().catch(error => {
      console.error('❌ خطأ في تنظيف التخزين المؤقت:', error);
      process.exit(1);
    });
  } else if (args.includes('--logs-only')) {
    cleaner.cleanLogsOnly().catch(error => {
      console.error('❌ خطأ في تنظيف السجلات:', error);
      process.exit(1);
    });
  } else {
    cleaner.run().catch(error => {
      console.error('❌ خطأ في التنظيف:', error);
      process.exit(1);
    });
  }
}

module.exports = LocalAICleaner;
