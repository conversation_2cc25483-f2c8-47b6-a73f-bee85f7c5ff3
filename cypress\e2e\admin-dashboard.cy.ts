/// <reference types="cypress" />

/**
 * اختبارات لوحة تحكم الإدارة
 * تختبر جميع وظائف إدارة النظام والمستخدمين
 */

describe('🛡️ لوحة تحكم الإدارة', () => {
  beforeEach(() => {
    // إعداد البيانات الوهمية
    cy.mockLogin('representative') // استخدام representative كمدير
    cy.mockFirebaseAuth()
    
    // زيارة لوحة تحكم الإدارة
    cy.visitWithLocale('/admin/dashboard')
    cy.waitForLoadingToFinish()
  })

  afterEach(() => {
    cy.mockLogout()
  })

  describe('📊 لوحة التحكم الرئيسية', () => {
    it('يجب أن تعرض لوحة تحكم الإدارة بشكل صحيح', () => {
      // التحقق من وجود العناصر الأساسية
      cy.get('[data-testid="admin-dashboard"]').should('be.visible')
      cy.shouldContainArabicText('لوحة تحكم الإدارة')
      cy.shouldContainArabicText('إحصائيات النظام')
      cy.shouldContainArabicText('المستخدمون')
      cy.shouldContainArabicText('التجار')
      cy.shouldContainArabicText('الطلبات')
      
      // التحقق من وجود الإحصائيات الرئيسية
      cy.get('[data-testid="total-users"]').should('be.visible')
      cy.get('[data-testid="total-merchants"]').should('be.visible')
      cy.get('[data-testid="total-orders"]').should('be.visible')
      cy.get('[data-testid="total-revenue"]').should('be.visible')
    })

    it('يجب أن تعرض الرسوم البيانية للتحليلات', () => {
      // التحقق من وجود الرسوم البيانية
      cy.get('[data-testid="users-growth-chart"]').should('be.visible')
      cy.get('[data-testid="revenue-chart"]').should('be.visible')
      cy.get('[data-testid="orders-chart"]').should('be.visible')
      cy.get('[data-testid="merchants-performance-chart"]').should('be.visible')
    })

    it('يجب أن تعرض الأنشطة الأخيرة', () => {
      // التحقق من قائمة الأنشطة
      cy.get('[data-testid="recent-activities"]').should('be.visible')
      cy.shouldContainArabicText('الأنشطة الأخيرة')
      
      // التحقق من وجود عناصر النشاط
      cy.get('[data-testid="activity-item"]').should('have.length.at.least', 1)
      cy.get('[data-testid="activity-type"]').should('be.visible')
      cy.get('[data-testid="activity-description"]').should('be.visible')
      cy.get('[data-testid="activity-time"]').should('be.visible')
    })

    it('يجب أن تعرض التنبيهات المهمة', () => {
      // التحقق من قسم التنبيهات
      cy.get('[data-testid="admin-alerts"]').should('be.visible')
      cy.shouldContainArabicText('التنبيهات')
      
      // التحقق من أنواع التنبيهات
      cy.get('[data-testid="pending-approvals"]').should('be.visible')
      cy.get('[data-testid="system-issues"]').should('be.visible')
      cy.get('[data-testid="security-alerts"]').should('be.visible')
    })
  })

  describe('👥 إدارة المستخدمين', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة إدارة المستخدمين
      cy.visitWithLocale('/admin/users')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض قائمة المستخدمين', () => {
      // التحقق من وجود جدول المستخدمين
      cy.get('[data-testid="users-table"]').should('be.visible')
      
      // التحقق من أعمدة الجدول
      cy.shouldContainArabicText('الاسم')
      cy.shouldContainArabicText('البريد الإلكتروني')
      cy.shouldContainArabicText('نوع المستخدم')
      cy.shouldContainArabicText('تاريخ التسجيل')
      cy.shouldContainArabicText('الحالة')
      cy.shouldContainArabicText('الإجراءات')
    })

    it('يجب أن يبحث في المستخدمين', () => {
      // البحث بالاسم
      cy.get('[data-testid="user-search"]').type('أحمد')
      cy.get('[data-testid="search-btn"]').click()
      
      // التحقق من نتائج البحث
      cy.get('[data-testid="user-row"]').should('contain.text', 'أحمد')
      
      // مسح البحث
      cy.get('[data-testid="clear-search"]').click()
      cy.get('[data-testid="user-row"]').should('have.length.at.least', 1)
    })

    it('يجب أن يفلتر المستخدمين حسب النوع', () => {
      // فلترة حسب نوع المستخدم
      cy.get('[data-testid="user-type-filter"]').select('merchant')
      
      // التحقق من الفلترة
      cy.get('[data-testid="user-row"]').each(($row) => {
        cy.wrap($row).find('[data-testid="user-type"]')
          .should('contain.text', 'تاجر')
      })
    })

    it('يجب أن يعرض تفاصيل المستخدم', () => {
      // النقر على مستخدم
      cy.get('[data-testid="user-row"]').first().click()
      
      // التحقق من فتح صفحة تفاصيل المستخدم
      cy.get('[data-testid="user-profile"]').should('be.visible')
      cy.shouldContainArabicText('ملف المستخدم')
      
      // التحقق من وجود المعلومات
      cy.get('[data-testid="user-info"]').should('be.visible')
      cy.get('[data-testid="user-activity"]').should('be.visible')
      cy.get('[data-testid="user-orders"]').should('be.visible')
    })

    it('يجب أن يحظر/يلغي حظر مستخدم', () => {
      // حظر المستخدم
      cy.get('[data-testid="user-row"]').first().within(() => {
        cy.get('[data-testid="ban-user"]').click()
      })
      
      // تأكيد الحظر
      cy.get('[data-testid="ban-reason"]').type('انتهاك شروط الاستخدام')
      cy.get('[data-testid="confirm-ban"]').click()
      
      // التحقق من تغيير الحالة
      cy.get('[data-testid="user-status"]').should('contain.text', 'محظور')
      
      // إلغاء الحظر
      cy.get('[data-testid="user-row"]').first().within(() => {
        cy.get('[data-testid="unban-user"]').click()
      })
      
      // تأكيد إلغاء الحظر
      cy.get('[data-testid="confirm-unban"]').click()
      
      // التحقق من تغيير الحالة
      cy.get('[data-testid="user-status"]').should('contain.text', 'نشط')
    })

    it('يجب أن يحذف مستخدم', () => {
      // حذف المستخدم
      cy.get('[data-testid="user-row"]').first().within(() => {
        cy.get('[data-testid="delete-user"]').click()
      })
      
      // تأكيد الحذف
      cy.get('[data-testid="confirm-delete"]').should('be.visible')
      cy.shouldContainArabicText('هل أنت متأكد من حذف هذا المستخدم؟')
      cy.get('[data-testid="delete-reason"]').type('طلب من المستخدم')
      cy.get('[data-testid="confirm-delete-btn"]').click()
      
      // التحقق من نجاح الحذف
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم حذف المستخدم بنجاح')
    })
  })

  describe('🏪 إدارة التجار', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة موافقات التجار
      cy.visitWithLocale('/admin/merchant-approvals')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض طلبات موافقة التجار', () => {
      // التحقق من وجود جدول الطلبات
      cy.get('[data-testid="merchant-approvals-table"]').should('be.visible')
      
      // التحقق من أعمدة الجدول
      cy.shouldContainArabicText('اسم التاجر')
      cy.shouldContainArabicText('اسم المتجر')
      cy.shouldContainArabicText('تاريخ الطلب')
      cy.shouldContainArabicText('الحالة')
      cy.shouldContainArabicText('الإجراءات')
    })

    it('يجب أن يعرض تفاصيل طلب التاجر', () => {
      // عرض تفاصيل الطلب
      cy.get('[data-testid="merchant-request-row"]').first().within(() => {
        cy.get('[data-testid="view-details"]').click()
      })
      
      // التحقق من فتح نافذة التفاصيل
      cy.get('[data-testid="merchant-details-modal"]').should('be.visible')
      cy.shouldContainArabicText('تفاصيل طلب التاجر')
      
      // التحقق من وجود المعلومات
      cy.get('[data-testid="merchant-info"]').should('be.visible')
      cy.get('[data-testid="store-info"]').should('be.visible')
      cy.get('[data-testid="documents"]').should('be.visible')
    })

    it('يجب أن يوافق على طلب تاجر', () => {
      // الموافقة على الطلب
      cy.get('[data-testid="merchant-request-row"]').first().within(() => {
        cy.get('[data-testid="approve-merchant"]').click()
      })
      
      // تأكيد الموافقة
      cy.get('[data-testid="approval-notes"]').type('تم مراجعة الطلب والموافقة عليه')
      cy.get('[data-testid="confirm-approval"]').click()
      
      // التحقق من نجاح الموافقة
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم قبول طلب التاجر بنجاح')
    })

    it('يجب أن يرفض طلب تاجر', () => {
      // رفض الطلب
      cy.get('[data-testid="merchant-request-row"]').first().within(() => {
        cy.get('[data-testid="reject-merchant"]').click()
      })
      
      // تأكيد الرفض
      cy.get('[data-testid="rejection-reason"]').type('الوثائق المقدمة غير مكتملة')
      cy.get('[data-testid="confirm-rejection"]').click()
      
      // التحقق من نجاح الرفض
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم رفض طلب التاجر')
    })
  })

  describe('📂 إدارة الفئات', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة إدارة الفئات
      cy.visitWithLocale('/admin/categories')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض قائمة الفئات', () => {
      // التحقق من وجود جدول الفئات
      cy.get('[data-testid="categories-table"]').should('be.visible')
      
      // التحقق من أعمدة الجدول
      cy.shouldContainArabicText('اسم الفئة')
      cy.shouldContainArabicText('الوصف')
      cy.shouldContainArabicText('عدد المنتجات')
      cy.shouldContainArabicText('الحالة')
      cy.shouldContainArabicText('الإجراءات')
    })

    it('يجب أن يضيف فئة جديدة', () => {
      // إضافة فئة جديدة
      cy.get('[data-testid="add-category"]').click()
      
      // ملء نموذج الفئة
      cy.fillForm({
        'category-name-ar': 'الأجهزة الذكية',
        'category-name-en': 'Smart Devices',
        'category-description-ar': 'فئة للأجهزة الذكية والتقنية',
        'category-description-en': 'Category for smart and tech devices'
      })
      
      // رفع أيقونة الفئة
      cy.get('[data-testid="category-icon"]').selectFile('cypress/fixtures/category-icon.png')
      
      // حفظ الفئة
      cy.get('[data-testid="save-category"]').click()
      
      // التحقق من نجاح الإضافة
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة الفئة بنجاح')
    })

    it('يجب أن يحرر فئة موجودة', () => {
      // فتح نموذج التحرير
      cy.get('[data-testid="category-row"]').first().within(() => {
        cy.get('[data-testid="edit-category"]').click()
      })
      
      // تعديل الوصف
      cy.get('[data-testid="category-description-ar"]')
        .clear()
        .type('وصف محدث للفئة')
      
      // حفظ التغييرات
      cy.get('[data-testid="save-changes"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث الفئة بنجاح')
    })

    it('يجب أن يحذف فئة', () => {
      // حذف الفئة
      cy.get('[data-testid="category-row"]').first().within(() => {
        cy.get('[data-testid="delete-category"]').click()
      })
      
      // تأكيد الحذف
      cy.get('[data-testid="confirm-delete"]').should('be.visible')
      cy.shouldContainArabicText('هل أنت متأكد من حذف هذه الفئة؟')
      cy.get('[data-testid="confirm-delete-btn"]').click()
      
      // التحقق من نجاح الحذف
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم حذف الفئة بنجاح')
    })
  })

  describe('⚙️ إعدادات النظام', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة إعدادات النظام
      cy.visitWithLocale('/admin/settings')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض إعدادات النظام', () => {
      // التحقق من وجود إعدادات النظام
      cy.get('[data-testid="system-settings"]').should('be.visible')
      cy.shouldContainArabicText('إعدادات النظام')
      
      // التحقق من الأقسام المختلفة
      cy.get('[data-testid="general-settings"]').should('be.visible')
      cy.get('[data-testid="payment-settings"]').should('be.visible')
      cy.get('[data-testid="notification-settings"]').should('be.visible')
      cy.get('[data-testid="security-settings"]').should('be.visible')
    })

    it('يجب أن يحدث الإعدادات العامة', () => {
      // تحديث الإعدادات العامة
      cy.fillForm({
        'site-name': 'مِخْلاة - منصة التجارة الإلكترونية',
        'site-description': 'منصة شاملة للتجارة الإلكترونية في المملكة العربية السعودية',
        'contact-email': '<EMAIL>',
        'support-phone': '+966500000000'
      })
      
      // حفظ التغييرات
      cy.get('[data-testid="save-general-settings"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث الإعدادات العامة بنجاح')
    })

    it('يجب أن يحدث إعدادات الدفع', () => {
      // الانتقال إلى إعدادات الدفع
      cy.get('[data-testid="payment-settings-tab"]').click()
      
      // تحديث إعدادات الدفع
      cy.get('[data-testid="enable-credit-cards"]').check()
      cy.get('[data-testid="enable-bank-transfer"]').check()
      cy.get('[data-testid="enable-cod"]').check()
      
      // تحديث رسوم المعاملات
      cy.fillForm({
        'transaction-fee': '2.5',
        'minimum-order': '50',
        'maximum-order': '10000'
      })
      
      // حفظ التغييرات
      cy.get('[data-testid="save-payment-settings"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث إعدادات الدفع بنجاح')
    })

    it('يجب أن يحدث إعدادات الأمان', () => {
      // الانتقال إلى إعدادات الأمان
      cy.get('[data-testid="security-settings-tab"]').click()
      
      // تحديث إعدادات الأمان
      cy.get('[data-testid="enable-2fa"]').check()
      cy.get('[data-testid="enable-login-alerts"]').check()
      cy.get('[data-testid="enable-suspicious-activity-detection"]').check()
      
      // تحديث سياسات كلمة المرور
      cy.fillForm({
        'min-password-length': '8',
        'password-expiry-days': '90',
        'max-login-attempts': '5'
      })
      
      // حفظ التغييرات
      cy.get('[data-testid="save-security-settings"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث إعدادات الأمان بنجاح')
    })
  })

  describe('📊 تقارير النظام', () => {
    it('يجب أن يعرض تقارير شاملة للنظام', () => {
      // الانتقال إلى صفحة التقارير
      cy.get('[data-testid="system-reports"]').click()
      
      // التحقق من وجود أنواع التقارير المختلفة
      cy.get('[data-testid="users-report"]').should('be.visible')
      cy.get('[data-testid="merchants-report"]').should('be.visible')
      cy.get('[data-testid="orders-report"]').should('be.visible')
      cy.get('[data-testid="revenue-report"]').should('be.visible')
      cy.get('[data-testid="system-performance-report"]').should('be.visible')
    })

    it('يجب أن يصدر تقرير شامل للنظام', () => {
      cy.get('[data-testid="system-reports"]').click()
      
      // تصدير التقرير
      cy.get('[data-testid="export-system-report"]').click()
      
      // اختيار نوع التصدير والفترة
      cy.get('[data-testid="report-period"]').select('last_month')
      cy.get('[data-testid="export-format"]').select('excel')
      cy.get('[data-testid="confirm-export"]').click()
      
      // التحقق من بدء التصدير
      cy.get('[data-testid="export-status"]').should('contain.text', 'جاري التصدير')
    })
  })

  describe('🔔 إدارة الإشعارات', () => {
    it('يجب أن يرسل إشعار عام للمستخدمين', () => {
      // فتح نموذج الإشعار العام
      cy.get('[data-testid="send-notification"]').click()
      
      // ملء نموذج الإشعار
      cy.fillForm({
        'notification-title': 'تحديث مهم في النظام',
        'notification-message': 'سيتم إجراء صيانة دورية على النظام غداً من الساعة 2-4 صباحاً',
        'notification-type': 'system_update'
      })
      
      // اختيار المستقبلين
      cy.get('[data-testid="target-audience"]').select('all_users')
      
      // إرسال الإشعار
      cy.get('[data-testid="send-notification-btn"]').click()
      
      // التحقق من نجاح الإرسال
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إرسال الإشعار بنجاح')
    })
  })
})
