// cypress/e2e/signup-redirect-flow.cy.ts
// اختبار تدفق التسجيل وإعادة التوجيه المحسن

describe('تدفق التسجيل وإعادة التوجيه المحسن', () => {
  beforeEach(() => {
    // مسح التخزين المحلي قبل كل اختبار
    cy.clearLocalStorage()
    cy.clearCookies()
    
    // زيارة الصفحة الرئيسية
    cy.visit('http://localhost:9002/ar')
    cy.wait(2000)
  })

  it('يجب أن يوجه العملاء الجدد مباشرة إلى لوحة التحكم دون عرض صفحة وسطية', () => {
    cy.log('🧪 اختبار تدفق التسجيل للعملاء')
    
    // الانتقال لصفحة اختيار نوع المستخدم
    cy.visit('http://localhost:9002/ar/user-type-selection')
    cy.wait(3000)
    
    // اختيار نوع العميل
    cy.get('[data-testid="customer-card"], button:contains("عميل")').first().click()
    cy.wait(2000)
    
    // التحقق من الوصول لصفحة التسجيل
    cy.url().should('include', '/signup')
    
    // ملء نموذج التسجيل
    const testEmail = `test-customer-${Date.now()}@example.com`
    
    cy.get('input[name="email"], [data-cy="email"]').type(testEmail)
    cy.get('input[name="username"], [data-cy="username"]').type('عميل تجريبي')
    cy.get('input[name="password"], [data-cy="password"]').type('Password123!')
    cy.get('input[name="confirmPassword"], [data-cy="confirm-password"]').type('Password123!')
    
    // محاكاة نجاح التسجيل
    cy.intercept('POST', '**/auth/signup', {
      statusCode: 200,
      body: { success: true, userType: 'customer' }
    }).as('signupRequest')
    
    // إرسال النموذج
    cy.get('button[type="submit"], [data-cy="submit-signup"]').click()
    
    // التحقق من عدم ظهور صفحة "أنت مسجل دخول بالفعل"
    cy.get('body').should('not.contain', 'أنت مسجل دخول بالفعل')
    
    // التحقق من التوجيه المباشر للوحة التحكم
    cy.url({ timeout: 10000 }).should('include', '/dashboard')
    
    cy.log('✅ تم توجيه العميل مباشرة للوحة التحكم')
  })

  it('يجب أن يوجه التجار الجدد مباشرة إلى صفحة انتظار الموافقة', () => {
    cy.log('🧪 اختبار تدفق التسجيل للتجار')
    
    // الانتقال لصفحة اختيار نوع المستخدم
    cy.visit('http://localhost:9002/ar/user-type-selection')
    cy.wait(3000)
    
    // اختيار نوع التاجر
    cy.get('[data-testid="merchant-card"], button:contains("تاجر")').first().click()
    cy.wait(2000)
    
    // التحقق من الوصول لصفحة التسجيل
    cy.url().should('include', '/signup')
    
    // ملء نموذج التسجيل الأساسي
    const testEmail = `test-merchant-${Date.now()}@example.com`
    
    cy.get('input[name="email"], [data-cy="email"]').type(testEmail)
    cy.get('input[name="username"], [data-cy="username"]').type('تاجر تجريبي')
    cy.get('input[name="password"], [data-cy="password"]').type('Password123!')
    cy.get('input[name="confirmPassword"], [data-cy="confirm-password"]').type('Password123!')
    
    // محاكاة نجاح التسجيل
    cy.intercept('POST', '**/auth/signup', {
      statusCode: 200,
      body: { success: true, userType: 'merchant' }
    }).as('merchantSignupRequest')
    
    // إرسال النموذج
    cy.get('button[type="submit"], [data-cy="submit-signup"]').click()
    
    // التحقق من عدم ظهور صفحة "أنت مسجل دخول بالفعل"
    cy.get('body').should('not.contain', 'أنت مسجل دخول بالفعل')
    
    // التحقق من التوجيه المباشر لصفحة انتظار الموافقة
    cy.url({ timeout: 10000 }).should('include', '/merchant/pending-approval')
    
    cy.log('✅ تم توجيه التاجر مباشرة لصفحة انتظار الموافقة')
  })

  it('يجب أن يعرض رسالة "أنت مسجل دخول بالفعل" فقط للمستخدمين الموجودين', () => {
    cy.log('🧪 اختبار عرض الرسالة للمستخدمين الموجودين')
    
    // محاكاة مستخدم مسجل دخول مسبقاً
    cy.window().then((win) => {
      win.localStorage.setItem('auth-user', JSON.stringify({
        uid: 'existing-user-123',
        email: '<EMAIL>',
        userType: 'customer'
      }))
    })
    
    // زيارة صفحة التسجيل
    cy.visit('http://localhost:9002/ar/signup')
    cy.wait(3000)
    
    // التحقق من ظهور رسالة "أنت مسجل دخول بالفعل"
    cy.get('body').should('contain', 'أنت مسجل دخول بالفعل')
    
    // التحقق من التوجيه للوحة التحكم
    cy.url({ timeout: 10000 }).should('include', '/dashboard')
    
    cy.log('✅ تم عرض الرسالة المناسبة للمستخدم الموجود')
  })

  it('يجب أن يتعامل مع أخطاء التوجيه بشكل صحيح', () => {
    cy.log('🧪 اختبار معالجة أخطاء التوجيه')
    
    // محاكاة خطأ في تحديد مسار التوجيه
    cy.intercept('GET', '**/users/**', {
      statusCode: 404,
      body: { error: 'User not found' }
    }).as('userDataError')
    
    // محاكاة مستخدم مسجل دخول
    cy.window().then((win) => {
      win.localStorage.setItem('auth-user', JSON.stringify({
        uid: 'error-user-123',
        email: '<EMAIL>',
        userType: 'customer'
      }))
    })
    
    // زيارة صفحة التسجيل
    cy.visit('http://localhost:9002/ar/signup')
    cy.wait(5000)
    
    // التحقق من التوجيه للوحة التحكم كـ fallback
    cy.url({ timeout: 15000 }).should('include', '/dashboard')
    
    cy.log('✅ تم التعامل مع خطأ التوجيه بشكل صحيح')
  })
})
