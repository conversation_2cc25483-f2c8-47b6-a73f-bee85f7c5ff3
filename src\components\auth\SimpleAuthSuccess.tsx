// src/components/auth/SimpleAuthSuccess.tsx
"use client";

import { useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import type { Locale } from '@/lib/i18n';

interface SimpleAuthSuccessProps {
  locale: Locale;
  userType: string;
}

export default function SimpleAuthSuccess({ locale, userType }: SimpleAuthSuccessProps) {
  
  useEffect(() => {
    console.log('🚀 SimpleAuthSuccess: Starting redirect');
    console.log('📍 UserType:', userType);
    console.log('🌐 Locale:', locale);
    
    // تحديد مسار التوجيه
    let targetPath = `/${locale}/dashboard`;
    
    if (userType === 'merchant') {
      targetPath = `/${locale}/merchant/pending-approval`;
    } else if (userType === 'representative') {
      targetPath = `/${locale}/representative/signup`;
    }
    
    console.log('🎯 Target path:', targetPath);
    
    // التوجيه الفوري
    const timer = setTimeout(() => {
      console.log('🔄 Redirecting now...');
      window.location.href = targetPath;
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [locale, userType]);

  const handleManualRedirect = () => {
    let targetPath = `/${locale}/dashboard`;
    
    if (userType === 'merchant') {
      targetPath = `/${locale}/merchant/pending-approval`;
    } else if (userType === 'representative') {
      targetPath = `/${locale}/representative/signup`;
    }
    
    window.location.href = targetPath;
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
      <div className="flex flex-col items-center justify-center space-y-6 text-center max-w-md">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
        
        <div className="space-y-2">
          <h2 className="text-xl font-semibold text-foreground">
            تم إنشاء حسابك بنجاح!
          </h2>
          <p className="text-muted-foreground">
            جاري توجيهك للصفحة المناسبة...
          </p>
        </div>
        
        <div className="text-sm text-muted-foreground space-y-1">
          <div className="text-green-600">✅ تم تسجيل الدخول بنجاح</div>
          <div className="text-blue-600">
            👤 نوع الحساب: {userType === 'merchant' ? 'تاجر' : userType === 'representative' ? 'مندوب' : 'عميل'}
          </div>
        </div>

        <div className="flex flex-col space-y-2 w-full">
          <button
            onClick={handleManualRedirect}
            className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            الانتقال الآن
          </button>

          <button
            onClick={() => window.location.href = `/${locale}/login`}
            className="px-6 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors text-sm"
          >
            تسجيل الدخول مرة أخرى
          </button>
        </div>
      </div>
    </div>
  );
}
