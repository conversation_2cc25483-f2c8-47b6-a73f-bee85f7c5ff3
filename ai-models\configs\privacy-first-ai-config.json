{"version": "3.0.0", "mode": "privacy-first-local", "description": "نظام ذكاء اصطناعي محلي بالكامل - خصوصية 100%", "privacy_policy": {"data_processing": "local_only", "network_requests": "none", "data_retention": "session_only", "encryption": "client_side_only", "compliance": ["GDPR", "Saudi_Data_Protection_Law", "CCPA"]}, "providers": {"browser_local": {"enabled": true, "priority": 1, "description": "معالجة محلية بالكامل في المتصفح", "privacy_level": "maximum", "models": {"tesseract_ocr": {"capabilities": ["ocr", "text-extraction", "arabic-text"], "engine": "tesseract.js", "version": "5.1.1", "languages": ["ara", "eng"], "accuracy": "85-92%", "speed": "fast", "privacy": {"data_leaves_browser": false, "network_required": false, "local_processing": true}}, "compromise_nlp": {"capabilities": ["text-analysis", "entity-extraction", "arabic-ner"], "engine": "compromise.js + arabic-nlp", "version": "14.x", "languages": ["ar", "en"], "accuracy": "80-88%", "privacy": {"data_leaves_browser": false, "network_required": false, "local_processing": true}}, "regex_validator": {"capabilities": ["document-validation", "pattern-matching", "format-check"], "engine": "custom-regex + rules", "accuracy": "95%+", "speed": "very_fast", "privacy": {"data_leaves_browser": false, "network_required": false, "local_processing": true}}, "ml5_classifier": {"capabilities": ["document-classification", "image-analysis"], "engine": "ml5.js + tensorflow.js", "model_size": "5-10MB", "accuracy": "88-93%", "privacy": {"data_leaves_browser": false, "network_required": false, "local_processing": true}}}}, "local_server": {"enabled": false, "priority": 2, "description": "خادم محلي مخصص - للمؤسسات", "privacy_level": "high", "models": {"ollama_aya": {"capabilities": ["text-analysis", "document-processing", "arabic-understanding"], "model": "aya:8b-instruct", "size": "4.7GB", "accuracy": "92-96%", "endpoint": "http://localhost:11434", "privacy": {"data_leaves_network": false, "local_server_only": true, "no_cloud_connection": true}}, "ollama_qwen": {"capabilities": ["reasoning", "complex-analysis", "multilingual"], "model": "qwen2.5:7b-instruct-q4_0", "size": "4.2GB", "accuracy": "94-97%", "endpoint": "http://localhost:11434", "privacy": {"data_leaves_network": false, "local_server_only": true, "no_cloud_connection": true}}}}, "hybrid_secure": {"enabled": false, "priority": 3, "description": "نظام هجين مع تشفير متقدم", "privacy_level": "medium", "encryption": {"algorithm": "AES-256-GCM", "key_management": "local_only", "data_anonymization": true}, "models": {"encrypted_gemini": {"capabilities": ["advanced-analysis", "complex-reasoning"], "provider": "google", "model": "gemini-2.0-flash", "privacy": {"data_encrypted": true, "anonymized": true, "no_personal_data": true, "audit_trail": true}}}}}, "processing": {"textAnalysis": {"provider": "browser_local", "model": "compromise_nlp", "confidence": 0.8, "fallback": "regex_patterns", "features": ["arabic-entity-extraction", "name-recognition", "date-extraction", "number-extraction"]}, "ocr": {"provider": "browser_local", "model": "tesseract_ocr", "confidence": 0.85, "languages": ["ara", "eng"], "preprocessing": {"image_enhancement": true, "noise_reduction": true, "contrast_adjustment": true}, "features": ["arabic-text-extraction", "layout-analysis", "table-detection"]}, "validation": {"provider": "browser_local", "model": "regex_validator", "confidence": 0.95, "features": ["format-validation", "pattern-matching", "consistency-check", "fraud-detection-basic"]}, "classification": {"provider": "browser_local", "model": "ml5_classifier", "confidence": 0.88, "features": ["document-type-detection", "layout-classification", "quality-assessment"]}}, "documentTemplates": {"commercial_registration": {"requiredFields": ["businessName", "ownerName", "registrationNumber", "issueDate", "expiryDate", "businessActivity"], "validationRules": {"registrationNumber": {"pattern": "^\\d{10}$", "description": "رقم السجل التجاري 10 أرقام"}, "businessName": {"minLength": 3, "maxLength": 100, "pattern": "^[\\u0600-\\u06FF\\s\\w\\-\\.]+$"}}, "extractionPatterns": {"businessName": ["اسم المنشأة[:\\s]*([^\\n]+)", "اسم الشركة[:\\s]*([^\\n]+)", "المنشأة[:\\s]*([^\\n]+)"], "ownerName": ["اسم التاجر[:\\s]*([^\\n]+)", "اسم المالك[:\\s]*([^\\n]+)", "صاحب المنشأة[:\\s]*([^\\n]+)"], "registrationNumber": ["رقم السجل[:\\s]*(\\d{10})", "السجل التجاري[:\\s]*(\\d{10})"]}}, "freelance_document": {"requiredFields": ["ownerName", "documentNumber", "issueDate", "expiryDate", "activityType"], "validationRules": {"documentNumber": {"pattern": "^[A-Z0-9]{8,12}$", "description": "رقم وثيقة العمل الحر"}}, "extractionPatterns": {"ownerName": ["اسم صاحب الوثيقة[:\\s]*([^\\n]+)", "اسم المستقل[:\\s]*([^\\n]+)"], "documentNumber": ["رقم الوثيقة[:\\s]*([A-Z0-9]{8,12})", "رقم العمل الحر[:\\s]*([A-Z0-9]{8,12})"]}}, "driving_license": {"requiredFields": ["<PERSON><PERSON><PERSON>", "licenseNumber", "issueDate", "expiryDate", "licenseClass"], "validationRules": {"licenseNumber": {"pattern": "^\\d{10}$", "description": "رقم رخصة القيادة 10 أرقام"}}, "extractionPatterns": {"holderName": ["اسم حامل الرخصة[:\\s]*([^\\n]+)", "الاسم[:\\s]*([^\\n]+)"], "licenseNumber": ["رقم الرخصة[:\\s]*(\\d{10})", "رقم رخصة القيادة[:\\s]*(\\d{10})"]}}}, "arabicProcessing": {"normalization": {"removeHarakat": true, "normalizeAlef": true, "normalizeTeh": true, "normalizeYeh": true}, "commonWords": ["اسم", "رقم", "تاريخ", "مكان", "شركة", "مؤسسة", "سجل", "تجاري", "هوية", "رخصة", "قيادة", "فحص"], "entities": {"PERSON": ["اسم", "صاحب", "مالك", "مدير", "<PERSON>ا<PERSON><PERSON>"], "ORG": ["شركة", "مؤسسة", "منشأة", "مكتب", "<PERSON><PERSON><PERSON>"], "LOC": ["مدينة", "منطقة", "حي", "شارع", "عنوان"], "DATE": ["تاريخ", "يوم", "شهر", "سنة", "إصدار", "انتهاء"]}, "abbreviations": {"ش.م.م": "شركة مساهمة مقفلة", "ذ.م.م": "ذات مسؤولية محدودة", "م.ت": "مؤسسة تجارية", "ت.ب": "تاجر بسيط"}}, "security": {"dataEncryption": true, "localProcessing": true, "dataRetention": "session_only", "privacyMode": "strict", "auditLogging": true, "networkIsolation": true, "memoryClearing": true}, "performance": {"caching": {"enabled": true, "ttl": 1800, "maxSize": "10MB", "clearOnExit": true}, "batching": {"enabled": false, "note": "معطل للحفاظ على الخصوصية"}, "retries": {"maxAttempts": 2, "backoffMs": 500}, "optimization": {"lazyLoading": true, "memoryManagement": true, "garbageCollection": true}}}