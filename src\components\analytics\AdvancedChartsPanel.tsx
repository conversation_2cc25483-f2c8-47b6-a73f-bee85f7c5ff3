"use client";

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ComposedChart
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Pie<PERSON>hart as PieChartIcon,
  Activity,
  Users,
  ShoppingCart,
  DollarSign,
  Calendar,
  Download,
  RefreshCw
} from 'lucide-react';
import type { SalesReport } from '@/services/advancedReportsService';

interface AdvancedChartsPanelProps {
  salesReport?: SalesReport;
  loading?: boolean;
  onRefresh?: () => void;
  onExport?: (format: 'pdf' | 'excel' | 'csv') => void;
  className?: string;
}

const COLORS = [
  '#0088FE', '#00C49F', '#FFBB28', '#FF8042', 
  '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C'
];

export default function AdvancedChartsPanel({
  salesReport,
  loading = false,
  onRefresh,
  onExport,
  className
}: AdvancedChartsPanelProps) {
  const [selectedChart, setSelectedChart] = useState<'line' | 'area' | 'bar' | 'pie'>('line');
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');

  // تحضير بيانات الرسوم البيانية
  const prepareChartData = () => {
    if (!salesReport) return [];

    return salesReport.dailyBreakdown.map(day => ({
      date: new Date(day.date).toLocaleDateString('ar-SA', { 
        month: 'short', 
        day: 'numeric' 
      }),
      revenue: day.revenue,
      orders: day.orders,
      customers: day.customers,
      averageOrderValue: day.averageOrderValue
    }));
  };

  // تحضير بيانات أداء المنتجات
  const prepareProductData = () => {
    if (!salesReport) return [];

    return salesReport.productPerformance.slice(0, 10).map(product => ({
      name: product.productName.length > 15 
        ? product.productName.substring(0, 15) + '...' 
        : product.productName,
      revenue: product.totalRevenue,
      sales: product.totalSales,
      rating: product.averageRating
    }));
  };

  // تحضير بيانات تجميع العملاء
  const prepareCustomerSegmentData = () => {
    if (!salesReport) return [];

    return salesReport.customerAnalytics.customerSegments.map(segment => ({
      name: segment.segment === 'high_value' ? 'عملاء مميزون' :
            segment.segment === 'regular' ? 'عملاء عاديون' :
            segment.segment === 'new' ? 'عملاء جدد' : 'عملاء معرضون للخطر',
      value: segment.count,
      percentage: segment.percentage
    }));
  };

  const chartData = prepareChartData();
  const productData = prepareProductData();
  const customerSegmentData = prepareCustomerSegmentData();

  // رسم الخط
  const renderLineChart = () => (
    <ResponsiveContainer width="100%" height={400}>
      <LineChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" />
        <YAxis />
        <Tooltip 
          formatter={(value, name) => [
            typeof value === 'number' ? value.toLocaleString() : value,
            name === 'revenue' ? 'الإيرادات' :
            name === 'orders' ? 'الطلبات' :
            name === 'customers' ? 'العملاء' : name
          ]}
        />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="revenue" 
          stroke={COLORS[0]} 
          strokeWidth={3}
          name="الإيرادات"
          dot={{ fill: COLORS[0], strokeWidth: 2, r: 4 }}
        />
        <Line 
          type="monotone" 
          dataKey="orders" 
          stroke={COLORS[1]} 
          strokeWidth={2}
          name="الطلبات"
          dot={{ fill: COLORS[1], strokeWidth: 2, r: 3 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  // رسم المساحة
  const renderAreaChart = () => (
    <ResponsiveContainer width="100%" height={400}>
      <AreaChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" />
        <YAxis />
        <Tooltip formatter={(value) => [value?.toLocaleString(), 'القيمة']} />
        <Legend />
        <Area 
          type="monotone" 
          dataKey="revenue" 
          stackId="1"
          stroke={COLORS[0]} 
          fill={COLORS[0]}
          fillOpacity={0.6}
          name="الإيرادات"
        />
      </AreaChart>
    </ResponsiveContainer>
  );

  // رسم الأعمدة
  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height={400}>
      <BarChart data={productData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip formatter={(value) => [value?.toLocaleString(), 'القيمة']} />
        <Legend />
        <Bar dataKey="revenue" fill={COLORS[0]} name="الإيرادات" />
        <Bar dataKey="sales" fill={COLORS[1]} name="المبيعات" />
      </BarChart>
    </ResponsiveContainer>
  );

  // رسم دائري
  const renderPieChart = () => (
    <ResponsiveContainer width="100%" height={400}>
      <PieChart>
        <Pie
          data={customerSegmentData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percentage }) => `${name} ${percentage?.toFixed(1)}%`}
          outerRadius={120}
          fill="#8884d8"
          dataKey="value"
        >
          {customerSegmentData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip formatter={(value) => [value?.toLocaleString(), 'العدد']} />
      </PieChart>
    </ResponsiveContainer>
  );

  const renderChart = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-96">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      );
    }

    switch (selectedChart) {
      case 'area':
        return renderAreaChart();
      case 'bar':
        return renderBarChart();
      case 'pie':
        return renderPieChart();
      default:
        return renderLineChart();
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* أدوات التحكم */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">الرسوم البيانية التحليلية</h2>
        
        <div className="flex items-center gap-2">
          <Select value={selectedChart} onValueChange={(value: any) => setSelectedChart(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="line">خطي</SelectItem>
              <SelectItem value="area">مساحة</SelectItem>
              <SelectItem value="bar">أعمدة</SelectItem>
              <SelectItem value="pie">دائري</SelectItem>
            </SelectContent>
          </Select>

          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 أيام</SelectItem>
              <SelectItem value="30d">30 يوم</SelectItem>
              <SelectItem value="90d">90 يوم</SelectItem>
            </SelectContent>
          </Select>

          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          )}

          {onExport && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onExport('pdf')}
            >
              <Download className="w-4 h-4 mr-1" />
              تصدير
            </Button>
          )}
        </div>
      </div>

      {/* الرسم البياني الرئيسي */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            {selectedChart === 'line' && 'اتجاهات المبيعات'}
            {selectedChart === 'area' && 'نمو الإيرادات'}
            {selectedChart === 'bar' && 'أداء المنتجات'}
            {selectedChart === 'pie' && 'تجميع العملاء'}
          </CardTitle>
          <CardDescription>
            تحليل بصري للبيانات التجارية
          </CardDescription>
        </CardHeader>
        <CardContent>
          {renderChart()}
        </CardContent>
      </Card>

      {/* رسوم بيانية إضافية */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* رسم مقارن */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              مقارنة الأداء
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <ComposedChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="orders" fill={COLORS[1]} name="الطلبات" />
                <Line type="monotone" dataKey="revenue" stroke={COLORS[0]} name="الإيرادات" />
              </ComposedChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* إحصائيات سريعة */}
        <Card>
          <CardHeader>
            <CardTitle>الإحصائيات السريعة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {salesReport && (
              <>
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-5 h-5 text-blue-600" />
                    <span className="font-medium">إجمالي الإيرادات</span>
                  </div>
                  <span className="text-lg font-bold text-blue-600">
                    {salesReport.summary.totalRevenue.toLocaleString()} ريال
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <ShoppingCart className="w-5 h-5 text-green-600" />
                    <span className="font-medium">إجمالي الطلبات</span>
                  </div>
                  <span className="text-lg font-bold text-green-600">
                    {salesReport.summary.totalOrders.toLocaleString()}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Users className="w-5 h-5 text-purple-600" />
                    <span className="font-medium">إجمالي العملاء</span>
                  </div>
                  <span className="text-lg font-bold text-purple-600">
                    {salesReport.summary.totalCustomers.toLocaleString()}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-orange-600" />
                    <span className="font-medium">متوسط قيمة الطلب</span>
                  </div>
                  <span className="text-lg font-bold text-orange-600">
                    {salesReport.summary.averageOrderValue.toLocaleString()} ريال
                  </span>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
