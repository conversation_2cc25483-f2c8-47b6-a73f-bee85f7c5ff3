import type { Locale } from '@/lib/i18n';
import { getTranslations } from '@/context/locale-context';
import SignupForm from '@/components/auth/SignupForm';
import Link from 'next/link';
import { BrandLogo } from '@/components/Logo';
import SignupPageClient from '@/components/auth/SignupPageClient';
import QuickRedirect from '@/components/auth/QuickRedirect';

export default async function SignupPage({
  params,
  searchParams
}: {
  params: { locale: Locale };
  searchParams?: { userType?: string };
}) {
  const paramsData = await Promise.resolve(params);
  const searchParamsData = await Promise.resolve(searchParams);
  const locale = paramsData.locale;
  const { t } = await getTranslations(locale);

  // Check if userType is provided in search params
  const userTypeParam = searchParamsData?.userType;

  // If no userType is provided, redirect to user type selection
  if (!userTypeParam || !['customer', 'merchant', 'representative'].includes(userTypeParam)) {
    return (
      <div className="flex min-h-[calc(100vh-8rem)] items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8 text-center">
          <div>
            <Link href={`/${locale}`} className="inline-block mx-auto">
              <BrandLogo size="default" className="mx-auto" />
            </Link>
          </div>

          <div className="space-y-6">
            <h1 className="text-3xl font-bold text-primary">{t('selectUserType')}</h1>
            <p className="text-muted-foreground">{t('selectUserTypeSubtitle')}</p>

            <div className="space-y-4">
              <Link
                href={`/${locale}/user-type-selection`}
                className="inline-flex items-center justify-center w-full px-4 py-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground font-medium rounded-md transition-colors"
              >
                {t('chooseAccountType')}
              </Link>

              <p className="text-sm text-muted-foreground">
                {t('alreadyHaveAccount')}{' '}
                <Link href={`/${locale}/login`} className="font-medium text-primary hover:text-primary/80">
                  {t('login')}
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <SignupPageClient locale={locale}>
      <div className="flex min-h-[calc(100vh-8rem)] items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-4xl space-y-8">
          <div className="text-center">
            <Link href={`/${locale}`} className="inline-block mx-auto">
              <BrandLogo size="default" className="mx-auto" />
            </Link>
          </div>
          <SignupForm locale={locale} />
          <p className="mt-8 text-center text-sm text-muted-foreground">
            {t('alreadyHaveAccount')}{' '}
            <Link href={`/${locale}/login`} className="font-medium text-primary hover:text-primary/80">
              {t('login')}
            </Link>
          </p>
        </div>
      </div>
    </SignupPageClient>
  );
}
