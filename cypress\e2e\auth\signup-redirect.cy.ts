// cypress/e2e/auth/signup-redirect.cy.ts
describe('Signup Redirect Flow', () => {
  beforeEach(() => {
    // مسح البيانات المحلية قبل كل اختبار
    cy.clearLocalStorage();
    cy.clearCookies();
    
    // زيارة صفحة اختيار نوع المستخدم
    cy.visit('/ar/user-type-selection');
  });

  it('should redirect customer to dashboard after successful signup', () => {
    // اختيار نوع المستخدم: عميل
    cy.get('[data-testid="customer-card"]').click();
    cy.get('[data-testid="continue-button"]').click();

    // ملء نموذج التسجيل
    const testEmail = `test-customer-${Date.now()}@example.com`;
    cy.get('[data-testid="email-input"]').type(testEmail);
    cy.get('[data-testid="password-input"]').type('TestPassword123!');
    cy.get('[data-testid="confirm-password-input"]').type('TestPassword123!');
    cy.get('[data-testid="username-input"]').type('Test Customer');
    cy.get('[data-testid="phone-input"]').type('1234567890');
    
    // الموافقة على الشروط والأحكام
    cy.get('[data-testid="terms-checkbox"]').check();
    
    // إرسال النموذج
    cy.get('[data-testid="signup-button"]').click();

    // التحقق من التوجيه إلى صفحة نجاح المصادقة
    cy.url().should('include', '/auth-success');
    cy.url().should('include', 'userType=customer');

    // انتظار التوجيه التلقائي إلى لوحة التحكم
    cy.url({ timeout: 10000 }).should('include', '/dashboard');
    
    // التحقق من وجود عناصر لوحة التحكم
    cy.get('[data-testid="dashboard-header"]', { timeout: 10000 }).should('be.visible');
  });

  it('should redirect merchant to pending approval after successful signup', () => {
    // اختيار نوع المستخدم: تاجر
    cy.get('[data-testid="merchant-card"]').click();
    cy.get('[data-testid="continue-button"]').click();

    // ملء نموذج التسجيل
    const testEmail = `test-merchant-${Date.now()}@example.com`;
    cy.get('[data-testid="email-input"]').type(testEmail);
    cy.get('[data-testid="password-input"]').type('TestPassword123!');
    cy.get('[data-testid="confirm-password-input"]').type('TestPassword123!');
    cy.get('[data-testid="username-input"]').type('Test Merchant');
    cy.get('[data-testid="phone-input"]').type('1234567890');
    
    // الموافقة على الشروط والأحكام
    cy.get('[data-testid="terms-checkbox"]').check();
    
    // إرسال النموذج
    cy.get('[data-testid="signup-button"]').click();

    // التحقق من التوجيه إلى صفحة نجاح المصادقة
    cy.url().should('include', '/auth-success');
    cy.url().should('include', 'userType=merchant');

    // انتظار التوجيه التلقائي إلى صفحة انتظار الموافقة
    cy.url({ timeout: 10000 }).should('include', '/merchant/pending-approval');
    
    // التحقق من وجود رسالة انتظار الموافقة
    cy.contains('في انتظار الموافقة', { timeout: 10000 }).should('be.visible');
  });

  it('should redirect representative to signup completion after successful signup', () => {
    // اختيار نوع المستخدم: مندوب
    cy.get('[data-testid="representative-card"]').click();
    cy.get('[data-testid="continue-button"]').click();

    // ملء نموذج التسجيل
    const testEmail = `test-representative-${Date.now()}@example.com`;
    cy.get('[data-testid="email-input"]').type(testEmail);
    cy.get('[data-testid="password-input"]').type('TestPassword123!');
    cy.get('[data-testid="confirm-password-input"]').type('TestPassword123!');
    cy.get('[data-testid="username-input"]').type('Test Representative');
    cy.get('[data-testid="phone-input"]').type('1234567890');
    
    // الموافقة على الشروط والأحكام
    cy.get('[data-testid="terms-checkbox"]').check();
    
    // إرسال النموذج
    cy.get('[data-testid="signup-button"]').click();

    // التحقق من التوجيه إلى صفحة نجاح المصادقة
    cy.url().should('include', '/auth-success');
    cy.url().should('include', 'userType=representative');

    // انتظار التوجيه التلقائي إلى صفحة إكمال التسجيل
    cy.url({ timeout: 10000 }).should('include', '/representative/signup');
    
    // التحقق من وجود نموذج إكمال التسجيل
    cy.get('[data-testid="representative-signup-form"]', { timeout: 10000 }).should('be.visible');
  });

  it('should handle redirect failures gracefully', () => {
    // محاكاة فشل التوجيه عبر تعطيل JavaScript مؤقتاً
    cy.get('[data-testid="customer-card"]').click();
    cy.get('[data-testid="continue-button"]').click();

    const testEmail = `test-fallback-${Date.now()}@example.com`;
    cy.get('[data-testid="email-input"]').type(testEmail);
    cy.get('[data-testid="password-input"]').type('TestPassword123!');
    cy.get('[data-testid="confirm-password-input"]').type('TestPassword123!');
    cy.get('[data-testid="username-input"]').type('Test User');
    cy.get('[data-testid="phone-input"]').type('1234567890');
    cy.get('[data-testid="terms-checkbox"]').check();
    
    cy.get('[data-testid="signup-button"]').click();

    // التحقق من وجود زر التوجيه اليدوي في حالة الفشل
    cy.get('[data-testid="manual-redirect-button"]', { timeout: 15000 }).should('be.visible');
    
    // النقر على زر التوجيه اليدوي
    cy.get('[data-testid="manual-redirect-button"]').click();
    
    // التحقق من التوجيه النهائي
    cy.url({ timeout: 10000 }).should('include', '/dashboard');
  });

  it('should preserve user session during redirect', () => {
    cy.get('[data-testid="customer-card"]').click();
    cy.get('[data-testid="continue-button"]').click();

    const testEmail = `test-session-${Date.now()}@example.com`;
    cy.get('[data-testid="email-input"]').type(testEmail);
    cy.get('[data-testid="password-input"]').type('TestPassword123!');
    cy.get('[data-testid="confirm-password-input"]').type('TestPassword123!');
    cy.get('[data-testid="username-input"]').type('Test Session User');
    cy.get('[data-testid="phone-input"]').type('1234567890');
    cy.get('[data-testid="terms-checkbox"]').check();
    
    cy.get('[data-testid="signup-button"]').click();

    // انتظار التوجيه إلى لوحة التحكم
    cy.url({ timeout: 10000 }).should('include', '/dashboard');
    
    // التحقق من أن المستخدم ما زال مسجل دخول
    cy.get('[data-testid="user-menu"]', { timeout: 10000 }).should('be.visible');
    
    // إعادة تحميل الصفحة للتأكد من استمرار الجلسة
    cy.reload();
    
    // التحقق من أن المستخدم ما زال مسجل دخول بعد إعادة التحميل
    cy.get('[data-testid="user-menu"]', { timeout: 10000 }).should('be.visible');
  });
});
