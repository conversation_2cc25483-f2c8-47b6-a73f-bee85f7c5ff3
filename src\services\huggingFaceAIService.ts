// src/services/huggingFaceAIService.ts - نظام الذكاء الاصطناعي المتدرج
// تم تحديثه لاستخدام النماذج المحلية عبر Transformers.js بدلاً من Hugging Face API
// يوفر معالجة محلية مجانية مع حماية خصوصية البيانات
import { collection, doc, addDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// أنواع البيانات للنظام المتدرج
export interface OCRResult {
  extractedText: string;
  confidence: number;
  language: 'ar' | 'en' | 'mixed';
  processingTime: number;
  modelUsed: string;
}

export interface NERResult {
  entities: {
    text: string;
    label: string;
    confidence: number;
    start: number;
    end: number;
  }[];
  extractedData: Record<string, any>;
  confidence: number;
  processingTime: number;
  modelUsed: string;
}

export interface ClassificationResult {
  documentType: string;
  decision: 'approve' | 'reject' | 'manual_review';
  confidence: number;
  reasons: string[];
  riskScore: number;
  processingTime: number;
  modelUsed: string;
}

export interface ProcessingResult {
  id: string;
  userId: string;
  userType: 'merchant' | 'representative';
  documentUrl: string;
  originalFileName: string;
  fileSize: number;
  mimeType: string;
  
  // نتائج المراحل الثلاث
  ocrResult?: OCRResult;
  nerResult?: NERResult;
  classificationResult?: ClassificationResult;
  
  // الحالة العامة
  status: 'processing' | 'completed' | 'failed' | 'requires_reupload';
  currentStage: 'ocr' | 'ner' | 'classification' | 'completed';
  overallConfidence: number;
  
  // معلومات إضافية
  qualityCheck: {
    imageQuality: number;
    blurDetected: boolean;
    resolutionSufficient: boolean;
    formatSupported: boolean;
  };
  
  // التوقيتات
  createdAt: any;
  updatedAt: any;
  completedAt?: any;
  
  // الأخطاء والتحذيرات
  errors: string[];
  warnings: string[];
}

// إعدادات الجودة
const QUALITY_THRESHOLDS = {
  minConfidence: 0.7,
  minImageQuality: 0.6,
  maxBlurThreshold: 0.3,
  minResolution: 300
};

export class HuggingFaceAIService {
  private static instance: HuggingFaceAIService;
  
  private constructor() {}
  
  static getInstance(): HuggingFaceAIService {
    if (!HuggingFaceAIService.instance) {
      HuggingFaceAIService.instance = new HuggingFaceAIService();
    }
    return HuggingFaceAIService.instance;
  }

  /**
   * معالجة مستند كاملة عبر المراحل الثلاث
   */
  async processDocument(
    documentUrl: string,
    userId: string,
    userType: 'merchant' | 'representative',
    originalFileName: string,
    fileSize: number,
    mimeType: string
  ): Promise<ProcessingResult> {
    const startTime = Date.now();
    
    // إنشاء سجل المعالجة
    const processingRecord: Partial<ProcessingResult> = {
      userId,
      userType,
      documentUrl,
      originalFileName,
      fileSize,
      mimeType,
      status: 'processing',
      currentStage: 'ocr',
      overallConfidence: 0,
      errors: [],
      warnings: [],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    try {
      // حفظ السجل في قاعدة البيانات
      const docRef = await addDoc(collection(db, 'ai_document_processing'), processingRecord);
      const processingId = docRef.id;

      // فحص جودة الملف أولاً
      const qualityCheck = await this.checkDocumentQuality(documentUrl, mimeType);
      
      if (!qualityCheck.formatSupported || !qualityCheck.resolutionSufficient) {
        await this.updateProcessingRecord(processingId, {
          status: 'requires_reupload',
          qualityCheck,
          errors: ['جودة الملف غير كافية أو التنسيق غير مدعوم'],
          updatedAt: serverTimestamp()
        });
        
        // إرسال إشعار للمستخدم
        await this.sendQualityNotification(userId, userType, 'low_quality');
        
        throw new Error('جودة الملف غير كافية');
      }

      // المرحلة الأولى: OCR
      const ocrResult = await this.performOCR(documentUrl, mimeType);
      await this.updateProcessingRecord(processingId, {
        ocrResult,
        currentStage: 'ner',
        updatedAt: serverTimestamp()
      });

      // المرحلة الثانية: NER & Information Extraction
      const nerResult = await this.performNER(ocrResult.extractedText, ocrResult.language);
      await this.updateProcessingRecord(processingId, {
        nerResult,
        currentStage: 'classification',
        updatedAt: serverTimestamp()
      });

      // المرحلة الثالثة: Classification & Decision Making
      const classificationResult = await this.performClassification(
        nerResult.extractedData,
        ocrResult.extractedText,
        userType
      );

      // حساب الثقة الإجمالية
      const overallConfidence = this.calculateOverallConfidence(
        ocrResult.confidence,
        nerResult.confidence,
        classificationResult.confidence
      );

      // تحديث السجل النهائي
      const finalResult: Partial<ProcessingResult> = {
        classificationResult,
        status: 'completed',
        currentStage: 'completed',
        overallConfidence,
        qualityCheck,
        completedAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      await this.updateProcessingRecord(processingId, finalResult);

      // إرسال إشعار بالنتيجة
      await this.sendCompletionNotification(userId, userType, classificationResult.decision);

      return {
        id: processingId,
        ...processingRecord,
        ocrResult,
        nerResult,
        classificationResult,
        qualityCheck,
        overallConfidence,
        status: 'completed',
        currentStage: 'completed'
      } as ProcessingResult;

    } catch (error) {
      console.error('خطأ في معالجة المستند:', error);
      
      // تحديث السجل بالخطأ
      if (processingRecord.id) {
        await this.updateProcessingRecord(processingRecord.id, {
          status: 'failed',
          errors: [error instanceof Error ? error.message : 'خطأ غير معروف'],
          updatedAt: serverTimestamp()
        });
      }

      throw error;
    }
  }

  /**
   * فحص جودة المستند
   */
  private async checkDocumentQuality(documentUrl: string, mimeType: string) {
    const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic', 'application/pdf'];

    return {
      imageQuality: 0.8, // سيتم تطويرها لاحقاً
      blurDetected: false,
      resolutionSufficient: true,
      formatSupported: supportedFormats.includes(mimeType.toLowerCase())
    };
  }

  /**
   * المرحلة الأولى: OCR - تحويل الصورة إلى نص (محلي)
   */
  private async performOCR(documentUrl: string, mimeType: string): Promise<OCRResult> {
    try {
      // استخدام الخدمة المحلية الجديدة
      const { LocalOCRService } = await import('./localOCRService');
      const ocrService = LocalOCRService.getInstance();

      console.log('🔍 بدء المرحلة الأولى: استخراج النص المحلي من المستند...');
      const result = await ocrService.extractText(documentUrl, mimeType);

      console.log(`✅ تم استخراج النص محلياً بنجاح. الثقة: ${result.confidence}, النموذج: ${result.modelUsed}`);
      return result;

    } catch (error) {
      console.error('❌ خطأ في المرحلة الأولى (OCR المحلي):', error);

      // محاولة استخدام الخدمة القديمة كبديل
      try {
        console.log('🔄 محاولة استخدام OCR التقليدي كبديل...');
        const { OCRService } = await import('./ocrService');
        const fallbackService = OCRService.getInstance();
        return await fallbackService.extractText(documentUrl, mimeType);
      } catch (fallbackError) {
        throw new Error(`فشل في استخراج النص: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      }
    }
  }

  /**
   * المرحلة الثانية: NER - استخلاص الكيانات والمعلومات (محلي)
   */
  private async performNER(text: string, language: 'ar' | 'en' | 'mixed'): Promise<NERResult> {
    try {
      // استخدام الخدمة المحلية الجديدة
      const { LocalNERService } = await import('./localNERService');
      const nerService = LocalNERService.getInstance();

      console.log('🔍 بدء المرحلة الثانية: استخلاص الكيانات والمعلومات محلياً...');
      const result = await nerService.extractEntitiesAndData(text, language);

      console.log(`✅ تم استخلاص ${result.entities.length} كيان و ${Object.keys(result.extractedData).length} حقل بيانات محلياً`);
      return result;

    } catch (error) {
      console.error('❌ خطأ في المرحلة الثانية (NER المحلي):', error);

      // محاولة استخدام الخدمة القديمة كبديل
      try {
        console.log('🔄 محاولة استخدام NER التقليدي كبديل...');
        const { NERService } = await import('./nerService');
        const fallbackService = NERService.getInstance();
        return await fallbackService.extractEntitiesAndData(text, language);
      } catch (fallbackError) {
        throw new Error(`فشل في استخلاص البيانات: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      }
    }
  }

  /**
   * المرحلة الثالثة: التصنيف واتخاذ القرار (محلي)
   */
  private async performClassification(
    extractedData: Record<string, any>,
    originalText: string,
    userType: 'merchant' | 'representative'
  ): Promise<ClassificationResult> {
    try {
      // استخدام الخدمة المحلية الجديدة
      const { LocalClassificationService } = await import('./localClassificationService');
      const classificationService = LocalClassificationService.getInstance();

      console.log('🔍 بدء المرحلة الثالثة: تصنيف المستند واتخاذ القرار محلياً...');
      const result = await classificationService.classifyAndDecide(extractedData, originalText, userType);

      console.log(`✅ تم اتخاذ القرار محلياً: ${result.decision} بثقة ${result.confidence}`);
      return result;

    } catch (error) {
      console.error('❌ خطأ في المرحلة الثالثة (التصنيف):', error);
      throw new Error(`فشل في تصنيف المستند: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  }

  /**
   * تحديث سجل المعالجة
   */
  private async updateProcessingRecord(id: string, updates: Partial<ProcessingResult>) {
    try {
      await updateDoc(doc(db, 'ai_document_processing', id), updates);
    } catch (error) {
      console.error('خطأ في تحديث السجل:', error);
    }
  }

  /**
   * إرسال إشعار جودة المستند
   */
  private async sendQualityNotification(userId: string, userType: string, type: string) {
    try {
      // سيتم تطوير هذا لاحقاً مع نظام الإشعارات
      console.log(`📧 إشعار جودة المستند: ${type} للمستخدم ${userId} (${userType})`);
    } catch (error) {
      console.error('خطأ في إرسال الإشعار:', error);
    }
  }

  /**
   * إرسال إشعار اكتمال المعالجة
   */
  private async sendCompletionNotification(userId: string, userType: string, decision: string) {
    try {
      // سيتم تطوير هذا لاحقاً مع نظام الإشعارات
      console.log(`📧 إشعار اكتمال المعالجة: ${decision} للمستخدم ${userId} (${userType})`);
    } catch (error) {
      console.error('خطأ في إرسال الإشعار:', error);
    }
  }

  /**
   * حساب الثقة الإجمالية
   */
  private calculateOverallConfidence(ocrConf: number, nerConf: number, classConf: number): number {
    return (ocrConf + nerConf + classConf) / 3;
  }
}
