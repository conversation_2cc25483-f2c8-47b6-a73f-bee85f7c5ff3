import { db } from '@/lib/firebase';
import { collection, addDoc, getDocs, query, where, orderBy, limit, updateDoc, doc, serverTimestamp } from 'firebase/firestore';

// أنواع البيانات لنظام التذاكر
export interface SupportTicket {
  id: string;
  ticketNumber: string;
  userId?: string;
  sessionId?: string;
  subject: string;
  description: string;
  category: TicketCategory;
  priority: TicketPriority;
  status: TicketStatus;
  assignedTo?: string;
  assignedToName?: string;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  source: TicketSource;
  tags: string[];
  attachments: TicketAttachment[];
  responses: TicketResponse[];
  satisfactionRating?: number;
  estimatedResolutionTime?: number;
  actualResolutionTime?: number;
}

export interface TicketResponse {
  id: string;
  ticketId: string;
  userId: string;
  userName: string;
  userType: 'customer' | 'support' | 'admin';
  message: string;
  isInternal: boolean;
  createdAt: Date;
  attachments: TicketAttachment[];
}

export interface TicketAttachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
  uploadedBy: string;
  uploadedAt: Date;
}

export type TicketCategory = 
  | 'technical' 
  | 'payment' 
  | 'delivery' 
  | 'account' 
  | 'product' 
  | 'general' 
  | 'complaint' 
  | 'suggestion';

export type TicketPriority = 'low' | 'medium' | 'high' | 'urgent';
export type TicketStatus = 'open' | 'in_progress' | 'pending_customer' | 'resolved' | 'closed';
export type TicketSource = 'chatbot' | 'email' | 'phone' | 'web_form' | 'mobile_app';

export interface TicketStats {
  totalTickets: number;
  openTickets: number;
  resolvedTickets: number;
  averageResolutionTime: number;
  customerSatisfaction: number;
  ticketsByCategory: { category: string; count: number }[];
  ticketsByPriority: { priority: string; count: number }[];
}

/**
 * خدمة نظام التذاكر الذكي المتكامل
 * نظام إدارة تذاكر الدعم مع تصنيف تلقائي وتوجيه ذكي
 */
class IntelligentTicketingService {
  private static readonly TICKET_PREFIX = 'MKH';
  private static readonly AUTO_ASSIGNMENT_ENABLED = true;
  private static readonly SLA_TIMES = {
    low: 72 * 60 * 60 * 1000,      // 72 ساعة
    medium: 24 * 60 * 60 * 1000,   // 24 ساعة
    high: 8 * 60 * 60 * 1000,      // 8 ساعات
    urgent: 2 * 60 * 60 * 1000     // 2 ساعة
  };

  // قواعد التصنيف التلقائي
  private static readonly CATEGORY_RULES = {
    technical: ['خطأ', 'عطل', 'مشكلة تقنية', 'لا يعمل', 'error', 'bug', 'technical issue'],
    payment: ['دفع', 'فاتورة', 'رسوم', 'تحصيل', 'payment', 'billing', 'charge'],
    delivery: ['توصيل', 'شحن', 'مندوب', 'تأخير', 'delivery', 'shipping', 'courier'],
    account: ['حساب', 'تسجيل دخول', 'كلمة مرور', 'account', 'login', 'password'],
    product: ['منتج', 'جودة', 'عيب', 'product', 'quality', 'defect'],
    complaint: ['شكوى', 'غير راضي', 'سيء', 'complaint', 'unsatisfied', 'bad'],
    suggestion: ['اقتراح', 'تحسين', 'فكرة', 'suggestion', 'improvement', 'idea']
  };

  // قواعد تحديد الأولوية
  private static readonly PRIORITY_RULES = {
    urgent: ['عاجل', 'طارئ', 'فوري', 'urgent', 'emergency', 'critical'],
    high: ['مهم', 'سريع', 'عالي', 'important', 'high', 'fast'],
    medium: ['متوسط', 'عادي', 'medium', 'normal'],
    low: ['منخفض', 'بسيط', 'low', 'simple']
  };

  /**
   * إنشاء تذكرة دعم جديدة
   */
  static async createTicket(ticketData: {
    userId?: string;
    sessionId?: string;
    subject: string;
    description: string;
    source: TicketSource;
    category?: TicketCategory;
    priority?: TicketPriority;
    attachments?: File[];
  }): Promise<SupportTicket> {
    try {
      console.log('🎫 إنشاء تذكرة دعم جديدة:', ticketData.subject);

      // توليد رقم التذكرة
      const ticketNumber = await this.generateTicketNumber();

      // التصنيف التلقائي
      const autoCategory = ticketData.category || this.categorizeTicket(ticketData.subject + ' ' + ticketData.description);
      const autoPriority = ticketData.priority || this.prioritizeTicket(ticketData.subject + ' ' + ticketData.description);

      // رفع المرفقات إذا وجدت
      const attachments: TicketAttachment[] = [];
      if (ticketData.attachments && ticketData.attachments.length > 0) {
        // في التطبيق الحقيقي، سيتم رفع الملفات إلى Firebase Storage
        for (const file of ticketData.attachments) {
          attachments.push({
            id: Date.now().toString() + Math.random(),
            fileName: file.name,
            fileUrl: 'placeholder_url', // سيتم استبداله برابط حقيقي
            fileSize: file.size,
            fileType: file.type,
            uploadedBy: ticketData.userId || 'anonymous',
            uploadedAt: new Date()
          });
        }
      }

      // إنشاء التذكرة
      const ticket: Omit<SupportTicket, 'id'> = {
        ticketNumber,
        userId: ticketData.userId,
        sessionId: ticketData.sessionId,
        subject: ticketData.subject,
        description: ticketData.description,
        category: autoCategory,
        priority: autoPriority,
        status: 'open',
        createdAt: new Date(),
        updatedAt: new Date(),
        source: ticketData.source,
        tags: this.generateTags(ticketData.subject + ' ' + ticketData.description),
        attachments,
        responses: [],
        estimatedResolutionTime: this.SLA_TIMES[autoPriority]
      };

      // حفظ في قاعدة البيانات
      const ticketRef = collection(db, 'support_tickets');
      const docRef = await addDoc(ticketRef, {
        ...ticket,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      const newTicket: SupportTicket = {
        id: docRef.id,
        ...ticket
      };

      // التوجيه التلقائي للمختص المناسب
      if (this.AUTO_ASSIGNMENT_ENABLED) {
        await this.autoAssignTicket(newTicket);
      }

      // إرسال إشعارات
      await this.sendTicketNotifications(newTicket, 'created');

      console.log('✅ تم إنشاء التذكرة بنجاح:', ticketNumber);
      return newTicket;

    } catch (error) {
      console.error('خطأ في إنشاء التذكرة:', error);
      throw new Error('فشل في إنشاء تذكرة الدعم');
    }
  }

  /**
   * إضافة رد على التذكرة
   */
  static async addTicketResponse(
    ticketId: string,
    response: {
      userId: string;
      userName: string;
      userType: 'customer' | 'support' | 'admin';
      message: string;
      isInternal?: boolean;
      attachments?: File[];
    }
  ): Promise<TicketResponse> {
    try {
      // رفع المرفقات
      const attachments: TicketAttachment[] = [];
      if (response.attachments && response.attachments.length > 0) {
        for (const file of response.attachments) {
          attachments.push({
            id: Date.now().toString() + Math.random(),
            fileName: file.name,
            fileUrl: 'placeholder_url',
            fileSize: file.size,
            fileType: file.type,
            uploadedBy: response.userId,
            uploadedAt: new Date()
          });
        }
      }

      const ticketResponse: TicketResponse = {
        id: Date.now().toString(),
        ticketId,
        userId: response.userId,
        userName: response.userName,
        userType: response.userType,
        message: response.message,
        isInternal: response.isInternal || false,
        createdAt: new Date(),
        attachments
      };

      // حفظ الرد
      const responseRef = collection(db, 'ticket_responses');
      await addDoc(responseRef, {
        ...ticketResponse,
        createdAt: serverTimestamp()
      });

      // تحديث حالة التذكرة
      const ticketRef = doc(db, 'support_tickets', ticketId);
      await updateDoc(ticketRef, {
        updatedAt: serverTimestamp(),
        status: response.userType === 'customer' ? 'pending_customer' : 'in_progress'
      });

      // إرسال إشعارات
      await this.sendResponseNotifications(ticketResponse);

      return ticketResponse;

    } catch (error) {
      console.error('خطأ في إضافة رد التذكرة:', error);
      throw new Error('فشل في إضافة الرد');
    }
  }

  /**
   * تحديث حالة التذكرة
   */
  static async updateTicketStatus(
    ticketId: string,
    status: TicketStatus,
    userId: string,
    note?: string
  ): Promise<void> {
    try {
      const ticketRef = doc(db, 'support_tickets', ticketId);
      const updateData: any = {
        status,
        updatedAt: serverTimestamp()
      };

      // إذا تم حل التذكرة
      if (status === 'resolved' || status === 'closed') {
        updateData.resolvedAt = serverTimestamp();
      }

      await updateDoc(ticketRef, updateData);

      // إضافة ملاحظة إذا وجدت
      if (note) {
        await this.addTicketResponse(ticketId, {
          userId,
          userName: 'النظام',
          userType: 'admin',
          message: `تم تحديث حالة التذكرة إلى: ${this.getStatusLabel(status)}\n${note}`,
          isInternal: true
        });
      }

      console.log('✅ تم تحديث حالة التذكرة:', status);

    } catch (error) {
      console.error('خطأ في تحديث حالة التذكرة:', error);
      throw new Error('فشل في تحديث حالة التذكرة');
    }
  }

  /**
   * تعيين التذكرة لمختص
   */
  static async assignTicket(
    ticketId: string,
    assignedTo: string,
    assignedToName: string,
    assignedBy: string
  ): Promise<void> {
    try {
      const ticketRef = doc(db, 'support_tickets', ticketId);
      await updateDoc(ticketRef, {
        assignedTo,
        assignedToName,
        status: 'in_progress',
        updatedAt: serverTimestamp()
      });

      // إضافة ملاحظة التعيين
      await this.addTicketResponse(ticketId, {
        userId: assignedBy,
        userName: 'النظام',
        userType: 'admin',
        message: `تم تعيين التذكرة إلى: ${assignedToName}`,
        isInternal: true
      });

      console.log('✅ تم تعيين التذكرة للمختص:', assignedToName);

    } catch (error) {
      console.error('خطأ في تعيين التذكرة:', error);
      throw new Error('فشل في تعيين التذكرة');
    }
  }

  /**
   * الحصول على إحصائيات التذاكر
   */
  static async getTicketStats(): Promise<TicketStats> {
    try {
      // في التطبيق الحقيقي، ستقوم بجلب البيانات من Firebase
      // هذه بيانات تجريبية للعرض
      
      return {
        totalTickets: 1247,
        openTickets: 89,
        resolvedTickets: 1158,
        averageResolutionTime: 4.2, // ساعات
        customerSatisfaction: 4.6,
        ticketsByCategory: [
          { category: 'technical', count: 324 },
          { category: 'payment', count: 198 },
          { category: 'delivery', count: 267 },
          { category: 'account', count: 156 },
          { category: 'product', count: 134 },
          { category: 'general', count: 168 }
        ],
        ticketsByPriority: [
          { priority: 'low', count: 456 },
          { priority: 'medium', count: 523 },
          { priority: 'high', count: 234 },
          { priority: 'urgent', count: 34 }
        ]
      };

    } catch (error) {
      console.error('خطأ في جلب إحصائيات التذاكر:', error);
      throw new Error('فشل في جلب الإحصائيات');
    }
  }

  /**
   * توليد رقم تذكرة فريد
   */
  private static async generateTicketNumber(): Promise<string> {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${this.TICKET_PREFIX}-${timestamp}-${random}`;
  }

  /**
   * التصنيف التلقائي للتذكرة
   */
  private static categorizeTicket(text: string): TicketCategory {
    const lowerText = text.toLowerCase();
    
    for (const [category, keywords] of Object.entries(this.CATEGORY_RULES)) {
      if (keywords.some(keyword => lowerText.includes(keyword))) {
        return category as TicketCategory;
      }
    }
    
    return 'general';
  }

  /**
   * تحديد أولوية التذكرة تلقائياً
   */
  private static prioritizeTicket(text: string): TicketPriority {
    const lowerText = text.toLowerCase();
    
    for (const [priority, keywords] of Object.entries(this.PRIORITY_RULES)) {
      if (keywords.some(keyword => lowerText.includes(keyword))) {
        return priority as TicketPriority;
      }
    }
    
    return 'medium';
  }

  /**
   * توليد علامات للتذكرة
   */
  private static generateTags(text: string): string[] {
    const tags: string[] = [];
    const lowerText = text.toLowerCase();
    
    // إضافة علامات بناءً على الكلمات المفتاحية
    if (lowerText.includes('عاجل') || lowerText.includes('urgent')) tags.push('عاجل');
    if (lowerText.includes('مهم') || lowerText.includes('important')) tags.push('مهم');
    if (lowerText.includes('شكوى') || lowerText.includes('complaint')) tags.push('شكوى');
    if (lowerText.includes('اقتراح') || lowerText.includes('suggestion')) tags.push('اقتراح');
    
    return tags;
  }

  /**
   * التوجيه التلقائي للمختص المناسب
   */
  private static async autoAssignTicket(ticket: SupportTicket): Promise<void> {
    try {
      // قواعد التوجيه بناءً على الفئة
      const assignmentRules = {
        technical: 'tech_support_team',
        payment: 'billing_team',
        delivery: 'logistics_team',
        account: 'customer_service',
        product: 'product_team',
        complaint: 'senior_support',
        general: 'general_support'
      };

      const assignedTeam = assignmentRules[ticket.category] || 'general_support';
      
      // في التطبيق الحقيقي، ستقوم بالبحث عن المختص المتاح
      console.log(`🎯 توجيه تلقائي للتذكرة ${ticket.ticketNumber} إلى فريق: ${assignedTeam}`);

    } catch (error) {
      console.error('خطأ في التوجيه التلقائي:', error);
    }
  }

  /**
   * إرسال إشعارات التذكرة
   */
  private static async sendTicketNotifications(
    ticket: SupportTicket,
    action: 'created' | 'updated' | 'resolved'
  ): Promise<void> {
    try {
      // إرسال إشعار للعميل
      if (ticket.userId) {
        console.log(`📧 إرسال إشعار للعميل: ${action} - ${ticket.ticketNumber}`);
      }

      // إرسال إشعار لفريق الدعم
      console.log(`🔔 إرسال إشعار لفريق الدعم: ${action} - ${ticket.ticketNumber}`);

    } catch (error) {
      console.error('خطأ في إرسال إشعارات التذكرة:', error);
    }
  }

  /**
   * إرسال إشعارات الردود
   */
  private static async sendResponseNotifications(response: TicketResponse): Promise<void> {
    try {
      console.log(`💬 إرسال إشعار رد جديد: ${response.ticketId}`);
    } catch (error) {
      console.error('خطأ في إرسال إشعارات الرد:', error);
    }
  }

  /**
   * الحصول على تسمية الحالة
   */
  private static getStatusLabel(status: TicketStatus): string {
    const labels = {
      open: 'مفتوحة',
      in_progress: 'قيد المعالجة',
      pending_customer: 'في انتظار العميل',
      resolved: 'محلولة',
      closed: 'مغلقة'
    };
    
    return labels[status] || status;
  }
}

export default IntelligentTicketingService;
export type { SupportTicket, TicketResponse, TicketAttachment, TicketStats };
