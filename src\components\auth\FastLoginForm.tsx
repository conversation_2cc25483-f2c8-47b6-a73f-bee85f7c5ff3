// src/components/auth/FastLoginForm.tsx
"use client";

import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useLocale } from '@/hooks/use-locale';
import Link from 'next/link';
import type { Locale } from '@/lib/i18n';
import { auth } from '@/lib/firebase';
import { signInWithEmailAndPassword, setPersistence, browserLocalPersistence, browserSessionPersistence } from 'firebase/auth';
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from 'lucide-react';

export default function FastLoginForm({ locale }: { locale: Locale}) {
  const { t } = useLocale();
  const router = useRouter();
  const { toast } = useToast();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      console.log('🚀 FastLogin: Starting login process');
      
      // Set persistence
      await setPersistence(auth, rememberMe ? browserLocalPersistence : browserSessionPersistence);

      // Sign in
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      console.log("✅ Login successful, user:", firebaseUser.uid);

      // توجيه سريع بدون فحص Firestore
      // نحفظ نوع المستخدم في localStorage إذا كان متوفر من التسجيل السابق
      const savedUserType = localStorage.getItem('userType') || 'customer';
      
      let redirectPath = `/${locale}/dashboard`;
      
      if (savedUserType === 'merchant') {
        redirectPath = `/${locale}/merchant/pending-approval`;
      } else if (savedUserType === 'representative') {
        redirectPath = `/${locale}/representative/signup`;
      }

      console.log('🎯 Fast redirect to:', redirectPath);

      toast({
        title: 'تم تسجيل الدخول بنجاح',
        description: 'مرحباً بك مرة أخرى',
      });

      // توجيه فوري
      setTimeout(() => {
        window.location.href = redirectPath;
      }, 500);

    } catch (err: any) {
      console.error("❌ Login error:", err);
      
      let errorMessage = 'فشل في تسجيل الدخول';
      
      switch (err.code) {
        case 'auth/user-not-found':
          errorMessage = 'البريد الإلكتروني غير مسجل';
          break;
        case 'auth/wrong-password':
          errorMessage = 'كلمة المرور غير صحيحة';
          break;
        case 'auth/invalid-email':
          errorMessage = 'البريد الإلكتروني غير صالح';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'محاولات كثيرة. يرجى المحاولة لاحقاً';
          break;
        case 'auth/network-request-failed':
          errorMessage = 'مشكلة في الاتصال. يرجى المحاولة مرة أخرى';
          break;
        default:
          errorMessage = 'حدث خطأ غير متوقع';
      }

      setError(errorMessage);
      toast({
        title: 'خطأ في تسجيل الدخول',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-bold">{t('loginTitle')}</h1>
        <p className="text-muted-foreground">
          {t('loginSubtitle')}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">{t('emailAddress')}</Label>
          <Input
            id="email"
            type="email"
            placeholder={t('emailPlaceholder')}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={isLoading}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">{t('password')}</Label>
          <Input
            id="password"
            type="password"
            placeholder={t('passwordPlaceholder')}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            disabled={isLoading}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="remember"
            checked={rememberMe}
            onCheckedChange={(checked) => setRememberMe(checked as boolean)}
            disabled={isLoading}
          />
          <Label htmlFor="remember" className="text-sm">
            {t('rememberMe')}
          </Label>
        </div>

        {error && (
          <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
            {error}
          </div>
        )}

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('signingIn')}
            </>
          ) : (
            t('login')
          )}
        </Button>
      </form>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            {t('orContinueWith')}
          </span>
        </div>
      </div>

      <Button
        type="button"
        variant="outline"
        className="w-full"
        onClick={() => {
          // يمكن إضافة تسجيل دخول Google لاحقاً
          toast({
            title: t('comingSoon'),
            description: t('googleSignInComingSoon'),
          });
        }}
      >
        <svg className="me-2 h-4 w-4" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="currentColor"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="currentColor"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="currentColor"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
        {t('continueWithGoogle')}
      </Button>

      <div className="text-center text-sm">
        <Link
          href={`/${locale}/forgot-password`}
          className="text-primary hover:underline"
        >
          {t('forgotPassword')}
        </Link>
      </div>

      <div className="text-center text-sm">
        {t('dontHaveAccount')}{' '}
        <Link
          href={`/${locale}/signup`}
          className="text-primary hover:underline"
        >
          {t('createAccount')}
        </Link>
      </div>
    </div>
  );
}
