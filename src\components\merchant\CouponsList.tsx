'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Copy, 
  Trash2, 
  Eye, 
  ToggleLeft, 
  ToggleRight,
  Calendar,
  Percent,
  DollarSign,
  Truck
} from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { couponService } from '@/services/couponService';
import { CouponDocument, CouponFilters, CouponSortOptions } from '@/types/coupon';
import { toast } from 'sonner';
import { useLocale } from '@/hooks/use-locale';

interface CouponsListProps {
  merchantId: string;
  onEditCoupon: (coupon: CouponDocument) => void;
  onCreateNew: () => void;
}

export function CouponsList({ merchantId, onEditCoupon, onCreateNew }: CouponsListProps) {
  const { t } = useLocale();
  const [coupons, setCoupons] = useState<CouponDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<'createdAt' | 'validUntil' | 'usedCount' | 'value'>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    loadCoupons();
  }, [merchantId, searchTerm, statusFilter, typeFilter, sortField, sortDirection]);

  const loadCoupons = async () => {
    try {
      setLoading(true);
      
      const filters: CouponFilters = {};
      
      if (searchTerm) {
        filters.searchTerm = searchTerm;
      }
      
      if (statusFilter !== 'all') {
        filters.status = [statusFilter as any];
      }
      
      if (typeFilter !== 'all') {
        filters.type = [typeFilter as any];
      }

      const sortOptions: CouponSortOptions = {
        field: sortField,
        direction: sortDirection
      };

      const response = await couponService.getMerchantCoupons(
        merchantId,
        filters,
        sortOptions,
        1,
        50
      );
      
      setCoupons(response.coupons);
    } catch (error) {
      console.error('Error loading coupons:', error);
      toast.error('فشل في تحميل الكوبونات');
    } finally {
      setLoading(false);
    }
  };

  const getCouponStatus = (coupon: CouponDocument) => {
    if (!coupon.isActive) return 'inactive';
    if (new Date() > coupon.validUntil.toDate()) return 'expired';
    if (coupon.usedCount >= coupon.usageLimit) return 'used_up';
    return 'active';
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-500">نشط</Badge>;
      case 'inactive':
        return <Badge variant="secondary">غير نشط</Badge>;
      case 'expired':
        return <Badge variant="destructive">منتهي الصلاحية</Badge>;
      case 'used_up':
        return <Badge variant="outline">مستنفد</Badge>;
      default:
        return <Badge variant="secondary">غير معروف</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'percentage':
        return <Percent className="h-4 w-4" />;
      case 'fixed':
        return <DollarSign className="h-4 w-4" />;
      case 'free_shipping':
        return <Truck className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'percentage':
        return 'نسبة مئوية';
      case 'fixed':
        return 'مبلغ ثابت';
      case 'free_shipping':
        return 'شحن مجاني';
      default:
        return type;
    }
  };

  const formatValue = (coupon: CouponDocument) => {
    switch (coupon.type) {
      case 'percentage':
        return `${coupon.value}%`;
      case 'fixed':
        return `${coupon.value} ريال`;
      case 'free_shipping':
        return 'شحن مجاني';
      default:
        return coupon.value.toString();
    }
  };

  const handleToggleStatus = async (couponId: string) => {
    try {
      await couponService.toggleCouponStatus(couponId);
      toast.success('تم تغيير حالة الكوبون بنجاح');
      loadCoupons();
    } catch (error) {
      toast.error('فشل في تغيير حالة الكوبون');
    }
  };

  const handleDuplicate = async (coupon: CouponDocument) => {
    try {
      const newCode = `${coupon.code}_COPY_${Date.now().toString().slice(-4)}`;
      await couponService.duplicateCoupon(coupon.id, newCode);
      toast.success('تم نسخ الكوبون بنجاح');
      loadCoupons();
    } catch (error) {
      toast.error('فشل في نسخ الكوبون');
    }
  };

  const handleDelete = async (couponId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا الكوبون؟')) {
      return;
    }

    try {
      await couponService.deleteCoupon(couponId);
      toast.success('تم حذف الكوبون بنجاح');
      loadCoupons();
    } catch (error) {
      toast.error('فشل في حذف الكوبون');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>إدارة الكوبونات</CardTitle>
          <Button onClick={onCreateNew}>
            إنشاء كوبون جديد
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* فلاتر البحث */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في الكوبونات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="الحالة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الحالات</SelectItem>
              <SelectItem value="active">نشط</SelectItem>
              <SelectItem value="inactive">غير نشط</SelectItem>
              <SelectItem value="expired">منتهي الصلاحية</SelectItem>
              <SelectItem value="used_up">مستنفد</SelectItem>
            </SelectContent>
          </Select>

          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="النوع" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الأنواع</SelectItem>
              <SelectItem value="percentage">نسبة مئوية</SelectItem>
              <SelectItem value="fixed">مبلغ ثابت</SelectItem>
              <SelectItem value="free_shipping">شحن مجاني</SelectItem>
            </SelectContent>
          </Select>

          <Select value={`${sortField}-${sortDirection}`} onValueChange={(value) => {
            const [field, direction] = value.split('-');
            setSortField(field as any);
            setSortDirection(direction as any);
          }}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="الترتيب" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="createdAt-desc">الأحدث أولاً</SelectItem>
              <SelectItem value="createdAt-asc">الأقدم أولاً</SelectItem>
              <SelectItem value="validUntil-asc">ينتهي قريباً</SelectItem>
              <SelectItem value="usedCount-desc">الأكثر استخداماً</SelectItem>
              <SelectItem value="value-desc">الأعلى قيمة</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* جدول الكوبونات */}
        {coupons.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">لا توجد كوبونات متاحة</p>
            <Button onClick={onCreateNew}>
              إنشاء أول كوبون
            </Button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الكود</TableHead>
                  <TableHead>النوع</TableHead>
                  <TableHead>القيمة</TableHead>
                  <TableHead>الاستخدام</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>تاريخ الانتهاء</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {coupons.map((coupon) => (
                  <TableRow key={coupon.id}>
                    <TableCell className="font-medium">
                      <div>
                        <div className="font-bold">{coupon.code}</div>
                        {coupon.description && (
                          <div className="text-sm text-gray-500 truncate max-w-[150px]">
                            {coupon.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTypeIcon(coupon.type)}
                        <span className="text-sm">{getTypeLabel(coupon.type)}</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatValue(coupon)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{coupon.usedCount} / {coupon.usageLimit}</div>
                        <div className="text-gray-500">
                          {coupon.usageLimit > 0 ? 
                            `${Math.round((coupon.usedCount / coupon.usageLimit) * 100)}%` : 
                            '0%'
                          }
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(getCouponStatus(coupon))}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{format(coupon.validUntil.toDate(), 'dd/MM/yyyy', { locale: ar })}</div>
                        <div className="text-gray-500">
                          {format(coupon.validUntil.toDate(), 'HH:mm', { locale: ar })}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => onEditCoupon(coupon)}>
                            <Edit className="mr-2 h-4 w-4" />
                            تعديل
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDuplicate(coupon)}>
                            <Copy className="mr-2 h-4 w-4" />
                            نسخ
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleStatus(coupon.id)}>
                            {coupon.isActive ? (
                              <>
                                <ToggleLeft className="mr-2 h-4 w-4" />
                                إلغاء التفعيل
                              </>
                            ) : (
                              <>
                                <ToggleRight className="mr-2 h-4 w-4" />
                                تفعيل
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleDelete(coupon.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            حذف
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
