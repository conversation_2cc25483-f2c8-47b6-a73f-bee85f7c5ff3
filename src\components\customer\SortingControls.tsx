"use client";

import { useState } from 'react';
import { useLocale } from '@/hooks/use-locale';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  ArrowUpDown, 
  Star, 
  MapPin, 
  Clock, 
  TrendingUp, 
  DollarSign,
  Zap,
  Grid3X3,
  List,
  Map
} from 'lucide-react';
import type { SearchFilter } from '@/types';
import { cn } from '@/lib/utils';

interface SortingControlsProps {
  filters: SearchFilter;
  onFiltersChange: (filters: SearchFilter) => void;
  totalResults?: number;
  className?: string;
  showViewMode?: boolean;
}

interface SortOption {
  value: SearchFilter['sortBy'];
  label: string;
  icon: React.ReactNode;
  description: string;
}

interface ViewModeOption {
  value: SearchFilter['viewMode'];
  label: string;
  icon: React.ReactNode;
}

export default function SortingControls({
  filters,
  onFiltersChange,
  totalResults,
  className = "",
  showViewMode = true
}: SortingControlsProps) {
  const { t } = useLocale();

  const sortOptions: SortOption[] = [
    {
      value: 'relevance',
      label: 'الأكثر صلة',
      icon: <Zap className="w-4 h-4" />,
      description: 'ترتيب حسب مدى الصلة بالبحث'
    },
    {
      value: 'rating',
      label: 'الأعلى تقييماً',
      icon: <Star className="w-4 h-4" />,
      description: 'ترتيب حسب التقييم من الأعلى للأقل'
    },
    {
      value: 'distance',
      label: 'الأقرب مسافة',
      icon: <MapPin className="w-4 h-4" />,
      description: 'ترتيب حسب المسافة من الأقرب للأبعد'
    },
    {
      value: 'newest',
      label: 'الأحدث',
      icon: <Clock className="w-4 h-4" />,
      description: 'ترتيب حسب تاريخ الإضافة'
    },
    {
      value: 'popular',
      label: 'الأكثر شعبية',
      icon: <TrendingUp className="w-4 h-4" />,
      description: 'ترتيب حسب عدد الزيارات والمبيعات'
    },
    {
      value: 'price_low',
      label: 'السعر: من الأقل للأعلى',
      icon: <DollarSign className="w-4 h-4" />,
      description: 'ترتيب حسب السعر تصاعدياً'
    },
    {
      value: 'price_high',
      label: 'السعر: من الأعلى للأقل',
      icon: <DollarSign className="w-4 h-4" />,
      description: 'ترتيب حسب السعر تنازلياً'
    },
    {
      value: 'offers',
      label: 'الأكثر عروضاً',
      icon: <Badge className="w-4 h-4" />,
      description: 'ترتيب حسب المتاجر التي تحتوي على عروض'
    }
  ];

  const viewModeOptions: ViewModeOption[] = [
    {
      value: 'grid',
      label: 'شبكة',
      icon: <Grid3X3 className="w-4 h-4" />
    },
    {
      value: 'list',
      label: 'قائمة',
      icon: <List className="w-4 h-4" />
    },
    {
      value: 'map',
      label: 'خريطة',
      icon: <Map className="w-4 h-4" />
    }
  ];

  const handleSortChange = (sortBy: SearchFilter['sortBy']) => {
    onFiltersChange({ ...filters, sortBy });
  };

  const handleViewModeChange = (viewMode: SearchFilter['viewMode']) => {
    onFiltersChange({ ...filters, viewMode });
  };

  const getCurrentSortOption = () => {
    return sortOptions.find(option => option.value === filters.sortBy) || sortOptions[0];
  };

  const getCurrentViewMode = () => {
    return viewModeOptions.find(option => option.value === filters.viewMode) || viewModeOptions[0];
  };

  return (
    <div className={cn("flex items-center justify-between gap-4 p-4 bg-background border rounded-lg", className)}>
      {/* معلومات النتائج */}
      <div className="flex items-center gap-2">
        {totalResults !== undefined && (
          <div className="text-sm text-muted-foreground">
            <span className="font-medium text-foreground">{totalResults.toLocaleString('ar')}</span>
            {' '}نتيجة
          </div>
        )}
      </div>

      {/* أدوات التحكم */}
      <div className="flex items-center gap-3">
        {/* ترتيب النتائج */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground whitespace-nowrap">
            ترتيب حسب:
          </span>
          
          {/* للشاشات الكبيرة - Dropdown */}
          <div className="hidden md:block">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="min-w-[140px] justify-between">
                  <div className="flex items-center gap-2">
                    {getCurrentSortOption().icon}
                    <span className="text-sm">{getCurrentSortOption().label}</span>
                  </div>
                  <ArrowUpDown className="w-3 h-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64">
                <DropdownMenuLabel>خيارات الترتيب</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {sortOptions.map((option) => (
                  <DropdownMenuItem
                    key={option.value}
                    onClick={() => handleSortChange(option.value)}
                    className={cn(
                      "flex items-start gap-3 p-3 cursor-pointer",
                      filters.sortBy === option.value && "bg-accent"
                    )}
                  >
                    <div className="mt-0.5">{option.icon}</div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{option.label}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {option.description}
                      </div>
                    </div>
                    {filters.sortBy === option.value && (
                      <Badge variant="secondary" className="text-xs">
                        نشط
                      </Badge>
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* للشاشات الصغيرة - Select */}
          <div className="md:hidden">
            <Select
              value={filters.sortBy || 'relevance'}
              onValueChange={(value) => handleSortChange(value as SearchFilter['sortBy'])}
            >
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value || 'relevance'}>
                    <div className="flex items-center gap-2">
                      {option.icon}
                      <span className="text-sm">{option.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* وضع العرض */}
        {showViewMode && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground whitespace-nowrap hidden sm:inline">
              العرض:
            </span>
            <div className="flex items-center border rounded-md">
              {viewModeOptions.map((option, index) => (
                <Button
                  key={option.value}
                  variant={filters.viewMode === option.value ? "default" : "ghost"}
                  size="sm"
                  onClick={() => handleViewModeChange(option.value)}
                  className={cn(
                    "px-3 py-2 rounded-none",
                    index === 0 && "rounded-l-md",
                    index === viewModeOptions.length - 1 && "rounded-r-md"
                  )}
                  title={option.label}
                >
                  {option.icon}
                  <span className="hidden sm:inline ml-1 text-xs">
                    {option.label}
                  </span>
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * مكون مبسط للترتيب فقط
 */
export function SimpleSortingControls({
  filters,
  onFiltersChange,
  className = ""
}: Omit<SortingControlsProps, 'totalResults' | 'showViewMode'>) {
  const { t } = useLocale();

  const sortOptions: SortOption[] = [
    {
      value: 'relevance',
      label: 'الأكثر صلة',
      icon: <Zap className="w-4 h-4" />,
      description: 'ترتيب حسب مدى الصلة بالبحث'
    },
    {
      value: 'rating',
      label: 'الأعلى تقييماً',
      icon: <Star className="w-4 h-4" />,
      description: 'ترتيب حسب التقييم'
    },
    {
      value: 'distance',
      label: 'الأقرب مسافة',
      icon: <MapPin className="w-4 h-4" />,
      description: 'ترتيب حسب المسافة'
    },
    {
      value: 'newest',
      label: 'الأحدث',
      icon: <Clock className="w-4 h-4" />,
      description: 'ترتيب حسب التاريخ'
    }
  ];

  const handleSortChange = (sortBy: SearchFilter['sortBy']) => {
    onFiltersChange({ ...filters, sortBy });
  };

  const getCurrentSortOption = () => {
    return sortOptions.find(option => option.value === filters.sortBy) || sortOptions[0];
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <span className="text-sm text-muted-foreground">
        ترتيب:
      </span>
      <Select
        value={filters.sortBy || 'relevance'}
        onValueChange={(value) => handleSortChange(value as SearchFilter['sortBy'])}
      >
        <SelectTrigger className="w-[120px]">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {sortOptions.map((option) => (
            <SelectItem key={option.value} value={option.value || 'relevance'}>
              <div className="flex items-center gap-2">
                {option.icon}
                <span className="text-sm">{option.label}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
