import { GoogleGenerativeAI } from '@google/generative-ai';

/**
 * خدمة الشات بوت المدعومة بـ Gemini 2.0 Flash
 * نظام محادثة ذكي مجاني وسريع
 */
class GeminiChatbotService {
  private static genAI: GoogleGenerativeAI | null = null;
  private static model: any = null;

  /**
   * تهيئة خدمة Gemini
   */
  private static initializeGemini() {
    if (!this.genAI) {
      const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
      if (!apiKey) {
        throw new Error('مفتاح Gemini API غير موجود في متغيرات البيئة');
      }
      
      this.genAI = new GoogleGenerativeAI(apiKey);
      this.model = this.genAI.getGenerativeModel({ 
        model: "gemini-2.0-flash-exp",
        generationConfig: {
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 1024,
        }
      });
    }
  }

  /**
   * إنشاء prompt النظام للشات بوت
   */
  private static getSystemPrompt(): string {
    return `أنت مساعد ذكي لمنصة "مِخْلاة" - منصة التجارة الإلكترونية المحلية الرائدة في المملكة العربية السعودية.

معلومات عن المنصة:
- الاسم: مِخْلاة (Mikhla)
- النوع: منصة تجارة إلكترونية تربط التجار المحليين بالعملاء
- الخدمات: بيع المنتجات، توصيل سريع، دفع آمن
- المناطق: المملكة العربية السعودية
- التوصيل: 30-60 دقيقة في المدن الرئيسية، 2-4 ساعات في المناطق الأخرى
- طرق الدفع: نقداً عند التسليم، مدى، فيزا، ماستركارد، Apple Pay، Google Pay، STC Pay، تقسيط (تمارا، تابي)

إجابات محددة للأسئلة الشائعة:

1. كيف أسجل كتاجر؟
   - اذهب إلى الصفحة الرئيسية للموقع
   - اضغط على "إنشاء حساب" أو "تسجيل"
   - اختر "تاجر" من خيارات نوع الحساب
   - املأ البيانات المطلوبة (الاسم، البريد الإلكتروني، رقم الهاتف)
   - أكمل معلومات المتجر (اسم المتجر، الوصف، العنوان)
   - ارفع المستندات المطلوبة (السجل التجاري، الهوية)
   - انتظر الموافقة من فريق المراجعة (عادة 24-48 ساعة)

2. كيف أتتبع طلبي؟
   - سجل الدخول إلى حسابك
   - اذهب إلى قسم "طلباتي"
   - اضغط على الطلب المراد تتبعه
   - ستجد حالة الطلب ومعلومات التوصيل

3. كيف أسجل كمندوب توصيل؟
   - اذهب إلى الصفحة الرئيسية
   - اضغط على "إنشاء حساب"
   - اختر "مندوب توصيل"
   - املأ البيانات الشخصية
   - أضف معلومات المركبة ورخصة القيادة
   - ارفع المستندات المطلوبة
   - انتظر الموافقة

دورك:
- قدم إجابات دقيقة ومفصلة بناءً على المعلومات أعلاه
- الرد باللغة العربية دائماً
- كن مفيداً ومهذباً ومختصراً
- إذا سُئلت عن شيء غير موجود في المعلومات أعلاه، اعترف بذلك واقترح التواصل مع الدعم

قواعد مهمة:
- لا تطلب أو تتعامل مع معلومات شخصية حساسة
- لا تقدم نصائح مالية أو قانونية
- ركز على الاستفسارات العامة عن المنصة
- كن إيجابياً ومساعداً

ابدأ المحادثة بترحيب ودود واسأل كيف يمكنك المساعدة.`;
  }

  /**
   * معالجة رسالة المستخدم والحصول على رد من Gemini
   */
  static async processMessage(
    message: string,
    conversationHistory: Array<{role: string, content: string}> = []
  ): Promise<{
    response: string;
    success: boolean;
    error?: string;
  }> {
    try {
      console.log('🤖 بدء معالجة الرسالة مع Gemini:', message);
      
      // تهيئة Gemini
      this.initializeGemini();

      if (!this.model) {
        throw new Error('فشل في تهيئة نموذج Gemini');
      }

      // بناء المحادثة
      const chat = this.model.startChat({
        history: [
          {
            role: "user",
            parts: [{ text: this.getSystemPrompt() }],
          },
          {
            role: "model",
            parts: [{ text: "مرحباً! أنا مساعد مِخْلاة الذكي. كيف يمكنني مساعدتك اليوم؟" }],
          },
          // إضافة تاريخ المحادثة
          ...conversationHistory.map(msg => ({
            role: msg.role === 'user' ? 'user' : 'model',
            parts: [{ text: msg.content }],
          }))
        ],
      });

      // إرسال الرسالة والحصول على الرد
      const result = await chat.sendMessage(message);
      const response = result.response;
      const responseText = response.text();

      console.log('✅ تم الحصول على رد من Gemini:', responseText);

      return {
        response: responseText,
        success: true
      };

    } catch (error) {
      console.error('❌ خطأ في معالجة الرسالة مع Gemini:', error);
      
      return {
        response: 'أعتذر، حدث خطأ تقني. يرجى المحاولة مرة أخرى أو التواصل مع فريق الدعم.',
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      };
    }
  }

  /**
   * إنشاء رسالة ترحيب
   */
  static getWelcomeMessage(): string {
    return 'مرحباً بك في مِخْلاة! 👋\nأنا مساعدك الذكي المدعوم بتقنية. كيف يمكنني مساعدتك اليوم؟';
  }

  /**
   * الحصول على اقتراحات سريعة
   */
  static getQuickSuggestions(): string[] {
    return [
      'كيف أسجل كتاجر؟',
      'كيف أتتبع طلبي؟',
      'ما هي طرق الدفع المتاحة؟',
      'كم يستغرق التوصيل؟',
      'كيف أسجل كمندوب توصيل؟',
      'ما هي مِخْلاة؟'
    ];
  }

  /**
   * التحقق من توفر الخدمة
   */
  static isServiceAvailable(): boolean {
    return !!process.env.NEXT_PUBLIC_GEMINI_API_KEY;
  }
}

export default GeminiChatbotService;
