// src/components/auth/SignupForm.tsx
"use client";

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useLocale } from '@/hooks/use-locale';
import type { Locale } from '@/lib/i18n';
import { auth, db } from '@/lib/firebase';
import { createUserWithEmailAndPassword, updateProfile, fetchSignInMethodsForEmail, sendEmailVerification } from 'firebase/auth';
import { doc, setDoc, serverTimestamp, updateDoc, type Timestamp, collection, query, where, getDocs } from 'firebase/firestore';
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { Loader2, User, Mail, Lock, UserCheck, FileText, Upload } from 'lucide-react';
import TermsAndConditionsModal from './TermsAndConditionsModal';
import type { UserType, UserDocument, StoreDocument } from '@/types';
import { merchantPlans } from '@/constants/plans';
import GoogleSignInButton from './GoogleSignInButton';

// ===== APEX SECURITY IMPORTS =====
import { DocumentEncryptionService } from '@/lib/encryption';
import { ApexAuditSystem, logUserAction, logDataAccess } from '@/lib/audit-system';

// Cloudinary Upload Function
async function uploadFileToCloudinary(file: File): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);
  const uploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET;
  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;

  if (!uploadPreset || !cloudName) {
    console.error('Cloudinary environment variables (upload preset or cloud name) are not set.');
    throw new Error('Cloudinary configuration error.');
  }

  formData.append('upload_preset', uploadPreset);

  console.log(`Uploading ${file.name} to Cloudinary. Cloud Name: ${cloudName}, Preset: ${uploadPreset}`);

  const response = await fetch(
    `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`,
    {
      method: 'POST',
      body: formData,
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    console.error('Cloudinary upload failed. Response:', errorData);
    throw new Error(errorData.error.message || 'Cloudinary upload failed');
  }

  const data = await response.json();
  console.log(`Successfully uploaded ${file.name} to Cloudinary. URL: ${data.secure_url}`);
  return data.secure_url; // URL of the uploaded file
}

// ===== APEX SECURITY: دالة رفع آمنة مع التشفير للتجار =====
async function uploadMerchantDocumentSecurely(file: File, documentType: string, userId?: string): Promise<string> {
  try {
    console.log(`🔐 بدء رفع آمن للمستند: ${documentType}`);

    // 1. تشفير المستند قبل الرفع
    const encryptedPayload = await DocumentEncryptionService.encryptDocument(file);

    // 2. تسجيل عملية التشفير للمراجعة
    if (userId) {
      await logDataAccess('merchant_document_encryption', userId, {
        documentType,
        fileName: file.name,
        fileSize: file.size,
        encryptionAlgorithm: 'AES-256-GCM',
        timestamp: new Date()
      });
    }

    // 3. تحويل البيانات المشفرة إلى ملف للرفع
    const encryptedBlob = new Blob([JSON.stringify(encryptedPayload)], {
      type: 'application/json'
    });
    const encryptedFile = new File([encryptedBlob], `encrypted_${documentType}_${file.name}.json`, {
      type: 'application/json'
    });

    // 4. رفع الملف المشفر إلى Cloudinary
    const formData = new FormData();
    formData.append('file', encryptedFile);
    const uploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET;
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;

    if (!uploadPreset || !cloudName) {
      console.error('Cloudinary environment variables are not set.');
      throw new Error('Cloudinary configuration error.');
    }

    formData.append('upload_preset', uploadPreset);

    console.log(`🔐 رفع المستند المشفر: ${documentType} إلى Cloudinary`);

    const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/raw/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to upload encrypted document to Cloudinary');
    }

    const data = await response.json();

    // 5. تسجيل نجاح الرفع
    if (userId) {
      await logUserAction('merchant_document_upload_success', userId, {
        documentType,
        fileName: file.name,
        encryptedUrl: data.secure_url,
        uploadTimestamp: new Date()
      });
    }

    console.log(`✅ تم رفع المستند المشفر بنجاح: ${documentType}`);
    return data.secure_url;

  } catch (error) {
    console.error(`🔴 خطأ في رفع المستند المشفر:`, error);

    // تسجيل الخطأ للمراجعة
    if (userId) {
      await logUserAction('merchant_document_upload_failed', userId, {
        documentType,
        fileName: file.name,
        error: error.message,
        timestamp: new Date()
      });
    }

    throw new Error(`فشل في رفع المستند المشفر: ${documentType}`);
  }
}


export default function SignupForm({ locale }: { locale: Locale }) {
  const { t } = useLocale();
  const router = useRouter();
  const { toast } = useToast();

  // Check for userType in URL params
  const [urlUserType, setUrlUserType] = useState<UserType | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const userTypeParam = urlParams.get('userType') as UserType;
      if (userTypeParam && ['customer', 'merchant', 'representative'].includes(userTypeParam)) {
        setUrlUserType(userTypeParam);
        setUserType(userTypeParam);
        // إذا جاء المستخدم مع userType في الـ URL، ابدأ من خطوة الموافقة على الشروط
        setCurrentStep(2);
      }
    }
  }, []);

  // Multi-step form state
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3; // Pre-signup steps: 1. User Type, 2. Terms, 3. Detailed Form

  // Pre-signup state
  const [showDetailedForm, setShowDetailedForm] = useState(false);
  const [detailedFormStep, setDetailedFormStep] = useState(1);
  const detailedFormTotalSteps = 4; // For detailed form: 1. Basic Info, 2. Password, 3. Documents (if merchant), 4. Final

  // Form data state
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [userType, setUserType] = useState<UserType | null>(null);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [commercialRegistrationFile, setCommercialRegistrationFile] = useState<File | null>(null);
  const [otherLicensesFile, setOtherLicensesFile] = useState<File | null>(null);
  const [freelanceDocumentFile, setFreelanceDocumentFile] = useState<File | null>(null);
  const [isTermsModalOpen, setIsTermsModalOpen] = useState(false);

  // File preview state
  const [filePreview, setFilePreview] = useState<{
    commercial?: string;
    other?: string;
    freelance?: string;
  }>({});

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);

  // دالة للتحقق من وجود الإيميل مسبقاً
  const checkEmailExists = async (emailToCheck: string): Promise<boolean> => {
    try {
      setIsCheckingEmail(true);

      // التحقق من صحة البريد الإلكتروني أولاً
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(emailToCheck)) {
        console.log('Invalid email format:', emailToCheck);
        return false;
      }

      console.log('🔍 Checking if email exists:', emailToCheck);

      // استخدام طريقة أكثر موثوقية للتحقق مع timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Email check timeout')), 10000); // 10 seconds timeout
      });

      const checkPromise = fetchSignInMethodsForEmail(auth, emailToCheck);

      const signInMethods = await Promise.race([checkPromise, timeoutPromise]);
      const exists = signInMethods.length > 0;

      console.log('✅ Email check result:', { email: emailToCheck, exists });
      return exists;
    } catch (error: any) {
      console.error('❌ Error checking email:', error);

      // إذا كان الخطأ يشير إلى أن البريد الإلكتروني غير موجود، فهذا جيد
      if (error.code === 'auth/user-not-found' || error.code === 'auth/invalid-email') {
        console.log('✅ Email not found (good for signup)');
        return false;
      }

      // في حالة timeout أو أخطاء أخرى، نسمح بالمتابعة لتجنب منع المستخدم
      console.warn('⚠️ Email check failed, allowing signup to proceed:', error.message);
      return false;
    } finally {
      setIsCheckingEmail(false);
    }
  };

  // دالة للتحقق من وجود اسم المستخدم مسبقاً
  const checkUsernameExists = async (usernameToCheck: string): Promise<boolean> => {
    try {
      setIsCheckingUsername(true);

      // التحقق من صحة اسم المستخدم أولاً
      if (!usernameToCheck.trim() || usernameToCheck.length < 3) {
        console.log('Invalid username format:', usernameToCheck);
        return false;
      }

      console.log('🔍 Checking if username exists:', usernameToCheck);

      // البحث في مجموعة المستخدمين عن اسم المستخدم مع timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Username check timeout')), 10000); // 10 seconds timeout
      });

      const usersRef = collection(db, 'users');
      const usernameQuery = query(
        usersRef,
        where('displayName', '==', usernameToCheck.trim())
      );

      const checkPromise = getDocs(usernameQuery);
      const querySnapshot = await Promise.race([checkPromise, timeoutPromise]);

      const exists = !querySnapshot.empty;
      console.log('✅ Username check result:', { username: usernameToCheck, exists });

      return exists;
    } catch (error: any) {
      console.error('❌ Error checking username:', error);

      // في حالة timeout أو وجود خطأ، نسمح بالمتابعة لتجنب منع المستخدم
      console.warn('⚠️ Username check failed, allowing signup to proceed:', error.message);
      return false;
    } finally {
      setIsCheckingUsername(false);
    }
  };

  // Navigation functions for pre-signup
  const nextStep = () => {
    if (validateCurrentStep()) {
      if (currentStep === totalSteps) {
        // Move to detailed form
        setShowDetailedForm(true);
        setDetailedFormStep(1);
      } else {
        setCurrentStep(prev => Math.min(prev + 1, totalSteps));
      }
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Navigation functions for detailed form
  const nextDetailedStep = async () => {
    try {
      console.log('🚀 Moving to next detailed step...');
      setError(null); // مسح أي أخطاء سابقة

      const isValid = await validateDetailedFormStep();
      if (isValid) {
        console.log('✅ Validation passed, moving to next step');
        setDetailedFormStep(prev => Math.min(prev + 1, detailedFormTotalSteps));
      } else {
        console.log('❌ Validation failed, staying on current step');
      }
    } catch (error) {
      console.error('❌ Error in nextDetailedStep:', error);
      toast({
        title: t('errorTitle'),
        description: 'حدث خطأ أثناء التحقق من البيانات. يرجى المحاولة مرة أخرى.',
        variant: "destructive"
      });
    }
  };

  const prevDetailedStep = () => {
    if (detailedFormStep === 1) {
      // Go back to pre-signup flow
      setShowDetailedForm(false);
      setCurrentStep(totalSteps);
    } else {
      setDetailedFormStep(prev => Math.max(prev - 1, 1));
    }
  };

  // Validation function for pre-signup steps
  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 1:
        if (!userType) {
          setError(t('errorUserTypeRequired'));
          toast({ title: t('errorTitle'), description: t('errorUserTypeRequired'), variant: "destructive" });
          return false;
        }
        return true;
      case 2:
        if (!agreedToTerms) {
          setError(t('errorTermsRequired'));
          toast({ title: t('errorTitle'), description: t('errorTermsRequired'), variant: "destructive" });
          return false;
        }
        return true;
      case 3:
        // This step is just a confirmation, always valid
        return true;
      default:
        return true;
    }
  };

  // Validation function for detailed form steps
  const validateDetailedFormStep = async (): Promise<boolean> => {
    try {
      switch (detailedFormStep) {
        case 1:
          console.log('🔍 Validating step 1 - Basic Information');

          // التحقق من اسم المستخدم
          if (!username.trim()) {
            setError(t('errorUsernameRequired'));
            toast({ title: t('errorTitle'), description: t('errorUsernameRequired'), variant: "destructive" });
            return false;
          }

          if (username.length < 3) {
            setError(t('errorUsernameMinLength'));
            toast({ title: t('errorTitle'), description: t('errorUsernameMinLength'), variant: "destructive" });
            return false;
          }

          // التحقق من الإيميل
          if (!email.trim()) {
            setError(t('errorEmailRequired'));
            toast({ title: t('errorTitle'), description: t('errorEmailRequired'), variant: "destructive" });
            return false;
          }

          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(email)) {
            setError(t('errorEmailInvalid'));
            toast({ title: t('errorTitle'), description: t('errorEmailInvalid'), variant: "destructive" });
            return false;
          }

          console.log('📝 Basic validation passed, checking for duplicates...');

          // التحقق من وجود اسم المستخدم والإيميل مسبقاً مع معالجة أفضل للأخطاء
          try {
            const usernameExists = await checkUsernameExists(username);
            if (usernameExists) {
              setError(t('usernameAlreadyInUse'));
              toast({ title: t('errorTitle'), description: t('usernameAlreadyInUse'), variant: "destructive" });
              return false;
            }

            const emailExists = await checkEmailExists(email);
            if (emailExists) {
              setError(t('emailAlreadyInUse'));
              toast({ title: t('errorTitle'), description: t('emailAlreadyInUse'), variant: "destructive" });
              return false;
            }

            console.log('✅ All validations passed for step 1');
            return true;
          } catch (validationError) {
            console.error('❌ Validation error:', validationError);
            // في حالة فشل التحقق، نسمح بالمتابعة مع تحذير
            toast({
              title: t('warning'),
              description: 'تعذر التحقق من البيانات، سيتم المتابعة...',
              variant: "default"
            });
            return true;
          }

        case 2:
        if (password.length < 6) {
          setError(t('passwordTooShort'));
          toast({ title: t('errorTitle'), description: t('passwordTooShort'), variant: "destructive" });
          return false;
        }
        if (password !== confirmPassword) {
          setError(t('passwordsDoNotMatch'));
          toast({ title: t('errorTitle'), description: t('passwordsDoNotMatch'), variant: "destructive" });
          return false;
        }
        return true;
      case 3:
        if (userType === 'merchant') {
          if (!commercialRegistrationFile) {
            setError(t('errorCommercialRegistrationRequired'));
            toast({ title: t('errorTitle'), description: t('errorCommercialRegistrationRequired'), variant: "destructive" });
            return false;
          }
          if (!freelanceDocumentFile) {
            setError(t('errorFreelanceDocumentRequired'));
            toast({ title: t('errorTitle'), description: t('errorFreelanceDocumentRequired'), variant: "destructive" });
            return false;
          }
        }
        return true;
      case 4:
        // Final step, always valid
        return true;
      default:
        return true;
    }
  } catch (error) {
    console.error('❌ Error in validateDetailedFormStep:', error);
    // في حالة حدوث خطأ، نسمح بالمتابعة مع تحذير
    toast({
      title: t('warning'),
      description: 'حدث خطأ أثناء التحقق، سيتم المتابعة...',
      variant: "default"
    });
    return true;
  }
};

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>, fileType: 'commercial' | 'other' | 'freelance') => {
    const file = event.target.files?.[0];
    if (file) {
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setFilePreview(prev => ({
        ...prev,
        [fileType]: previewUrl
      }));

      if (fileType === 'commercial') {
        setCommercialRegistrationFile(file);
      } else if (fileType === 'other') {
        setOtherLicensesFile(file);
      } else if (fileType === 'freelance') {
        setFreelanceDocumentFile(file);
      }
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);

    // Final validation for detailed form (تجنب التحقق المزدوج)
    if (detailedFormStep !== 4) {
      const isValid = await validateDetailedFormStep();
      if (!isValid) {
        return;
      }
    }

    setIsLoading(true);

    try {
      console.log("Attempting to create user in Firebase Auth...");
      toast({ title: t('signupInProgressTitle'), description: t('signupInProgressMessage')});

      // محاولة إنشاء المستخدم مع معالجة أفضل للأخطاء
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      console.log("Firebase Auth user created successfully:", firebaseUser.uid);

      if (firebaseUser) {
        await updateProfile(firebaseUser, { displayName: username });
        console.log("Firebase Auth profile updated with displayName.");

        // إرسال بريد التحقق
        try {
          await sendEmailVerification(firebaseUser);
          console.log("✅ تم إرسال بريد التحقق بنجاح");
          toast({
            title: "تم إرسال بريد التحقق",
            description: "يرجى التحقق من بريدك الإلكتروني لتفعيل حسابك",
          });
        } catch (verificationError) {
          console.warn("⚠️ فشل في إرسال بريد التحقق:", verificationError);
          // لا نوقف عملية التسجيل بسبب فشل إرسال بريد التحقق
        }

        // تعيين الخطة المجانية الافتراضية حسب نوع المستخدم
        const defaultPlanId = userType === 'customer' ? 'customer-basic' :
                             userType === 'merchant' ? 'merchant-basic' :
                             'representative-basic';

        const userDocData: UserDocument = {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          displayName: username,
          photoURL: null,
          userType: userType,
          planId: defaultPlanId, // إضافة الخطة المجانية الافتراضية
          createdAt: serverTimestamp() as Timestamp,
          updatedAt: serverTimestamp() as Timestamp,
        };
        console.log("Attempting to create user document in Firestore for UID:", firebaseUser.uid);
        await setDoc(doc(db, "users", firebaseUser.uid), userDocData);
        console.log("User document created successfully in Firestore.");

        if (userType === 'merchant') {
          const defaultPlan = merchantPlans.find(plan => plan.priceDisplayKey === 'free');
          const defaultPlanId = defaultPlan ? defaultPlan.id : 'merchant-basic';

          const storeDocRef = doc(db, "stores", firebaseUser.uid);
          const storeDocData: StoreDocument = {
            merchantUid: firebaseUser.uid,
            storeName: `${username}'s Store`,
            isActive: false, // سيتم تفعيله بعد الموافقة
            isVerified: false,
            planId: defaultPlanId,
            // نظام الموافقة الجديد
            approvalStatus: 'pending', // في انتظار الموافقة
            submittedAt: serverTimestamp() as Timestamp,
            createdAt: serverTimestamp() as Timestamp,
            updatedAt: serverTimestamp() as Timestamp,
          };
          console.log("Attempting to create store document in Firestore for merchant UID:", firebaseUser.uid);
          await setDoc(storeDocRef, storeDocData);
          console.log("Store document created successfully in Firestore.");

          let commercialRegURL: string | undefined = undefined;
          let otherLicensesURL: string | undefined = undefined;
          let freelanceDocumentURL: string | undefined = undefined;
          
          // ===== APEX SECURITY: رفع آمن للمستندات الحساسة =====
          console.log('🔐 بدء رفع المستندات التجارية بشكل آمن...');

          // تسجيل بداية عملية التسجيل
          await logUserAction('merchant_signup_start', firebaseUser.uid, {
            email: email.substring(0, 3) + '***',
            displayName: username,
            timestamp: new Date()
          });

          if (commercialRegistrationFile) {
            toast({ title: t('uploadingFilesTitle'), description: '🔐 تشفير ورفع السجل التجاري بشكل آمن...' });
            try {
              console.log("🔐 رفع السجل التجاري مع التشفير...");
              commercialRegURL = await uploadMerchantDocumentSecurely(
                commercialRegistrationFile,
                'commercial_registration',
                firebaseUser.uid
              );
              console.log("✅ تم رفع السجل التجاري المشفر:", commercialRegURL);
              toast({ title: '✅ تم الرفع بأمان', description: 'تم تشفير ورفع السجل التجاري بنجاح'});
            } catch (uploadError: any) {
              console.error("🔴 فشل رفع السجل التجاري المشفر:", uploadError);
              toast({ title: t('errorTitle'), description: `فشل في رفع السجل التجاري المشفر: ${uploadError.message}`, variant: "destructive" });
            }
          }

          if (otherLicensesFile) {
            toast({ title: t('uploadingFilesTitle'), description: t('uploadingOtherLicensesDescCloudinary') });
            try {
              console.log("Attempting to upload other licenses to Cloudinary...");
              otherLicensesURL = await uploadFileToCloudinary(otherLicensesFile);
              console.log("Other licenses uploaded to Cloudinary:", otherLicensesURL);
              toast({ title: t('fileUploadSuccessTitleCloudinary'), description: t('otherLicensesUploadSuccessDescCloudinary')});
            } catch (uploadError: any) {
              console.error("Cloudinary other licenses upload failed:", uploadError);
              toast({ title: t('errorTitle'), description: t('errorFileUploadFailedCloudinary', { file: t('otherLicenses') }) + `: ${uploadError.message}`, variant: "destructive" });
            }
          }

          if (freelanceDocumentFile) {
            toast({ title: t('uploadingFilesTitle'), description: '🔐 تشفير ورفع وثيقة العمل الحر بشكل آمن...' });
            try {
              console.log("🔐 رفع وثيقة العمل الحر مع التشفير...");
              freelanceDocumentURL = await uploadMerchantDocumentSecurely(
                freelanceDocumentFile,
                'freelance_document',
                firebaseUser.uid
              );
              console.log("✅ تم رفع وثيقة العمل الحر المشفرة:", freelanceDocumentURL);
              toast({ title: '✅ تم الرفع بأمان', description: 'تم تشفير ورفع وثيقة العمل الحر بنجاح'});
            } catch (uploadError: any) {
              console.error("🔴 فشل رفع وثيقة العمل الحر المشفرة:", uploadError);
              toast({ title: t('errorTitle'), description: `فشل في رفع وثيقة العمل الحر المشفرة: ${uploadError.message}`, variant: "destructive" });
            }
          }
          
          const updateData: Partial<StoreDocument> = { updatedAt: serverTimestamp() as Timestamp };
          let shouldUpdateStoreDoc = false;
          if (commercialRegURL) {
            updateData.commercialRegistrationURL = commercialRegURL;
            shouldUpdateStoreDoc = true;
          }
          if (otherLicensesURL) {
            updateData.otherLicensesURL = otherLicensesURL;
            shouldUpdateStoreDoc = true;
          }
          if (freelanceDocumentURL) {
            updateData.freelanceDocumentURL = freelanceDocumentURL;
            shouldUpdateStoreDoc = true;
          }

          if (shouldUpdateStoreDoc) {
            console.log("Attempting to update store document in Firestore with Cloudinary URLs...");
            await updateDoc(storeDocRef, updateData);
            console.log("Store document updated successfully with Cloudinary URLs.");
          }

          // ===== APEX SECURITY: تسجيل نجاح التسجيل =====
          await logUserAction('merchant_signup_success', firebaseUser.uid, {
            email: email.substring(0, 3) + '***',
            displayName: username,
            documentsUploaded: {
              commercialRegistration: !!commercialRegURL,
              freelanceDocument: !!freelanceDocumentURL,
              otherLicenses: !!otherLicensesURL
            },
            timestamp: new Date()
          });

          console.log('✅ تم تسجيل التاجر بنجاح مع الحماية الأمنية الكاملة');
        }
      }

      toast({
        title: t('signupSuccessTitle'),
        description: t('signupSuccessMessage'),
      });

      // تعيين علامات متعددة للتسجيل الجديد مع آلية أمان
      try {
        sessionStorage.setItem('justSignedUp', 'true');
        sessionStorage.setItem('newUserType', userType);
        sessionStorage.setItem('signupTimestamp', Date.now().toString());
        localStorage.setItem('justSignedUp', 'true');
        localStorage.setItem('newUserType', userType);
        localStorage.setItem('signupTimestamp', Date.now().toString());
        console.log('SignupForm: Set justSignedUp flag for new user', { userType, timestamp: Date.now() });
      } catch (storageError) {
        console.warn('⚠️ Storage error, continuing without flags:', storageError);
      }

      // انتظار أطول للسماح لـ Firebase بحفظ حالة المصادقة والبيانات
      console.log("⏳ Waiting for Firebase Auth state to persist...");
      await new Promise(resolve => setTimeout(resolve, 1000));

      // التأكد من أن المستخدم ما زال مسجل دخول
      const currentUser = auth.currentUser;
      if (!currentUser) {
        console.error("❌ User lost after signup - auth state not persisted");
        throw new Error("Authentication state was not persisted");
      }

      console.log("✅ User still authenticated:", currentUser.uid);

      // التأكد من أن مستند المستخدم تم إنشاؤه بنجاح
      try {
        const { getDocumentWithRetry } = await import('@/lib/firestore-utils');
        const userDocCheck = await getDocumentWithRetry(
          `users/${currentUser.uid}`,
          { retries: 2, timeout: 5000 }
        );

        if (!userDocCheck.success || !userDocCheck.exists) {
          console.warn('⚠️ User document not found after creation, but continuing...');
        } else {
          console.log('✅ User document verified:', userDocCheck.data?.userType);
        }
      } catch (docError) {
        console.warn('⚠️ Could not verify user document, but continuing:', docError);
      }

      // توجيه لصفحة نجاح المصادقة مع نوع المستخدم
      const redirectPath = `/${locale}/auth-success?userType=${userType}`;
      console.log("🔄 Redirecting to auth success page:", redirectPath);
      console.log("🔍 Current user before redirect:", currentUser.uid);
      console.log("🔍 User type:", userType);

      // استخدام window.location للتوجيه المباشر مع fallback
      try {
        console.log("🚀 Attempting redirect with window.location.href");
        window.location.href = redirectPath;
      } catch (redirectError) {
        console.error('❌ Redirect error, trying alternative:', redirectError);
        window.location.replace(redirectPath);
      }

    } catch (err: any) {
      console.error("Signup error:", err);
      let errorMessage = t('signupFailed');

      // معالجة أفضل لأخطاء Firebase Auth
      switch (err.code) {
        case 'auth/email-already-in-use':
          errorMessage = t('emailAlreadyInUse');
          break;
        case 'auth/weak-password':
          errorMessage = t('passwordTooShort');
          break;
        case 'auth/invalid-email':
          errorMessage = t('errorInvalidEmailFormat');
          break;
        case 'auth/operation-not-allowed':
          errorMessage = 'نوع التسجيل غير مسموح. يرجى التواصل مع الدعم.';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'محاولات كثيرة. يرجى المحاولة لاحقاً.';
          break;
        default:
          // معالجة أخطاء Cloudinary وأخطاء أخرى
          if (err.message) {
            if (err.message.includes('Cloudinary configuration error')) {
              errorMessage = err.message;
            } else if (err.message.includes('Cloudinary upload failed')) {
              errorMessage = err.message;
            } else if (err.message.includes('network')) {
              errorMessage = 'مشكلة في الاتصال. يرجى التحقق من الإنترنت والمحاولة مرة أخرى.';
            }
          }
          break;
      }

      setError(errorMessage);
      toast({
        title: t('errorTitle'),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Pre-signup step content
  const renderPreSignupContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-center">
                <UserCheck className="h-5 w-5" />
                {t('selectAccountType')}
              </CardTitle>
              <CardDescription className="text-center">
                {t('chooseYourAccountType')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label className="text-base font-medium">{t('userType')} *</Label>
                <RadioGroup
                  value={userType || ''}
                  onValueChange={(value: string) => setUserType(value as UserType)}
                  className="grid grid-cols-1 gap-4 mt-4"
                  disabled={isLoading}
                >
                  <div className="flex items-center space-x-3 rtl:space-x-reverse p-4 border rounded-lg hover:bg-muted/50">
                    <RadioGroupItem value="customer" id="customer" />
                    <div className="flex-1">
                      <Label htmlFor="customer" className="text-base font-medium cursor-pointer">
                        {t('customer')}
                      </Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        {t('customerDescription')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse p-4 border rounded-lg hover:bg-muted/50">
                    <RadioGroupItem value="merchant" id="merchant" />
                    <div className="flex-1">
                      <Label htmlFor="merchant" className="text-base font-medium cursor-pointer">
                        {t('merchant')}
                      </Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        {t('merchantDescription')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse p-4 border rounded-lg hover:bg-muted/50">
                    <RadioGroupItem value="representative" id="representative" />
                    <div className="flex-1">
                      <Label htmlFor="representative" className="text-base font-medium cursor-pointer">
                        {t('representative')}
                      </Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        {t('representativeDescription')}
                      </p>
                    </div>
                  </div>
                </RadioGroup>
              </div>
            </CardContent>
          </Card>
        );

      case 2:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-center">
                <FileText className="h-5 w-5" />
                {t('termsAndConditions')}
              </CardTitle>
              <CardDescription className="text-center">
                {t('reviewAndAcceptTerms')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border rounded-lg p-4 max-h-60 overflow-y-auto bg-muted/50">
                <h4 className="font-semibold mb-2">{t('termsAndConditionsTitle')}</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  {t('termsAndConditionsIntro')}
                </p>
                <div className="space-y-2 text-sm">
                  <p><strong>1. {t('userRights')}</strong></p>
                  <p><strong>2. {t('userResponsibilities')}</strong></p>
                  <p><strong>3. {t('privacyPolicy')}</strong></p>
                  <p><strong>4. {t('serviceTerms')}</strong></p>
                  <p><strong>5. {t('paymentTerms')}</strong></p>
                </div>
              </div>

              <div className="flex items-center space-x-2 rtl:space-x-reverse justify-center">
                <Checkbox
                  id="terms"
                  checked={agreedToTerms}
                  onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
                  disabled={isLoading}
                  className="mt-1"
                />
                <div className="text-sm leading-normal">
                  {t('iAgreeToThe')}{' '}
                  <Button
                    type="button"
                    variant="link"
                    className="p-0 h-auto text-primary hover:text-primary/80 underline"
                    onClick={() => setIsTermsModalOpen(true)}
                    disabled={isLoading}
                  >
                    {t('termsAndConditions')}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 3:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-center">
                <User className="h-5 w-5" />
                {t('readyToStart')}
              </CardTitle>
              <CardDescription className="text-center">
                {t('proceedToCreateAccount')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 text-center">
              <div className="p-6 bg-muted/50 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">
                  {userType === 'customer' ? t('customer') :
                   userType === 'merchant' ? t('merchant') :
                   t('representative')}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {userType === 'customer' ? t('customerAccountSummary') :
                   userType === 'merchant' ? t('merchantAccountSummary') :
                   t('representativeAccountSummary')}
                </p>
              </div>
              <p className="text-sm text-muted-foreground">
                {t('clickNextToProceed')}
              </p>
            </CardContent>
          </Card>
        );

      default:
        return <div>Step {currentStep} content</div>;
    }
  };

  // Effect to handle step skipping for non-merchants
  useEffect(() => {
    if (showDetailedForm && detailedFormStep === 3 && userType !== 'merchant') {
      // تخطي خطوة المستندات للعملاء والمندوبين
      console.log('Skipping documents step for non-merchant user type:', userType);
      setDetailedFormStep(4);
    }
  }, [showDetailedForm, detailedFormStep, userType]);

  // Detailed form step content
  const renderDetailedFormContent = () => {
    switch (detailedFormStep) {
      case 1:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-center">
                <User className="h-5 w-5" />
                {t('basicInformation')}
              </CardTitle>
              <CardDescription className="text-center">
                {t('enterBasicInformation')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="username">{t('username')} *</Label>
                <div className="relative">
                  <Input
                    id="username"
                    name="username"
                    type="text"
                    autoComplete="username"
                    required
                    className="mt-1"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    disabled={isLoading || isCheckingUsername}
                    placeholder={t('enterUsername')}
                  />
                  {isCheckingUsername && (
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    </div>
                  )}
                </div>
                {isCheckingUsername && (
                  <p className="text-sm text-muted-foreground mt-1 flex items-center gap-1">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    جاري التحقق من توفر اسم المستخدم...
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="email">{t('emailAddress')} *</Label>
                <div className="relative">
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    className="mt-1"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading || isCheckingEmail}
                    placeholder={t('enterEmail')}
                  />
                  {isCheckingEmail && (
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    </div>
                  )}
                </div>
                {isCheckingEmail && (
                  <p className="text-sm text-muted-foreground mt-1 flex items-center gap-1">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    جاري التحقق من توفر البريد الإلكتروني...
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        );

      case 2:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-center">
                <Lock className="h-5 w-5" />
                {t('passwordSetup')}
              </CardTitle>
              <CardDescription className="text-center">
                {t('createSecurePassword')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="password">{t('password')} *</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  className="mt-1"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading}
                  placeholder={t('enterPassword')}
                />
                <p className="text-xs text-muted-foreground mt-1">{t('passwordLengthHint')}</p>
              </div>
              <div>
                <Label htmlFor="confirm-password">{t('confirmPassword')} *</Label>
                <Input
                  id="confirm-password"
                  name="confirm-password"
                  type="password"
                  autoComplete="new-password"
                  required
                  className="mt-1"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  disabled={isLoading}
                  placeholder={t('confirmYourPassword')}
                />
              </div>
            </CardContent>
          </Card>
        );

      case 3:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-center">
                <FileText className="h-5 w-5" />
                {t('documentsUpload')}
              </CardTitle>
              <CardDescription className="text-center">
                {t('uploadRequiredDocuments')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-1">
                <Label htmlFor="commercial-registration">{t('commercialRegistration')} *</Label>
                <Input
                  id="commercial-registration"
                  type="file"
                  onChange={(e) => handleFileChange(e, 'commercial')}
                  className="mt-1"
                  accept=".pdf,.jpg,.jpeg,.png"
                  disabled={isLoading}
                  required
                />
                <p className="text-xs text-muted-foreground">{t('fileFormatsAllowed')}</p>
                {filePreview.commercial && (
                  <div className="mt-2">
                    <p className="text-sm text-green-600">{t('fileSelected')}</p>
                  </div>
                )}
              </div>

              <div className="space-y-1">
                <Label htmlFor="freelance-document">{t('freelanceDocument')} *</Label>
                <Input
                  id="freelance-document"
                  type="file"
                  onChange={(e) => handleFileChange(e, 'freelance')}
                  className="mt-1"
                  accept=".pdf,.jpg,.jpeg,.png"
                  disabled={isLoading}
                  required
                />
                <p className="text-xs text-muted-foreground">{t('fileFormatsAllowed')}</p>
                {filePreview.freelance && (
                  <div className="mt-2">
                    <p className="text-sm text-green-600">{t('fileSelected')}</p>
                  </div>
                )}
              </div>

              <div className="space-y-1">
                <Label htmlFor="other-licenses">{t('otherLicenses')}</Label>
                <Input
                  id="other-licenses"
                  type="file"
                  onChange={(e) => handleFileChange(e, 'other')}
                  className="mt-1"
                  accept=".pdf,.jpg,.jpeg,.png"
                  disabled={isLoading}
                />
                <p className="text-xs text-muted-foreground">{t('optionalFileFormatsAllowed')}</p>
                {filePreview.other && (
                  <div className="mt-2">
                    <p className="text-sm text-green-600">{t('fileSelected')}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );

      case 4:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-center">
                <FileText className="h-5 w-5" />
                {t('finalStep')}
              </CardTitle>
              <CardDescription className="text-center">
                {t('reviewYourInformation')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-muted/50 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">{t('accountType')}:</span>
                  <span>{userType === 'customer' ? t('customer') :
                         userType === 'merchant' ? t('merchant') :
                         t('representative')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">{t('username')}:</span>
                  <span>{username}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">{t('emailAddress')}:</span>
                  <span>{email}</span>
                </div>
                {userType === 'merchant' && (
                  <>
                    <div className="flex justify-between">
                      <span className="font-medium">{t('commercialRegistration')}:</span>
                      <span className="text-green-600">{commercialRegistrationFile ? t('uploaded') : t('notUploaded')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">{t('freelanceDocument')}:</span>
                      <span className="text-green-600">{freelanceDocumentFile ? t('uploaded') : t('notUploaded')}</span>
                    </div>
                  </>
                )}
              </div>
              <p className="text-sm text-muted-foreground text-center">
                {t('clickCreateToFinish')}
              </p>
            </CardContent>
          </Card>
        );

      default:
        return <div>Detailed Step {detailedFormStep} content</div>;
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto space-y-6">
      {/* Progress indicator */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">
            {showDetailedForm ? t('step') : t('step')} {showDetailedForm ? detailedFormStep : currentStep} {t('of')} {showDetailedForm ? (userType === 'merchant' ? detailedFormTotalSteps : detailedFormTotalSteps - 1) : totalSteps}
          </span>
          <span className="text-sm text-muted-foreground">
            {showDetailedForm ? (
              Math.round((detailedFormStep / (userType === 'merchant' ? detailedFormTotalSteps : detailedFormTotalSteps - 1)) * 100)
            ) : (
              Math.round((currentStep / totalSteps) * 100)
            )}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{
              width: showDetailedForm
                ? `${(detailedFormStep / (userType === 'merchant' ? detailedFormTotalSteps : detailedFormTotalSteps - 1)) * 100}%`
                : `${(currentStep / totalSteps) * 100}%`
            }}
          />
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {showDetailedForm ? renderDetailedFormContent() : renderPreSignupContent()}

        {error && (
          <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-sm text-destructive text-center">{error}</p>
          </div>
        )}

        {/* Navigation buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={showDetailedForm ? prevDetailedStep : prevStep}
            disabled={(!showDetailedForm && currentStep === 1) || isLoading}
          >
            {t('previous')}
          </Button>

          {showDetailedForm ? (
            detailedFormStep < (userType === 'merchant' ? detailedFormTotalSteps : detailedFormTotalSteps - 1) ? (
              <Button
                type="button"
                onClick={nextDetailedStep}
                disabled={isLoading || isCheckingEmail || isCheckingUsername}
              >
                {(isCheckingEmail || isCheckingUsername) && detailedFormStep === 1 && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {(isCheckingEmail || isCheckingUsername) && detailedFormStep === 1 ?
                  (isCheckingUsername ? t('checkingUsername') : t('checkingEmail')) :
                  t('next')
                }
              </Button>
            ) : (
              <Button type="submit" disabled={isLoading || isCheckingEmail || isCheckingUsername}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t('createAccount')}
              </Button>
            )
          ) : (
            <Button
              type="button"
              onClick={nextStep}
              disabled={isLoading || isCheckingEmail}
            >
              {currentStep === totalSteps ? t('startRegistration') : t('next')}
            </Button>
          )}
        </div>
      </form>

      <TermsAndConditionsModal
        isOpen={isTermsModalOpen}
        onClose={() => setIsTermsModalOpen(false)}
        userType={userType || undefined}
        locale={locale}
      />
    </div>
  );
}
