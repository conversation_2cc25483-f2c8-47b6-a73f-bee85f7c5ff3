# 🌐 دليل النشر على Netlify - مِخْلاة

## 🚀 **إعدادات النشر المطلوبة**

### **1. Build Settings**
```
Build command: npm run setup-ai-models && bun run build
Publish directory: .next
Functions directory: netlify/functions
```

### **2. Environment Variables**
أضف هذه المتغيرات في Netlify Dashboard:

```bash
# الذكاء الاصطناعي المحلي
USE_LOCAL_AI=true
ENCRYPT_EXTERNAL=false
AUDIT_LOGGING=true
NETLIFY_DEPLOYMENT=true

# إعدادات النماذج
AI_MODELS_PATH=/ai-models
AI_LOCAL_ONLY=true
AI_SECURITY_LEVEL=high

# إعدادات الأداء
AI_MAX_CONCURRENT_ANALYSIS=3
AI_ANALYSIS_TIMEOUT=30000
AI_CACHE_ENABLED=true
NODE_OPTIONS=--max-old-space-size=4096
NEXT_TELEMETRY_DISABLED=1

# Firebase (استخدم قيمك الحقيقية)
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id

# Cloudinary (استخدم قيمك الحقيقية)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your-cloud-name
NEXT_PUBLIC_CLOUDINARY_API_KEY=your-api-key
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=your-preset
```

## 📋 **خطوات النشر**

### **الخطوة 1: تحضير المشروع**
```bash
# تأكد من وجود جميع الملفات
git add .
git commit -m "إضافة نظام الذكاء الاصطناعي المحلي"
git push origin main
```

### **الخطوة 2: ربط المشروع بـ Netlify**
1. اذهب إلى [Netlify Dashboard](https://app.netlify.com)
2. اضغط "New site from Git"
3. اختر مستودع GitHub الخاص بك
4. اختر branch: `main` أو `master`

### **الخطوة 3: تكوين الإعدادات**
1. **Build command**: `npm run setup-ai-models && bun run build`
2. **Publish directory**: `.next`
3. **Functions directory**: `netlify/functions`

### **الخطوة 4: إضافة متغيرات البيئة**
1. اذهب إلى Site settings > Environment variables
2. أضف جميع المتغيرات المذكورة أعلاه
3. احفظ الإعدادات

### **الخطوة 5: النشر**
1. اضغط "Deploy site"
2. انتظر اكتمال البناء (5-10 دقائق)
3. تحقق من عمل الموقع

## ✅ **التحقق من النشر**

### **1. فحص النماذج المحلية**
زر الرابط: `https://your-site.netlify.app/ai-models/models/`
يجب أن ترى قائمة بالنماذج المحملة.

### **2. فحص النظام**
1. اذهب إلى `/admin/ai-dashboard`
2. تحقق من عمل النظام المحلي
3. اختبر تحليل مستند

### **3. فحص الأداء**
- سرعة التحميل: أقل من 3 ثواني
- تحليل المستندات: 0.5-1 ثانية
- لا أخطاء في Console

## 🔧 **استكشاف الأخطاء**

### **خطأ في البناء**
```bash
# إذا فشل البناء، تحقق من:
1. وجود ملف netlify.toml
2. صحة متغيرات البيئة
3. حجم المشروع (أقل من 500MB)
```

### **خطأ في النماذج**
```bash
# إذا لم تعمل النماذج:
1. تحقق من مسار /ai-models/
2. تأكد من تشغيل setup-ai-models
3. فحص سجلات البناء
```

### **خطأ في الأداء**
```bash
# إذا كان الموقع بطيء:
1. تحقق من NODE_OPTIONS
2. فعل التخزين المؤقت
3. تحسين حجم النماذج
```

## 📊 **مؤشرات النجاح**

### **البناء الناجح**
- ✅ Build time: 5-10 دقائق
- ✅ Deploy size: ~150-200MB
- ✅ Functions: متاحة
- ✅ لا أخطاء في البناء

### **الأداء المتوقع**
- ✅ Page load: < 3 ثواني
- ✅ AI analysis: 0.5-1 ثانية
- ✅ Memory usage: < 512MB
- ✅ CPU usage: متوسط

## 🎯 **الميزات المتاحة بعد النشر**

### **النظام المحلي**
- 🤖 تحليل مستندات محلي
- 🔒 خصوصية كاملة
- ⚡ سرعة عالية
- 💰 تكلفة صفر

### **الأمان**
- 🛡️ لا إرسال بيانات خارجية
- 🔐 تشفير محلي
- 📝 سجلات مراجعة
- 🚨 تنبيهات أمنية

## 📞 **الدعم**

### **في حالة المشاكل:**
1. تحقق من سجلات Netlify
2. راجع متغيرات البيئة
3. تأكد من صحة الإعدادات
4. اتصل بالدعم التقني

### **روابط مفيدة:**
- [Netlify Docs](https://docs.netlify.com)
- [Next.js on Netlify](https://docs.netlify.com/frameworks/next-js/)
- [Environment Variables](https://docs.netlify.com/environment-variables/)

## 🎉 **مبروك!**
موقعك الآن يعمل على Netlify مع نظام ذكاء اصطناعي محلي بالكامل!
