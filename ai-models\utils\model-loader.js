// محمل النماذج الذكي المتقدم - خصوصية 100%
import { PrivacyGuardian } from './privacy-guardian.js';
import { MemoryManager } from './memory-manager.js';
import { CacheManager } from './cache-manager.js';

export class ModelLoader {
  constructor() {
    this.models = new Map();
    this.loadingPromises = new Map();
    this.config = null;
    this.privacyGuardian = new PrivacyGuardian();
    this.memoryManager = new MemoryManager();
    this.cacheManager = new CacheManager();
    this.downloadProgress = new Map();
    this.initialized = false;
  }

  /**
   * تهيئة محمل النماذج
   */
  async initialize() {
    try {
      console.log('🚀 تهيئة محمل النماذج الذكي...');
      
      // تحميل التكوين
      this.config = await this.loadConfig();
      
      // تهيئة حارس الخصوصية
      await this.privacyGuardian.initialize();
      
      // تهيئة مدير الذاكرة
      await this.memoryManager.initialize(this.config.performance);
      
      // تهيئة مدير التخزين المؤقت
      await this.cacheManager.initialize(this.config.performance.cache);
      
      // تحميل النماذج الأساسية
      await this.loadEssentialModels();
      
      this.initialized = true;
      console.log('✅ تم تهيئة محمل النماذج بنجاح');
      
    } catch (error) {
      console.error('❌ خطأ في تهيئة محمل النماذج:', error);
      throw error;
    }
  }

  /**
   * تحميل التكوين
   */
  async loadConfig() {
    try {
      const [modelsConfig, performanceConfig, privacyConfig] = await Promise.all([
        fetch('./ai-models/configs/local-models-config.json').then(r => r.json()),
        fetch('./ai-models/configs/performance-config.json').then(r => r.json()),
        fetch('./ai-models/configs/privacy-config.json').then(r => r.json())
      ]);

      return {
        models: modelsConfig,
        performance: performanceConfig,
        privacy: privacyConfig
      };
    } catch (error) {
      console.error('❌ خطأ في تحميل التكوين:', error);
      throw new Error('فشل في تحميل تكوين النماذج');
    }
  }

  /**
   * تحميل النماذج الأساسية
   */
  async loadEssentialModels() {
    console.log('📦 تحميل النماذج الأساسية...');
    
    const essentialModels = this.config.models.models.essential;
    const loadPromises = [];

    for (const [modelId, modelConfig] of Object.entries(essentialModels)) {
      if (modelConfig.loadOnStartup) {
        loadPromises.push(this.loadModel(modelId, modelConfig));
      }
    }

    try {
      await Promise.all(loadPromises);
      console.log('✅ تم تحميل جميع النماذج الأساسية');
    } catch (error) {
      console.error('❌ خطأ في تحميل النماذج الأساسية:', error);
      throw error;
    }
  }

  /**
   * تحميل نموذج محدد
   */
  async loadModel(modelId, modelConfig = null) {
    // التحقق من وجود النموذج في الذاكرة
    if (this.models.has(modelId)) {
      return this.models.get(modelId);
    }

    // التحقق من وجود عملية تحميل جارية
    if (this.loadingPromises.has(modelId)) {
      return this.loadingPromises.get(modelId);
    }

    // إنشاء promise للتحميل
    const loadingPromise = this._loadModelInternal(modelId, modelConfig);
    this.loadingPromises.set(modelId, loadingPromise);

    try {
      const model = await loadingPromise;
      this.models.set(modelId, model);
      this.loadingPromises.delete(modelId);
      return model;
    } catch (error) {
      this.loadingPromises.delete(modelId);
      throw error;
    }
  }

  /**
   * التحميل الداخلي للنموذج
   */
  async _loadModelInternal(modelId, modelConfig) {
    try {
      console.log(`📥 تحميل النموذج: ${modelId}`);
      
      // الحصول على تكوين النموذج
      if (!modelConfig) {
        modelConfig = this.getModelConfig(modelId);
      }

      // التحقق من الذاكرة المتاحة
      await this.memoryManager.checkMemoryAvailability(modelConfig.compressedSize);

      // تحميل النموذج من التخزين المؤقت أو التحميل
      let modelData = await this.cacheManager.get(modelId);
      
      if (!modelData) {
        modelData = await this.downloadModel(modelId, modelConfig);
        await this.cacheManager.set(modelId, modelData);
      }

      // تهيئة النموذج حسب النوع
      const model = await this.initializeModel(modelId, modelConfig, modelData);
      
      // تسجيل التحميل في سجل الخصوصية
      this.privacyGuardian.logModelLoad(modelId, {
        type: modelConfig.type,
        size: modelConfig.compressedSize,
        processingLocation: 'local_browser',
        privacySafe: true
      });

      console.log(`✅ تم تحميل النموذج: ${modelId}`);
      return model;

    } catch (error) {
      console.error(`❌ خطأ في تحميل النموذج ${modelId}:`, error);
      throw error;
    }
  }

  /**
   * تحميل النموذج من الخادم
   */
  async downloadModel(modelId, modelConfig) {
    const urls = this.config.models.download.cdnUrls.map(baseUrl => 
      baseUrl + modelConfig.compressedPath.replace('./', '')
    );

    let lastError;
    
    for (let attempt = 0; attempt < this.config.models.download.retryAttempts; attempt++) {
      for (const url of urls) {
        try {
          console.log(`📡 محاولة تحميل من: ${url}`);
          
          const response = await fetch(url, {
            headers: {
              'Accept-Encoding': 'br, gzip, deflate'
            }
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          // تتبع تقدم التحميل
          const contentLength = response.headers.get('content-length');
          if (contentLength && this.config.models.download.progressTracking) {
            this.trackDownloadProgress(modelId, response, parseInt(contentLength));
          }

          const arrayBuffer = await response.arrayBuffer();
          
          // فك الضغط إذا لزم الأمر
          const modelData = await this.decompressModel(arrayBuffer, modelConfig);
          
          console.log(`✅ تم تحميل النموذج ${modelId} بنجاح`);
          return modelData;

        } catch (error) {
          console.warn(`⚠️ فشل التحميل من ${url}:`, error.message);
          lastError = error;
        }
      }

      // انتظار قبل إعادة المحاولة
      if (attempt < this.config.models.download.retryAttempts - 1) {
        await this.delay(this.config.models.download.retryDelay * (attempt + 1));
      }
    }

    throw new Error(`فشل في تحميل النموذج ${modelId} بعد ${this.config.models.download.retryAttempts} محاولات: ${lastError.message}`);
  }

  /**
   * تتبع تقدم التحميل
   */
  trackDownloadProgress(modelId, response, totalSize) {
    const reader = response.body.getReader();
    let receivedSize = 0;

    const progressInterval = setInterval(() => {
      const progress = (receivedSize / totalSize) * 100;
      this.downloadProgress.set(modelId, {
        progress: Math.round(progress),
        receivedSize,
        totalSize
      });
      
      console.log(`📊 تقدم تحميل ${modelId}: ${Math.round(progress)}%`);
    }, 1000);

    // تنظيف المؤقت عند الانتهاء
    response.finally(() => {
      clearInterval(progressInterval);
      this.downloadProgress.delete(modelId);
    });
  }

  /**
   * فك ضغط النموذج
   */
  async decompressModel(compressedData, modelConfig) {
    if (!this.config.models.download.compressionEnabled) {
      return compressedData;
    }

    try {
      // فك ضغط Brotli
      if (modelConfig.compressedPath.endsWith('.br')) {
        const decompressed = await this.decompressBrotli(compressedData);
        return decompressed;
      }
      
      return compressedData;
    } catch (error) {
      console.warn('⚠️ فشل في فك الضغط، استخدام البيانات كما هي:', error);
      return compressedData;
    }
  }

  /**
   * فك ضغط Brotli
   */
  async decompressBrotli(compressedData) {
    // استخدام مكتبة فك ضغط Brotli
    const { decompress } = await import('brotli-wasm');
    return decompress(new Uint8Array(compressedData));
  }

  /**
   * تهيئة النموذج حسب النوع
   */
  async initializeModel(modelId, modelConfig, modelData) {
    switch (modelConfig.type) {
      case 'ocr':
        return this.initializeOCRModel(modelId, modelConfig, modelData);
      case 'classification':
      case 'nlp':
      case 'validation':
      case 'fraud_detection':
        return this.initializeONNXModel(modelId, modelConfig, modelData);
      default:
        throw new Error(`نوع نموذج غير مدعوم: ${modelConfig.type}`);
    }
  }

  /**
   * تهيئة نموذج OCR
   */
  async initializeOCRModel(modelId, modelConfig, modelData) {
    const { createWorker } = await import('tesseract.js');
    
    const worker = await createWorker({
      logger: m => console.log(`OCR ${modelId}:`, m)
    });

    await worker.loadLanguage('ara+eng');
    await worker.initialize('ara+eng');
    
    return {
      type: 'ocr',
      worker,
      config: modelConfig,
      recognize: async (imageData) => {
        const result = await worker.recognize(imageData);
        return result;
      }
    };
  }

  /**
   * تهيئة نموذج ONNX
   */
  async initializeONNXModel(modelId, modelConfig, modelData) {
    const ort = await import('onnxruntime-web');
    
    const session = await ort.InferenceSession.create(modelData, {
      executionProviders: ['webgl', 'cpu'],
      graphOptimizationLevel: 'all'
    });

    return {
      type: 'onnx',
      session,
      config: modelConfig,
      run: async (inputs) => {
        const results = await session.run(inputs);
        return results;
      }
    };
  }

  /**
   * الحصول على تكوين النموذج
   */
  getModelConfig(modelId) {
    const allModels = {
      ...this.config.models.models.essential,
      ...this.config.models.models.advanced,
      ...this.config.models.models.specialized
    };

    const modelConfig = allModels[modelId];
    if (!modelConfig) {
      throw new Error(`تكوين النموذج غير موجود: ${modelId}`);
    }

    return modelConfig;
  }

  /**
   * الحصول على نموذج
   */
  async getModel(modelId) {
    if (!this.initialized) {
      await this.initialize();
    }

    if (this.models.has(modelId)) {
      return this.models.get(modelId);
    }

    // تحميل النموذج عند الطلب
    const modelConfig = this.getModelConfig(modelId);
    return await this.loadModel(modelId, modelConfig);
  }

  /**
   * تفريغ نموذج من الذاكرة
   */
  async unloadModel(modelId) {
    if (this.models.has(modelId)) {
      const model = this.models.get(modelId);
      
      // تنظيف الموارد
      if (model.worker) {
        await model.worker.terminate();
      }
      if (model.session) {
        await model.session.release();
      }

      this.models.delete(modelId);
      this.memoryManager.releaseMemory(modelId);
      
      console.log(`🗑️ تم تفريغ النموذج: ${modelId}`);
    }
  }

  /**
   * تنظيف جميع النماذج
   */
  async cleanup() {
    console.log('🧹 تنظيف جميع النماذج...');
    
    const unloadPromises = Array.from(this.models.keys()).map(modelId => 
      this.unloadModel(modelId)
    );

    await Promise.all(unloadPromises);
    
    await this.cacheManager.clear();
    await this.memoryManager.cleanup();
    
    console.log('✅ تم تنظيف جميع النماذج');
  }

  /**
   * الحصول على إحصائيات النماذج
   */
  getStats() {
    return {
      loadedModels: this.models.size,
      memoryUsage: this.memoryManager.getUsage(),
      cacheStats: this.cacheManager.getStats(),
      downloadProgress: Object.fromEntries(this.downloadProgress)
    };
  }

  /**
   * تأخير
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// تصدير مثيل واحد
export const modelLoader = new ModelLoader();
