@echo off
echo.
echo ========================================
echo 🔬 اختبارات نظام التحليلات المتقدمة
echo ========================================
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت
    echo يرجى تثبيت Node.js أولاً
    pause
    exit /b 1
)

REM التحقق من وجود المشروع
if not exist "..\..\package.json" (
    echo ❌ خطأ: لم يتم العثور على package.json
    echo تأكد من تشغيل الأمر في مجلد المشروع الصحيح
    pause
    exit /b 1
)

echo 📋 الخيارات المتاحة:
echo.
echo 1. تشغيل جميع اختبارات التحليلات
echo 2. اختبارات الأداء فقط
echo 3. اختبارات التكامل فقط
echo 4. فتح واجهة Cypress التفاعلية
echo 5. تنظيف النتائج القديمة
echo 6. عرض المساعدة
echo 0. خروج
echo.

set /p choice="اختر رقم الخيار: "

if "%choice%"=="1" goto run_all
if "%choice%"=="2" goto run_performance
if "%choice%"=="3" goto run_integration
if "%choice%"=="4" goto open_cypress
if "%choice%"=="5" goto clean_results
if "%choice%"=="6" goto show_help
if "%choice%"=="0" goto exit
goto invalid_choice

:run_all
echo.
echo 🚀 تشغيل جميع اختبارات التحليلات...
echo.
cd ..\..
node cypress/scripts/run-analytics-tests.js --all --clean --report
goto end

:run_performance
echo.
echo ⚡ تشغيل اختبارات الأداء...
echo.
cd ..\..
node cypress/scripts/run-analytics-tests.js --performance --clean
goto end

:run_integration
echo.
echo 🔗 تشغيل اختبارات التكامل...
echo.
cd ..\..
node cypress/scripts/run-analytics-tests.js --integration --clean
goto end

:open_cypress
echo.
echo 🖥️ فتح واجهة Cypress التفاعلية...
echo.
cd ..\..
npx cypress open --config specPattern="cypress/e2e/advanced-analytics-*.cy.ts,cypress/e2e/analytics-*.cy.ts"
goto end

:clean_results
echo.
echo 🧹 تنظيف النتائج القديمة...
echo.
if exist "..\results" rmdir /s /q "..\results"
if exist "..\screenshots" rmdir /s /q "..\screenshots"
if exist "..\videos" rmdir /s /q "..\videos"
echo ✅ تم تنظيف النتائج القديمة
goto menu

:show_help
echo.
echo 📖 مساعدة اختبارات التحليلات
echo ================================
echo.
echo هذا السكريبت يساعدك في تشغيل اختبارات نظام التحليلات المتقدمة
echo.
echo الاختبارات المتاحة:
echo - اختبارات شاملة: تشمل جميع جوانب النظام
echo - اختبارات الأداء: تركز على سرعة التحميل والاستجابة
echo - اختبارات التكامل: تختبر التكامل بين الخدمات
echo.
echo النتائج:
echo - التقارير: cypress/results/html/index.html
echo - لقطات الشاشة: cypress/screenshots/
echo - مقاطع الفيديو: cypress/videos/
echo.
goto menu

:invalid_choice
echo.
echo ❌ خيار غير صحيح. يرجى اختيار رقم من 0 إلى 6
echo.
goto menu

:menu
echo.
echo اضغط أي مفتاح للعودة إلى القائمة الرئيسية...
pause >nul
cls
goto start

:end
echo.
echo ✅ انتهت العملية
echo.
if exist "..\results\html\index.html" (
    echo 📊 تقرير HTML متوفر في: cypress\results\html\index.html
    set /p open_report="هل تريد فتح التقرير؟ (y/n): "
    if /i "%open_report%"=="y" start ..\results\html\index.html
)
echo.
pause

:exit
echo.
echo 👋 شكراً لاستخدام اختبارات التحليلات المتقدمة
echo.
exit /b 0

:start
cls
goto :eof
