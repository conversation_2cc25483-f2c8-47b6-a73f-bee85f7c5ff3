const fs = require('fs');
const path = require('path');

// مسارات الملفات
const AR_TRANSLATIONS_PATH = path.join(__dirname, '..', 'src', 'locales', 'ar.json');
const EN_TRANSLATIONS_PATH = path.join(__dirname, '..', 'src', 'locales', 'en.json');

/**
 * إنشاء نسخة احتياطية من الملف
 */
function createBackup(filePath) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = filePath.replace('.json', `_backup_${timestamp}.json`);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    fs.writeFileSync(backupPath, content, 'utf8');
    console.log(`✅ تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);
    return backupPath;
  } catch (error) {
    console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error.message);
    return null;
  }
}

/**
 * الترجمات الأساسية المفقودة للإنجليزية
 */
const essentialEnglishTranslations = {
  // ترجمات أساسية
  "representative": "Representative",
  "sar": "SAR",
  "status": "Status",
  "processing": "Processing",
  "cancel": "Cancel",
  "loading": "Loading...",
  "close": "Close",
  "actions": "Actions",
  "category": "Category",
  "price": "Price",
  "stock": "Stock",
  "active": "Active",
  "inactive": "Inactive",
  "delete": "Delete",
  "back": "Back",
  "next": "Next",
  "previous": "Previous",
  "search": "Search",
  "filter": "Filter",
  
  // ترجمات المتاجر
  "store": "Store",
  "available": "Available",
  "outOfStock": "Out of Stock",
  "address": "Address",
  "businessHours": "Business Hours",
  "totalOrders": "Total Orders",
  "productNotFound": "Product Not Found",
  "storeNotFound": "Store Not Found",
  "description": "Description",
  "moderationNotes": "Moderation Notes",
  
  // ترجمات التفاعل
  "follow": "Follow",
  "share": "Share",
  "aboutStore": "About Store",
  "noDescriptionAvailable": "No description available",
  "trending": "Trending",
  "location": "Location",
  "errorFetchingProduct": "Error fetching product",
  "inStock": "In Stock",
  
  // ترجمات المندوبين الأساسية
  "representative.dashboard": "Representative Dashboard",
  "representative.orders": "Orders",
  "representative.earnings": "Earnings",
  "representative.profile": "Profile",
  "representative.acceptOrder": "Accept Order",
  "representative.rejectOrder": "Reject Order",
  "representative.completeOrder": "Complete Order",
  "representative.totalEarnings": "Total Earnings",
  "representative.todayEarnings": "Today's Earnings",
  "representative.pendingOrders": "Pending Orders",
  "representative.completedOrders": "Completed Orders",
  
  // ترجمات التقييمات
  "reviews": "Reviews",
  "rating": "Rating",
  "writeReview": "Write Review",
  "submitReview": "Submit Review",
  "reviewSubmitted": "Review Submitted",
  "thankYouForReview": "Thank you for your review",
  
  // ترجمات الطلبات
  "orderDetails": "Order Details",
  "orderHistory": "Order History",
  "trackOrder": "Track Order",
  "orderStatus": "Order Status",
  "orderTotal": "Order Total",
  "orderDate": "Order Date",
  
  // ترجمات الدفع
  "payment": "Payment",
  "paymentMethod": "Payment Method",
  "paymentStatus": "Payment Status",
  "paymentSuccessful": "Payment Successful",
  "paymentFailed": "Payment Failed",
  
  // ترجمات الشحن
  "shipping": "Shipping",
  "shippingAddress": "Shipping Address",
  "shippingMethod": "Shipping Method",
  "shippingCost": "Shipping Cost",
  "freeShipping": "Free Shipping",
  
  // ترجمات الإعدادات
  "settings": "Settings",
  "accountSettings": "Account Settings",
  "privacySettings": "Privacy Settings",
  "notificationSettings": "Notification Settings",
  
  // ترجمات الأخطاء
  "error": "Error",
  "errorOccurred": "An error occurred",
  "tryAgain": "Try Again",
  "somethingWentWrong": "Something went wrong",
  
  // ترجمات التنقل
  "navigation": "Navigation",
  "menu": "Menu",
  "dashboard": "Dashboard",
  "products": "Products",
  "orders": "Orders",
  "customers": "Customers",
  "reports": "Reports",
  "analytics": "Analytics"
};

/**
 * الترجمات الأساسية المفقودة للعربية
 */
const essentialArabicTranslations = {
  // ترجمات التقييمات المتقدمة
  "reviews.reportReview": "الإبلاغ عن المراجعة",
  "reviews.reportReviewDescription": "إذا كانت هذه المراجعة تنتهك قواعد المجتمع، يرجى الإبلاغ عنها",
  "reviews.reportReason": "سبب الإبلاغ",
  "reviews.additionalDetails": "تفاصيل إضافية",
  "reviews.reportDescriptionPlaceholder": "اكتب تفاصيل إضافية حول سبب الإبلاغ...",
  "reviews.reportWarningTitle": "تحذير",
  "reviews.reportWarningDescription": "الإبلاغ الكاذب قد يؤدي إلى تعليق حسابك",
  "reviews.submittingReport": "جاري إرسال البلاغ...",
  "reviews.submitReport": "إرسال البلاغ",
  
  // أسباب الإبلاغ
  "reviews.reportReasons.spam": "رسائل مزعجة",
  "reviews.reportReasons.inappropriate": "محتوى غير مناسب",
  "reviews.reportReasons.fake": "مراجعة مزيفة",
  "reviews.reportReasons.offensive": "محتوى مسيء",
  "reviews.reportReasons.other": "أخرى",
  
  // ترجمات الملف الشخصي المتقدمة
  "profile.editProfile": "تعديل الملف الشخصي",
  "profile.changePassword": "تغيير كلمة المرور",
  "profile.deleteAccount": "حذف الحساب",
  "profile.accountVerification": "التحقق من الحساب",
  "profile.verificationPending": "التحقق قيد الانتظار",
  "profile.verificationCompleted": "تم التحقق بنجاح",
  
  // ترجمات الموقع والخرائط
  "location.currentLocation": "الموقع الحالي",
  "location.detectLocation": "تحديد الموقع",
  "location.locationPermission": "إذن الوصول للموقع",
  "location.locationNotAvailable": "الموقع غير متاح",
  "location.nearbyStores": "المتاجر القريبة",
  
  // ترجمات الإشعارات المتقدمة
  "notifications.markAllAsRead": "تحديد الكل كمقروء",
  "notifications.deleteAll": "حذف جميع الإشعارات",
  "notifications.notificationSettings": "إعدادات الإشعارات",
  "notifications.pushNotifications": "الإشعارات الفورية",
  "notifications.emailNotifications": "إشعارات البريد الإلكتروني",
  
  // ترجمات الدعم
  "support.contactSupport": "اتصل بالدعم",
  "support.helpCenter": "مركز المساعدة",
  "support.faq": "الأسئلة الشائعة",
  "support.reportIssue": "الإبلاغ عن مشكلة",
  "support.technicalSupport": "الدعم التقني",
  
  // ترجمات الأمان
  "security.twoFactorAuth": "المصادقة الثنائية",
  "security.loginHistory": "تاريخ تسجيل الدخول",
  "security.securitySettings": "إعدادات الأمان",
  "security.changePassword": "تغيير كلمة المرور",
  "security.logoutAllDevices": "تسجيل الخروج من جميع الأجهزة"
};

/**
 * إضافة الترجمات المفقودة لملف معين
 */
function addMissingTranslations(filePath, newTranslations, language) {
  try {
    console.log(`🔧 إضافة الترجمات المفقودة للغة ${language}...`);
    
    // إنشاء نسخة احتياطية
    const backupPath = createBackup(filePath);
    if (!backupPath) {
      throw new Error('فشل في إنشاء النسخة الاحتياطية');
    }

    // قراءة الملف الحالي
    const content = fs.readFileSync(filePath, 'utf8');
    const existingTranslations = JSON.parse(content);
    
    // إضافة الترجمات الجديدة
    let addedCount = 0;
    const addedKeys = [];
    
    Object.entries(newTranslations).forEach(([key, value]) => {
      if (!existingTranslations[key]) {
        existingTranslations[key] = value;
        addedCount++;
        addedKeys.push(key);
      }
    });

    // كتابة الملف المحدث
    const updatedContent = JSON.stringify(existingTranslations, null, 2);
    fs.writeFileSync(filePath, updatedContent, 'utf8');

    console.log(`✅ تم إضافة ${addedCount} ترجمة جديدة للغة ${language}`);
    
    if (addedKeys.length > 0) {
      console.log('📋 الترجمات المضافة:');
      addedKeys.slice(0, 10).forEach(key => {
        console.log(`   - ${key}: "${existingTranslations[key]}"`);
      });
      if (addedKeys.length > 10) {
        console.log(`   ... و ${addedKeys.length - 10} ترجمة أخرى`);
      }
    }

    return {
      added: addedCount,
      keys: addedKeys,
      backup: backupPath
    };

  } catch (error) {
    console.error(`❌ خطأ في إضافة الترجمات للغة ${language}:`, error.message);
    return { added: 0, keys: [], error: error.message };
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🚀 بدء إضافة الترجمات الأساسية المفقودة...\n');

  // إضافة الترجمات الإنجليزية
  const englishResult = addMissingTranslations(
    EN_TRANSLATIONS_PATH, 
    essentialEnglishTranslations, 
    'الإنجليزية'
  );

  console.log(''); // سطر فارغ

  // إضافة الترجمات العربية
  const arabicResult = addMissingTranslations(
    AR_TRANSLATIONS_PATH, 
    essentialArabicTranslations, 
    'العربية'
  );

  console.log('\n📊 ملخص النتائج:');
  console.log(`✅ الترجمات الإنجليزية المضافة: ${englishResult.added}`);
  console.log(`✅ الترجمات العربية المضافة: ${arabicResult.added}`);
  console.log(`✅ إجمالي الترجمات المضافة: ${englishResult.added + arabicResult.added}`);

  if (englishResult.error || arabicResult.error) {
    console.log('\n❌ أخطاء:');
    if (englishResult.error) console.log(`   - الإنجليزية: ${englishResult.error}`);
    if (arabicResult.error) console.log(`   - العربية: ${arabicResult.error}`);
    process.exit(1);
  }

  console.log('\n💡 للتحقق من النتائج:');
  console.log('   node scripts/validate-translations.js');
  
  console.log('\n🎉 تم الانتهاء من إضافة الترجمات الأساسية!');
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  addMissingTranslations,
  essentialEnglishTranslations,
  essentialArabicTranslations
};
