// src/services/advancedReportsService.ts
"use client";

import { 
  collection, 
  query, 
  where, 
  getDocs, 
  orderBy,
  Timestamp,
  limit,
  startAfter,
  endBefore,
  doc,
  getDoc
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// ===== أنواع البيانات =====

export interface SalesReport {
  id: string;
  merchantId: string;
  period: {
    startDate: Timestamp;
    endDate: Timestamp;
    type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  summary: {
    totalRevenue: number;
    totalOrders: number;
    averageOrderValue: number;
    totalProducts: number;
    totalCustomers: number;
    conversionRate: number;
    growthRate: number;
  };
  dailyBreakdown: DailySales[];
  productPerformance: ProductPerformance[];
  customerAnalytics: CustomerAnalytics;
  trends: TrendAnalysis;
  generatedAt: Timestamp;
}

export interface DailySales {
  date: string;
  revenue: number;
  orders: number;
  customers: number;
  averageOrderValue: number;
  topProducts: string[];
}

export interface ProductPerformance {
  productId: string;
  productName: string;
  category: string;
  totalSales: number;
  totalRevenue: number;
  averageRating: number;
  viewsCount: number;
  conversionRate: number;
  profitMargin: number;
  trend: 'up' | 'down' | 'stable';
  growthRate: number;
}

export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
  customerRetentionRate: number;
  averageCustomerLifetimeValue: number;
  topCustomers: TopCustomer[];
  customerSegments: CustomerSegment[];
}

export interface TopCustomer {
  customerId: string;
  customerName: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate: Timestamp;
}

export interface CustomerSegment {
  segment: 'high_value' | 'regular' | 'new' | 'at_risk';
  count: number;
  percentage: number;
  averageSpending: number;
  characteristics: string[];
}

export interface TrendAnalysis {
  revenueGrowth: {
    current: number;
    previous: number;
    growthRate: number;
    trend: 'up' | 'down' | 'stable';
  };
  orderGrowth: {
    current: number;
    previous: number;
    growthRate: number;
    trend: 'up' | 'down' | 'stable';
  };
  customerGrowth: {
    current: number;
    previous: number;
    growthRate: number;
    trend: 'up' | 'down' | 'stable';
  };
  seasonalPatterns: SeasonalPattern[];
  predictions: {
    nextMonthRevenue: number;
    nextMonthOrders: number;
    confidence: number;
  };
}

export interface SeasonalPattern {
  period: string;
  averageRevenue: number;
  averageOrders: number;
  pattern: 'peak' | 'normal' | 'low';
}

// ===== خدمة التقارير المتقدمة =====

export class AdvancedReportsService {
  private ordersCollection = collection(db, 'orders');
  private productsCollection = collection(db, 'products');
  private customersCollection = collection(db, 'users');
  private reviewsCollection = collection(db, 'reviews');
  private storesCollection = collection(db, 'stores');

  // ===== إنتاج التقارير الرئيسية =====

  /**
   * إنتاج تقرير مبيعات شامل
   */
  async generateSalesReport(
    merchantId: string,
    period: {
      startDate: Date;
      endDate: Date;
      type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    }
  ): Promise<SalesReport> {
    try {
      console.log(`🔄 إنتاج تقرير المبيعات للتاجر: ${merchantId}`);
      
      // جلب البيانات الأساسية
      const [orders, products, customers] = await Promise.all([
        this.getOrdersInPeriod(merchantId, period.startDate, period.endDate),
        this.getMerchantProducts(merchantId),
        this.getMerchantCustomers(merchantId, period.startDate, period.endDate)
      ]);

      // حساب الملخص العام
      const summary = this.calculateSummary(orders, products, customers, period);
      
      // تحليل يومي
      const dailyBreakdown = this.generateDailyBreakdown(orders, period);
      
      // أداء المنتجات
      const productPerformance = await this.analyzeProductPerformance(
        merchantId, 
        orders, 
        products, 
        period
      );
      
      // تحليلات العملاء
      const customerAnalytics = await this.analyzeCustomers(
        merchantId, 
        orders, 
        customers, 
        period
      );
      
      // تحليل الاتجاهات
      const trends = await this.analyzeTrends(merchantId, orders, period);

      const report: SalesReport = {
        id: `report_${merchantId}_${Date.now()}`,
        merchantId,
        period: {
          startDate: Timestamp.fromDate(period.startDate),
          endDate: Timestamp.fromDate(period.endDate),
          type: period.type
        },
        summary,
        dailyBreakdown,
        productPerformance,
        customerAnalytics,
        trends,
        generatedAt: Timestamp.now()
      };

      console.log(`✅ تم إنتاج التقرير بنجاح`);
      return report;

    } catch (error) {
      console.error('خطأ في إنتاج تقرير المبيعات:', error);
      throw new Error('فشل في إنتاج تقرير المبيعات');
    }
  }

  // ===== الدوال المساعدة =====

  /**
   * جلب الطلبات في فترة محددة
   */
  private async getOrdersInPeriod(
    merchantId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<any[]> {
    const ordersQuery = query(
      this.ordersCollection,
      where('merchantId', '==', merchantId),
      where('createdAt', '>=', Timestamp.fromDate(startDate)),
      where('createdAt', '<=', Timestamp.fromDate(endDate)),
      where('status', '==', 'completed'),
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(ordersQuery);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  /**
   * جلب منتجات التاجر
   */
  private async getMerchantProducts(merchantId: string): Promise<any[]> {
    const productsQuery = query(
      this.productsCollection,
      where('merchantId', '==', merchantId)
    );

    const snapshot = await getDocs(productsQuery);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  /**
   * جلب عملاء التاجر في فترة محددة
   */
  private async getMerchantCustomers(
    merchantId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<any[]> {
    // جلب العملاء الذين قاموا بطلبات في هذه الفترة
    const ordersQuery = query(
      this.ordersCollection,
      where('merchantId', '==', merchantId),
      where('createdAt', '>=', Timestamp.fromDate(startDate)),
      where('createdAt', '<=', Timestamp.fromDate(endDate))
    );

    const ordersSnapshot = await getDocs(ordersQuery);
    const customerIds = [...new Set(ordersSnapshot.docs.map(doc => doc.data().customerId))];

    // جلب بيانات العملاء
    const customers = [];
    for (const customerId of customerIds) {
      try {
        const customerDoc = await getDoc(doc(this.customersCollection, customerId));
        if (customerDoc.exists()) {
          customers.push({
            id: customerDoc.id,
            ...customerDoc.data()
          });
        }
      } catch (error) {
        console.warn(`تعذر جلب بيانات العميل: ${customerId}`);
      }
    }

    return customers;
  }

  /**
   * حساب الملخص العام
   */
  private calculateSummary(
    orders: any[],
    products: any[],
    customers: any[],
    period: any
  ) {
    const totalRevenue = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
    const totalOrders = orders.length;
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const totalProducts = products.length;
    const totalCustomers = customers.length;

    // حساب معدل التحويل (محاكاة - يحتاج بيانات الزيارات الفعلية)
    const conversionRate = totalCustomers > 0 ? (totalOrders / (totalCustomers * 10)) * 100 : 0;

    // حساب معدل النمو (محاكاة - يحتاج مقارنة مع الفترة السابقة)
    const growthRate = Math.random() * 20 - 10; // محاكاة نمو بين -10% و +10%

    return {
      totalRevenue,
      totalOrders,
      averageOrderValue,
      totalProducts,
      totalCustomers,
      conversionRate,
      growthRate
    };
  }

  /**
   * إنتاج التحليل اليومي
   */
  private generateDailyBreakdown(orders: any[], period: any): DailySales[] {
    const dailyData = new Map<string, DailySales>();

    // تهيئة الأيام في الفترة
    const startDate = new Date(period.startDate);
    const endDate = new Date(period.endDate);

    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const dateStr = date.toISOString().split('T')[0];
      dailyData.set(dateStr, {
        date: dateStr,
        revenue: 0,
        orders: 0,
        customers: 0,
        averageOrderValue: 0,
        topProducts: []
      });
    }

    // تجميع البيانات حسب اليوم
    const customersByDay = new Map<string, Set<string>>();
    const productsByDay = new Map<string, Map<string, number>>();

    orders.forEach(order => {
      const orderDate = order.createdAt.toDate();
      const dateStr = orderDate.toISOString().split('T')[0];

      if (dailyData.has(dateStr)) {
        const dayData = dailyData.get(dateStr)!;
        dayData.revenue += order.totalAmount || 0;
        dayData.orders += 1;

        // تتبع العملاء الفريدين
        if (!customersByDay.has(dateStr)) {
          customersByDay.set(dateStr, new Set());
        }
        customersByDay.get(dateStr)!.add(order.customerId);

        // تتبع المنتجات
        if (!productsByDay.has(dateStr)) {
          productsByDay.set(dateStr, new Map());
        }

        order.items?.forEach((item: any) => {
          const productMap = productsByDay.get(dateStr)!;
          const currentCount = productMap.get(item.productId) || 0;
          productMap.set(item.productId, currentCount + item.quantity);
        });
      }
    });

    // حساب المتوسطات والمنتجات الأكثر مبيعاً
    dailyData.forEach((dayData, dateStr) => {
      dayData.customers = customersByDay.get(dateStr)?.size || 0;
      dayData.averageOrderValue = dayData.orders > 0 ? dayData.revenue / dayData.orders : 0;

      // أفضل 3 منتجات في اليوم
      const productMap = productsByDay.get(dateStr);
      if (productMap) {
        const sortedProducts = Array.from(productMap.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, 3)
          .map(([productId]) => productId);
        dayData.topProducts = sortedProducts;
      }
    });

    return Array.from(dailyData.values()).sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * تحليل أداء المنتجات
   */
  private async analyzeProductPerformance(
    merchantId: string,
    orders: any[],
    products: any[],
    period: any
  ): Promise<ProductPerformance[]> {
    const productStats = new Map<string, {
      totalSales: number;
      totalRevenue: number;
      orders: number;
    }>();

    // تجميع إحصائيات المنتجات من الطلبات
    orders.forEach(order => {
      order.items?.forEach((item: any) => {
        const productId = item.productId;
        const stats = productStats.get(productId) || {
          totalSales: 0,
          totalRevenue: 0,
          orders: 0
        };

        stats.totalSales += item.quantity;
        stats.totalRevenue += item.price * item.quantity;
        stats.orders += 1;

        productStats.set(productId, stats);
      });
    });

    // إنشاء تحليل الأداء لكل منتج
    const performance: ProductPerformance[] = [];

    for (const product of products) {
      const stats = productStats.get(product.id) || {
        totalSales: 0,
        totalRevenue: 0,
        orders: 0
      };

      // جلب التقييمات
      const reviewsQuery = query(
        this.reviewsCollection,
        where('productId', '==', product.id)
      );
      const reviewsSnapshot = await getDocs(reviewsQuery);
      const reviews = reviewsSnapshot.docs.map((doc: any) => doc.data());
      const averageRating = reviews.length > 0
        ? reviews.reduce((sum: number, review: any) => sum + (review.rating || 0), 0) / reviews.length
        : 0;

      // حساب معدل التحويل (محاكاة)
      const viewsCount = product.viewsCount || Math.floor(Math.random() * 1000) + 100;
      const conversionRate = viewsCount > 0 ? (stats.totalSales / viewsCount) * 100 : 0;

      // تحديد الاتجاه (محاكاة)
      const growthRate = Math.random() * 40 - 20; // بين -20% و +20%
      const trend: 'up' | 'down' | 'stable' =
        growthRate > 5 ? 'up' :
        growthRate < -5 ? 'down' : 'stable';

      performance.push({
        productId: product.id,
        productName: product.name || 'منتج غير محدد',
        category: product.category || 'غير مصنف',
        totalSales: stats.totalSales,
        totalRevenue: stats.totalRevenue,
        averageRating,
        viewsCount,
        conversionRate,
        profitMargin: Math.random() * 30 + 10, // محاكاة هامش ربح بين 10-40%
        trend,
        growthRate
      });
    }

    return performance.sort((a, b) => b.totalRevenue - a.totalRevenue);
  }

  /**
   * تحليل العملاء
   */
  private async analyzeCustomers(
    merchantId: string,
    orders: any[],
    customers: any[],
    period: any
  ): Promise<CustomerAnalytics> {
    const totalCustomers = customers.length;

    // العملاء الجدد (الذين انضموا في هذه الفترة)
    const newCustomers = customers.filter(customer => {
      const joinDate = customer.createdAt?.toDate();
      return joinDate && joinDate >= period.startDate && joinDate <= period.endDate;
    }).length;

    const returningCustomers = totalCustomers - newCustomers;

    // حساب معدل الاحتفاظ بالعملاء (محاكاة)
    const customerRetentionRate = totalCustomers > 0 ? (returningCustomers / totalCustomers) * 100 : 0;

    // حساب متوسط القيمة الدائمة للعميل
    const totalRevenue = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
    const averageCustomerLifetimeValue = totalCustomers > 0 ? totalRevenue / totalCustomers : 0;

    // أفضل العملاء
    const customerOrderStats = new Map<string, {
      totalOrders: number;
      totalSpent: number;
      lastOrderDate: Date;
      customerData: any;
    }>();

    orders.forEach(order => {
      const customerId = order.customerId;
      const stats = customerOrderStats.get(customerId) || {
        totalOrders: 0,
        totalSpent: 0,
        lastOrderDate: new Date(0),
        customerData: customers.find(c => c.id === customerId)
      };

      stats.totalOrders += 1;
      stats.totalSpent += order.totalAmount || 0;
      const orderDate = order.createdAt.toDate();
      if (orderDate > stats.lastOrderDate) {
        stats.lastOrderDate = orderDate;
      }

      customerOrderStats.set(customerId, stats);
    });

    const topCustomers: TopCustomer[] = Array.from(customerOrderStats.entries())
      .sort((a, b) => b[1].totalSpent - a[1].totalSpent)
      .slice(0, 10)
      .map(([customerId, stats]) => ({
        customerId,
        customerName: stats.customerData?.displayName || stats.customerData?.email || 'عميل غير محدد',
        totalOrders: stats.totalOrders,
        totalSpent: stats.totalSpent,
        averageOrderValue: stats.totalOrders > 0 ? stats.totalSpent / stats.totalOrders : 0,
        lastOrderDate: Timestamp.fromDate(stats.lastOrderDate)
      }));

    // تجميع العملاء
    const customerSegments: CustomerSegment[] = [
      {
        segment: 'high_value',
        count: Math.floor(totalCustomers * 0.2),
        percentage: 20,
        averageSpending: averageCustomerLifetimeValue * 2,
        characteristics: ['إنفاق عالي', 'طلبات متكررة', 'ولاء عالي']
      },
      {
        segment: 'regular',
        count: Math.floor(totalCustomers * 0.5),
        percentage: 50,
        averageSpending: averageCustomerLifetimeValue,
        characteristics: ['إنفاق متوسط', 'طلبات منتظمة']
      },
      {
        segment: 'new',
        count: newCustomers,
        percentage: totalCustomers > 0 ? (newCustomers / totalCustomers) * 100 : 0,
        averageSpending: averageCustomerLifetimeValue * 0.5,
        characteristics: ['عملاء جدد', 'يحتاجون متابعة']
      },
      {
        segment: 'at_risk',
        count: Math.floor(totalCustomers * 0.1),
        percentage: 10,
        averageSpending: averageCustomerLifetimeValue * 0.3,
        characteristics: ['لم يطلبوا مؤخراً', 'يحتاجون إعادة تفعيل']
      }
    ];

    return {
      totalCustomers,
      newCustomers,
      returningCustomers,
      customerRetentionRate,
      averageCustomerLifetimeValue,
      topCustomers,
      customerSegments
    };
  }

  /**
   * تحليل الاتجاهات
   */
  private async analyzeTrends(
    merchantId: string,
    orders: any[],
    period: any
  ): Promise<TrendAnalysis> {
    const currentRevenue = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
    const currentOrders = orders.length;
    const currentCustomers = new Set(orders.map(order => order.customerId)).size;

    // محاكاة البيانات السابقة (في التطبيق الحقيقي، نجلب البيانات من الفترة السابقة)
    const previousRevenue = currentRevenue * (0.8 + Math.random() * 0.4); // ±20%
    const previousOrders = Math.floor(currentOrders * (0.8 + Math.random() * 0.4));
    const previousCustomers = Math.floor(currentCustomers * (0.8 + Math.random() * 0.4));

    const revenueGrowthRate = previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    const orderGrowthRate = previousOrders > 0 ? ((currentOrders - previousOrders) / previousOrders) * 100 : 0;
    const customerGrowthRate = previousCustomers > 0 ? ((currentCustomers - previousCustomers) / previousCustomers) * 100 : 0;

    // أنماط موسمية (محاكاة)
    const seasonalPatterns: SeasonalPattern[] = [
      {
        period: 'الربع الأول',
        averageRevenue: currentRevenue * 0.9,
        averageOrders: currentOrders * 0.9,
        pattern: 'normal'
      },
      {
        period: 'الربع الثاني',
        averageRevenue: currentRevenue * 1.1,
        averageOrders: currentOrders * 1.1,
        pattern: 'peak'
      },
      {
        period: 'الربع الثالث',
        averageRevenue: currentRevenue * 0.8,
        averageOrders: currentOrders * 0.8,
        pattern: 'low'
      },
      {
        period: 'الربع الرابع',
        averageRevenue: currentRevenue * 1.2,
        averageOrders: currentOrders * 1.2,
        pattern: 'peak'
      }
    ];

    // توقعات (محاكاة بسيطة)
    const nextMonthRevenue = currentRevenue * (1 + revenueGrowthRate / 100) * (0.9 + Math.random() * 0.2);
    const nextMonthOrders = Math.floor(currentOrders * (1 + orderGrowthRate / 100) * (0.9 + Math.random() * 0.2));

    return {
      revenueGrowth: {
        current: currentRevenue,
        previous: previousRevenue,
        growthRate: revenueGrowthRate,
        trend: revenueGrowthRate > 5 ? 'up' : revenueGrowthRate < -5 ? 'down' : 'stable'
      },
      orderGrowth: {
        current: currentOrders,
        previous: previousOrders,
        growthRate: orderGrowthRate,
        trend: orderGrowthRate > 5 ? 'up' : orderGrowthRate < -5 ? 'down' : 'stable'
      },
      customerGrowth: {
        current: currentCustomers,
        previous: previousCustomers,
        growthRate: customerGrowthRate,
        trend: customerGrowthRate > 5 ? 'up' : customerGrowthRate < -5 ? 'down' : 'stable'
      },
      seasonalPatterns,
      predictions: {
        nextMonthRevenue,
        nextMonthOrders,
        confidence: 75 + Math.random() * 20 // ثقة بين 75-95%
      }
    };
  }

  /**
   * إنتاج تقرير سريع للفترة الحالية
   */
  async generateQuickReport(merchantId: string): Promise<Partial<SalesReport>> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30); // آخر 30 يوم

    return this.generateSalesReport(merchantId, {
      startDate,
      endDate,
      type: 'monthly'
    });
  }

  /**
   * مقارنة الأداء بين فترتين
   */
  async comparePerformance(
    merchantId: string,
    currentPeriod: { startDate: Date; endDate: Date },
    previousPeriod: { startDate: Date; endDate: Date }
  ) {
    const [currentReport, previousReport] = await Promise.all([
      this.generateSalesReport(merchantId, { ...currentPeriod, type: 'monthly' }),
      this.generateSalesReport(merchantId, { ...previousPeriod, type: 'monthly' })
    ]);

    return {
      current: currentReport,
      previous: previousReport,
      comparison: {
        revenueChange: currentReport.summary.totalRevenue - previousReport.summary.totalRevenue,
        revenueChangePercent: previousReport.summary.totalRevenue > 0
          ? ((currentReport.summary.totalRevenue - previousReport.summary.totalRevenue) / previousReport.summary.totalRevenue) * 100
          : 0,
        ordersChange: currentReport.summary.totalOrders - previousReport.summary.totalOrders,
        customersChange: currentReport.summary.totalCustomers - previousReport.summary.totalCustomers
      }
    };
  }
}

// إنشاء مثيل واحد من الخدمة
export const advancedReportsService = new AdvancedReportsService();
