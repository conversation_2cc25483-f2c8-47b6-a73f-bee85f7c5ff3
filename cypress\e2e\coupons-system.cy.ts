/// <reference types="cypress" />

/**
 * اختبارات نظام الكوبونات
 * تختبر جميع وظائف إنشاء وإدارة واستخدام الكوبونات
 */

describe('🎟️ نظام الكوبونات والخصومات', () => {
  beforeEach(() => {
    // إعداد البيانات الوهمية
    cy.mockLogin('merchant')
    cy.mockFirebaseAuth()
    cy.mockCoupons()
    
    // زيارة صفحة الكوبونات
    cy.visitWithLocale('/merchant/coupons')
    cy.waitForLoadingToFinish()
  })

  afterEach(() => {
    cy.mockLogout()
  })

  describe('📋 صفحة إدارة الكوبونات', () => {
    it('يجب أن تعرض صفحة الكوبونات بشكل صحيح', () => {
      // التحقق من وجود العناصر الأساسية
      cy.get('[data-testid="coupons-page"]').should('be.visible')
      cy.shouldContainArabicText('إدارة الكوبونات')
      cy.shouldContainArabicText('الكوبونات النشطة')
      cy.shouldContainArabicText('إحصائيات الكوبونات')
      
      // التحقق من وجود زر إنشاء كوبون جديد
      cy.get('[data-testid="create-coupon-btn"]').should('be.visible')
      cy.shouldContainArabicText('إنشاء كوبون جديد')
    })

    it('يجب أن تعرض إحصائيات الكوبونات', () => {
      // التحقق من وجود الإحصائيات
      cy.get('[data-testid="coupons-stats"]').should('be.visible')
      cy.get('[data-testid="total-coupons"]').should('be.visible')
      cy.get('[data-testid="active-coupons"]').should('be.visible')
      cy.get('[data-testid="total-usage"]').should('be.visible')
      cy.get('[data-testid="total-discount"]').should('be.visible')
      
      // التحقق من الرسوم البيانية
      cy.get('[data-testid="usage-chart"]').should('be.visible')
      cy.get('[data-testid="performance-chart"]').should('be.visible')
    })

    it('يجب أن تعرض قائمة الكوبونات', () => {
      // التحقق من وجود جدول الكوبونات
      cy.get('[data-testid="coupons-table"]').should('be.visible')
      
      // التحقق من أعمدة الجدول
      cy.shouldContainArabicText('كود الكوبون')
      cy.shouldContainArabicText('نوع الخصم')
      cy.shouldContainArabicText('قيمة الخصم')
      cy.shouldContainArabicText('الاستخدام')
      cy.shouldContainArabicText('تاريخ الانتهاء')
      cy.shouldContainArabicText('الحالة')
      cy.shouldContainArabicText('الإجراءات')
    })
  })

  describe('➕ إنشاء كوبون جديد', () => {
    it('يجب أن يفتح نموذج إنشاء كوبون', () => {
      cy.get('[data-testid="create-coupon-btn"]').click()
      
      // التحقق من فتح النموذج
      cy.get('[data-testid="create-coupon-modal"]').should('be.visible')
      cy.shouldContainArabicText('إنشاء كوبون جديد')
      
      // التحقق من وجود الحقول المطلوبة
      cy.get('[data-testid="coupon-code"]').should('be.visible')
      cy.get('[data-testid="coupon-type"]').should('be.visible')
      cy.get('[data-testid="coupon-value"]').should('be.visible')
      cy.get('[data-testid="usage-limit"]').should('be.visible')
      cy.get('[data-testid="valid-from"]').should('be.visible')
      cy.get('[data-testid="valid-until"]').should('be.visible')
    })

    it('يجب أن ينشئ كوبون خصم نسبة مئوية', () => {
      cy.get('[data-testid="create-coupon-btn"]').click()
      
      // ملء نموذج الكوبون
      cy.fillForm({
        'coupon-code': 'SAVE20',
        'coupon-value': '20',
        'usage-limit': '100',
        'min-order-amount': '100'
      })
      
      // اختيار نوع الخصم
      cy.get('[data-testid="coupon-type"]').select('percentage')
      
      // تحديد تواريخ الصلاحية
      const today = new Date().toISOString().split('T')[0]
      const nextMonth = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      
      cy.get('[data-testid="valid-from"]').type(today)
      cy.get('[data-testid="valid-until"]').type(nextMonth)
      
      // حفظ الكوبون
      cy.get('[data-testid="save-coupon"]').click()
      
      // التحقق من نجاح الإنشاء
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إنشاء الكوبون بنجاح')
    })

    it('يجب أن ينشئ كوبون خصم مبلغ ثابت', () => {
      cy.get('[data-testid="create-coupon-btn"]').click()
      
      // ملء نموذج الكوبون
      cy.fillForm({
        'coupon-code': 'FIXED50',
        'coupon-value': '50',
        'usage-limit': '50',
        'min-order-amount': '200'
      })
      
      // اختيار نوع الخصم
      cy.get('[data-testid="coupon-type"]').select('fixed')
      
      // تحديد الحد الأقصى للخصم
      cy.get('[data-testid="max-discount"]').type('50')
      
      // تحديد تواريخ الصلاحية
      const today = new Date().toISOString().split('T')[0]
      const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      
      cy.get('[data-testid="valid-from"]').type(today)
      cy.get('[data-testid="valid-until"]').type(nextWeek)
      
      // حفظ الكوبون
      cy.get('[data-testid="save-coupon"]').click()
      
      // التحقق من نجاح الإنشاء
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إنشاء الكوبون بنجاح')
    })

    it('يجب أن ينشئ كوبون شحن مجاني', () => {
      cy.get('[data-testid="create-coupon-btn"]').click()
      
      // ملء نموذج الكوبون
      cy.fillForm({
        'coupon-code': 'FREESHIP',
        'usage-limit': '200',
        'min-order-amount': '150'
      })
      
      // اختيار نوع الخصم
      cy.get('[data-testid="coupon-type"]').select('free_shipping')
      
      // تحديد تواريخ الصلاحية
      const today = new Date().toISOString().split('T')[0]
      const nextMonth = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      
      cy.get('[data-testid="valid-from"]').type(today)
      cy.get('[data-testid="valid-until"]').type(nextMonth)
      
      // حفظ الكوبون
      cy.get('[data-testid="save-coupon"]').click()
      
      // التحقق من نجاح الإنشاء
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إنشاء الكوبون بنجاح')
    })

    it('يجب أن يضيف شروط تطبيق للكوبون', () => {
      cy.get('[data-testid="create-coupon-btn"]').click()
      
      // ملء البيانات الأساسية
      cy.fillForm({
        'coupon-code': 'ELECTRONICS20',
        'coupon-value': '20'
      })
      
      cy.get('[data-testid="coupon-type"]').select('percentage')
      
      // إضافة شروط المنتجات
      cy.get('[data-testid="add-product-conditions"]').click()
      cy.get('[data-testid="applicable-products"]').select(['product-1', 'product-2'])
      
      // إضافة شروط الفئات
      cy.get('[data-testid="add-category-conditions"]').click()
      cy.get('[data-testid="applicable-categories"]').select(['electronics'])
      
      // حفظ الكوبون
      cy.get('[data-testid="save-coupon"]').click()
      
      // التحقق من نجاح الإنشاء
      cy.get('[data-testid="success-message"]').should('be.visible')
    })
  })

  describe('✏️ تحرير وإدارة الكوبونات', () => {
    it('يجب أن يحرر كوبون موجود', () => {
      // فتح نموذج التحرير
      cy.get('[data-testid="coupon-row"]').first().within(() => {
        cy.get('[data-testid="edit-coupon"]').click()
      })
      
      // التحقق من فتح نموذج التحرير
      cy.get('[data-testid="edit-coupon-modal"]').should('be.visible')
      cy.shouldContainArabicText('تحرير الكوبون')
      
      // تعديل قيمة الخصم
      cy.get('[data-testid="coupon-value"]').clear().type('25')
      
      // حفظ التغييرات
      cy.get('[data-testid="save-changes"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث الكوبون بنجاح')
    })

    it('يجب أن يوقف/يفعل كوبون', () => {
      // إيقاف الكوبون
      cy.get('[data-testid="coupon-row"]').first().within(() => {
        cy.get('[data-testid="toggle-coupon"]').click()
      })
      
      // تأكيد الإيقاف
      cy.get('[data-testid="confirm-toggle"]').click()
      
      // التحقق من تغيير الحالة
      cy.get('[data-testid="coupon-status"]').should('contain.text', 'معطل')
      
      // إعادة تفعيل الكوبون
      cy.get('[data-testid="coupon-row"]').first().within(() => {
        cy.get('[data-testid="toggle-coupon"]').click()
      })
      
      // تأكيد التفعيل
      cy.get('[data-testid="confirm-toggle"]').click()
      
      // التحقق من تغيير الحالة
      cy.get('[data-testid="coupon-status"]').should('contain.text', 'نشط')
    })

    it('يجب أن يحذف كوبون', () => {
      // حذف الكوبون
      cy.get('[data-testid="coupon-row"]').first().within(() => {
        cy.get('[data-testid="delete-coupon"]').click()
      })
      
      // تأكيد الحذف
      cy.get('[data-testid="confirm-delete"]').should('be.visible')
      cy.shouldContainArabicText('هل أنت متأكد من حذف هذا الكوبون؟')
      cy.get('[data-testid="confirm-delete-btn"]').click()
      
      // التحقق من نجاح الحذف
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم حذف الكوبون بنجاح')
    })

    it('يجب أن يعرض تفاصيل استخدام الكوبون', () => {
      // عرض تفاصيل الاستخدام
      cy.get('[data-testid="coupon-row"]').first().within(() => {
        cy.get('[data-testid="view-usage"]').click()
      })
      
      // التحقق من فتح نافذة التفاصيل
      cy.get('[data-testid="coupon-usage-modal"]').should('be.visible')
      cy.shouldContainArabicText('تفاصيل استخدام الكوبون')
      
      // التحقق من وجود بيانات الاستخدام
      cy.get('[data-testid="usage-stats"]').should('be.visible')
      cy.get('[data-testid="usage-history"]').should('be.visible')
      cy.get('[data-testid="usage-chart"]').should('be.visible')
    })
  })

  describe('🛒 تطبيق الكوبونات (من جانب العميل)', () => {
    beforeEach(() => {
      // تسجيل دخول كعميل
      cy.mockLogout()
      cy.mockLogin('customer')
      
      // الانتقال إلى صفحة السلة
      cy.visitWithLocale('/checkout')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن يطبق كوبون صحيح في السلة', () => {
      // إضافة منتجات وهمية للسلة
      cy.window().then((win) => {
        const mockCart = {
          items: [
            { id: 'product-1', name: 'منتج تجريبي', price: 150, quantity: 2 }
          ],
          total: 300
        }
        win.localStorage.setItem('cart', JSON.stringify(mockCart))
      })
      
      // تطبيق الكوبون
      cy.get('[data-testid="coupon-input"]').type('SAVE20')
      cy.get('[data-testid="apply-coupon"]').click()
      
      // التحقق من تطبيق الخصم
      cy.get('[data-testid="discount-amount"]').should('be.visible')
      cy.get('[data-testid="final-total"]').should('contain.text', '240') // 300 - 20% = 240
      cy.get('[data-testid="coupon-success"]').should('be.visible')
      cy.shouldContainArabicText('تم تطبيق الكوبون بنجاح')
    })

    it('يجب أن يرفض كوبون غير صحيح', () => {
      // تطبيق كوبون غير صحيح
      cy.get('[data-testid="coupon-input"]').type('INVALID_COUPON')
      cy.get('[data-testid="apply-coupon"]').click()
      
      // التحقق من رسالة الخطأ
      cy.get('[data-testid="coupon-error"]').should('be.visible')
      cy.shouldContainArabicText('الكوبون غير صحيح أو منتهي الصلاحية')
    })

    it('يجب أن يرفض كوبون لا يحقق الحد الأدنى', () => {
      // إضافة منتجات بقيمة أقل من الحد الأدنى
      cy.window().then((win) => {
        const mockCart = {
          items: [
            { id: 'product-1', name: 'منتج تجريبي', price: 50, quantity: 1 }
          ],
          total: 50
        }
        win.localStorage.setItem('cart', JSON.stringify(mockCart))
      })
      
      // تطبيق كوبون يتطلب حد أدنى 100
      cy.get('[data-testid="coupon-input"]').type('SAVE20')
      cy.get('[data-testid="apply-coupon"]').click()
      
      // التحقق من رسالة الخطأ
      cy.get('[data-testid="coupon-error"]').should('be.visible')
      cy.shouldContainArabicText('الحد الأدنى للطلب لا يحقق شروط الكوبون')
    })

    it('يجب أن يطبق كوبون شحن مجاني', () => {
      // إضافة منتجات للسلة
      cy.window().then((win) => {
        const mockCart = {
          items: [
            { id: 'product-1', name: 'منتج تجريبي', price: 200, quantity: 1 }
          ],
          total: 200,
          shipping: 25
        }
        win.localStorage.setItem('cart', JSON.stringify(mockCart))
      })
      
      // تطبيق كوبون شحن مجاني
      cy.get('[data-testid="coupon-input"]').type('FREESHIP')
      cy.get('[data-testid="apply-coupon"]').click()
      
      // التحقق من إلغاء رسوم الشحن
      cy.get('[data-testid="shipping-cost"]').should('contain.text', '0')
      cy.get('[data-testid="free-shipping-badge"]').should('be.visible')
      cy.shouldContainArabicText('شحن مجاني')
    })
  })

  describe('📊 تحليلات وتقارير الكوبونات', () => {
    it('يجب أن تعرض تحليلات شاملة للكوبونات', () => {
      // الانتقال إلى صفحة التحليلات
      cy.get('[data-testid="coupons-analytics"]').click()
      
      // التحقق من وجود المقاييس
      cy.get('[data-testid="conversion-rate"]').should('be.visible')
      cy.get('[data-testid="average-discount"]').should('be.visible')
      cy.get('[data-testid="revenue-impact"]').should('be.visible')
      cy.get('[data-testid="customer-acquisition"]').should('be.visible')
      
      // التحقق من الرسوم البيانية
      cy.get('[data-testid="usage-trends-chart"]').should('be.visible')
      cy.get('[data-testid="performance-comparison-chart"]').should('be.visible')
      cy.get('[data-testid="discount-distribution-chart"]').should('be.visible')
    })

    it('يجب أن يفلتر التحليلات حسب الفترة الزمنية', () => {
      cy.get('[data-testid="coupons-analytics"]').click()
      
      // تغيير الفترة الزمنية
      cy.get('[data-testid="date-range-filter"]').select('last_7_days')
      
      // التحقق من تحديث البيانات
      cy.get('[data-testid="analytics-loading"]').should('be.visible')
      cy.get('[data-testid="analytics-loading"]').should('not.exist')
      cy.get('[data-testid="conversion-rate"]').should('be.visible')
    })

    it('يجب أن يصدر تقرير الكوبونات', () => {
      cy.get('[data-testid="coupons-analytics"]').click()
      
      // تصدير التقرير
      cy.get('[data-testid="export-report"]').click()
      
      // اختيار نوع التصدير
      cy.get('[data-testid="export-format"]').select('pdf')
      cy.get('[data-testid="confirm-export"]').click()
      
      // التحقق من بدء التصدير
      cy.get('[data-testid="export-status"]').should('contain.text', 'جاري التصدير')
    })
  })

  describe('🔍 البحث والفلترة', () => {
    it('يجب أن يبحث في الكوبونات بالكود', () => {
      // البحث بكود الكوبون
      cy.get('[data-testid="coupon-search"]').type('SAVE20')
      cy.get('[data-testid="search-btn"]').click()
      
      // التحقق من نتائج البحث
      cy.get('[data-testid="coupon-row"]').should('contain.text', 'SAVE20')
    })

    it('يجب أن يفلتر الكوبونات حسب النوع', () => {
      // فلترة حسب نوع الخصم
      cy.get('[data-testid="coupon-type-filter"]').select('percentage')
      
      // التحقق من الفلترة
      cy.get('[data-testid="coupon-row"]').each(($row) => {
        cy.wrap($row).find('[data-testid="coupon-type-cell"]')
          .should('contain.text', 'نسبة مئوية')
      })
    })

    it('يجب أن يفلتر الكوبونات حسب الحالة', () => {
      // فلترة حسب الحالة
      cy.get('[data-testid="coupon-status-filter"]').select('active')
      
      // التحقق من الفلترة
      cy.get('[data-testid="coupon-row"]').each(($row) => {
        cy.wrap($row).find('[data-testid="coupon-status-cell"]')
          .should('contain.text', 'نشط')
      })
    })
  })
})
