#!/usr/bin/env node

/**
 * اختبار شامل لنظام الترجمات مع MCP
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// مسارات ملفات الترجمة
const TRANSLATIONS_PATH = path.join(__dirname, '../src/locales/translations.json');

/**
 * تحميل ملف الترجمات
 */
function loadTranslations() {
  try {
    if (fs.existsSync(TRANSLATIONS_PATH)) {
      const content = fs.readFileSync(TRANSLATIONS_PATH, 'utf8');
      return JSON.parse(content);
    } else {
      console.error('❌ ملف الترجمات غير موجود:', TRANSLATIONS_PATH);
      return null;
    }
  } catch (error) {
    console.error('❌ خطأ في تحميل ملف الترجمات:', error.message);
    return null;
  }
}

/**
 * فحص تطابق المفاتيح بين اللغات
 */
function checkKeyConsistency(translations) {
  const arKeys = new Set(Object.keys(translations.ar || {}));
  const enKeys = new Set(Object.keys(translations.en || {}));
  
  const missingInArabic = [...enKeys].filter(key => !arKeys.has(key));
  const missingInEnglish = [...arKeys].filter(key => !enKeys.has(key));
  
  let hasErrors = false;
  
  if (missingInArabic.length > 0) {
    console.log('❌ مفاتيح مفقودة في الترجمة العربية:');
    missingInArabic.forEach(key => console.log(`   - ${key}`));
    hasErrors = true;
  }
  
  if (missingInEnglish.length > 0) {
    console.log('❌ مفاتيح مفقودة في الترجمة الإنجليزية:');
    missingInEnglish.forEach(key => console.log(`   - ${key}`));
    hasErrors = true;
  }
  
  if (!hasErrors) {
    console.log('✅ جميع المفاتيح متطابقة بين اللغتين');
  }
  
  return !hasErrors;
}

/**
 * فحص القيم الفارغة
 */
function checkEmptyValues(translations) {
  let hasErrors = false;
  
  ['ar', 'en'].forEach(lang => {
    const langTranslations = translations[lang] || {};
    const emptyKeys = Object.entries(langTranslations)
      .filter(([key, value]) => !value || value.trim() === '')
      .map(([key]) => key);
    
    if (emptyKeys.length > 0) {
      console.log(`❌ قيم فارغة في ${lang === 'ar' ? 'العربية' : 'الإنجليزية'}:`);
      emptyKeys.forEach(key => console.log(`   - ${key}`));
      hasErrors = true;
    }
  });
  
  if (!hasErrors) {
    console.log('✅ لا توجد قيم فارغة');
  }
  
  return !hasErrors;
}

/**
 * فحص معاملات الترجمة
 */
function checkParameterConsistency(translations) {
  let hasErrors = false;
  
  const arTranslations = translations.ar || {};
  const enTranslations = translations.en || {};
  
  Object.keys(arTranslations).forEach(key => {
    const arValue = arTranslations[key];
    const enValue = enTranslations[key];
    
    if (!enValue) return;
    
    const arParams = (arValue.match(/\{\{(\w+)\}\}/g) || []).map(p => p.slice(2, -2));
    const enParams = (enValue.match(/\{\{(\w+)\}\}/g) || []).map(p => p.slice(2, -2));
    
    const arParamsSet = new Set(arParams);
    const enParamsSet = new Set(enParams);
    
    const missingInAr = [...enParamsSet].filter(p => !arParamsSet.has(p));
    const missingInEn = [...arParamsSet].filter(p => !enParamsSet.has(p));
    
    if (missingInAr.length > 0 || missingInEn.length > 0) {
      console.log(`❌ عدم تطابق المعاملات في المفتاح: ${key}`);
      if (missingInAr.length > 0) {
        console.log(`   مفقود في العربية: ${missingInAr.join(', ')}`);
      }
      if (missingInEn.length > 0) {
        console.log(`   مفقود في الإنجليزية: ${missingInEn.join(', ')}`);
      }
      hasErrors = true;
    }
  });
  
  if (!hasErrors) {
    console.log('✅ جميع معاملات الترجمة متطابقة');
  }
  
  return !hasErrors;
}

/**
 * تشغيل اختبارات Cypress للترجمات
 */
function runCypressTests() {
  try {
    console.log('🧪 تشغيل اختبارات Cypress للترجمات...');
    
    // تشغيل اختبارات الترجمات المحددة
    execSync('npx cypress run --spec "cypress/e2e/translation-system-comprehensive.cy.ts"', {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    console.log('✅ اختبارات Cypress نجحت');
    return true;
  } catch (error) {
    console.log('❌ اختبارات Cypress فشلت');
    return false;
  }
}

/**
 * فحص النصوص المكتوبة مباشرة
 */
function checkHardcodedText() {
  try {
    console.log('🔍 فحص النصوص المكتوبة مباشرة...');
    
    execSync('node scripts/find-hardcoded-text.js', {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    console.log('✅ لا توجد نصوص مكتوبة مباشرة');
    return true;
  } catch (error) {
    console.log('❌ وجدت نصوص مكتوبة مباشرة');
    return false;
  }
}

/**
 * إحصائيات الترجمات
 */
function generateStats(translations) {
  const arCount = Object.keys(translations.ar || {}).length;
  const enCount = Object.keys(translations.en || {}).length;
  
  console.log('\n📊 إحصائيات الترجمات:');
  console.log(`   🇸🇦 العربية: ${arCount} مفتاح`);
  console.log(`   🇺🇸 الإنجليزية: ${enCount} مفتاح`);
  console.log(`   📝 إجمالي: ${Math.max(arCount, enCount)} مفتاح فريد`);
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🌍 اختبار شامل لنظام الترجمات مع MCP...\n');
  
  const translations = loadTranslations();
  if (!translations) {
    process.exit(1);
  }
  
  let allTestsPassed = true;
  
  // الاختبارات الأساسية
  console.log('📋 الاختبارات الأساسية:');
  const basicTests = [
    () => checkKeyConsistency(translations),
    () => checkEmptyValues(translations),
    () => checkParameterConsistency(translations)
  ];
  
  basicTests.forEach(test => {
    if (!test()) {
      allTestsPassed = false;
    }
  });
  
  console.log('\n🔍 فحص النصوص المكتوبة مباشرة:');
  if (!checkHardcodedText()) {
    allTestsPassed = false;
  }
  
  console.log('\n🧪 اختبارات التكامل:');
  if (!runCypressTests()) {
    allTestsPassed = false;
  }
  
  // عرض الإحصائيات
  generateStats(translations);
  
  // النتيجة النهائية
  console.log('\n' + '='.repeat(60));
  if (allTestsPassed) {
    console.log('🎉 جميع اختبارات نظام الترجمات نجحت!');
    console.log('✅ نظام الترجمات يعمل بشكل مثالي مع MCP');
    console.log('🌍 صفحة تسجيل الدخول جاهزة للاستخدام بكلا اللغتين');
  } else {
    console.log('❌ بعض اختبارات نظام الترجمات فشلت');
    console.log('🔧 يرجى إصلاح المشاكل المذكورة أعلاه');
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  loadTranslations,
  checkKeyConsistency,
  checkEmptyValues,
  checkParameterConsistency,
  runCypressTests,
  checkHardcodedText
};
