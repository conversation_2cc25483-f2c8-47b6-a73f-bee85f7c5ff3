/**
 * خدمة الدفع المتقدمة - نظام دفع شامل ومتطور
 * يدعم جميع طرق الدفع المحلية والعالمية مع أعلى معايير الأمان
 */

import { db } from '@/lib/firebase';
import { ApexEncryptionEngine } from '@/lib/encryption';
import { 
  collection, 
  addDoc, 
  updateDoc, 
  doc, 
  getDocs,
  query,
  where,
  orderBy,
  serverTimestamp 
} from 'firebase/firestore';

interface PaymentMethod {
  id: string;
  type: 'card' | 'wallet' | 'bank_transfer' | 'installment' | 'cash';
  provider: string;
  displayName: string;
  isActive: boolean;
  fees: {
    fixed: number;
    percentage: number;
  };
  limits: {
    min: number;
    max: number;
  };
  supportedCurrencies: string[];
}

interface PaymentRequest {
  orderId: string;
  amount: number;
  currency: string;
  customerId: string;
  paymentMethodId: string;
  description?: string;
  metadata?: Record<string, any>;
  installmentPlan?: {
    months: number;
    monthlyAmount: number;
    totalAmount: number;
    interestRate: number;
  };
}

interface PaymentResult {
  transactionId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  amount: number;
  fees: number;
  netAmount: number;
  paymentUrl?: string;
  qrCode?: string;
  expiresAt?: Date;
  errorMessage?: string;
}

interface SavedPaymentMethod {
  id: string;
  userId: string;
  type: 'card' | 'wallet';
  provider: string;
  maskedDetails: string;
  expiryDate?: string;
  isDefault: boolean;
  createdAt: Date;
}

class AdvancedPaymentService {
  private static readonly PAYMENT_METHODS: PaymentMethod[] = [
    {
      id: 'mada',
      type: 'card',
      provider: 'MADA',
      displayName: 'مدى',
      isActive: true,
      fees: { fixed: 0, percentage: 1.5 },
      limits: { min: 1, max: 50000 },
      supportedCurrencies: ['SAR']
    },
    {
      id: 'visa',
      type: 'card',
      provider: 'VISA',
      displayName: 'فيزا',
      isActive: true,
      fees: { fixed: 2, percentage: 2.5 },
      limits: { min: 1, max: 100000 },
      supportedCurrencies: ['SAR', 'USD', 'EUR']
    },
    {
      id: 'mastercard',
      type: 'card',
      provider: 'MASTERCARD',
      displayName: 'ماستركارد',
      isActive: true,
      fees: { fixed: 2, percentage: 2.5 },
      limits: { min: 1, max: 100000 },
      supportedCurrencies: ['SAR', 'USD', 'EUR']
    },
    {
      id: 'apple_pay',
      type: 'wallet',
      provider: 'APPLE',
      displayName: 'Apple Pay',
      isActive: true,
      fees: { fixed: 0, percentage: 2.0 },
      limits: { min: 1, max: 25000 },
      supportedCurrencies: ['SAR', 'USD']
    },
    {
      id: 'google_pay',
      type: 'wallet',
      provider: 'GOOGLE',
      displayName: 'Google Pay',
      isActive: true,
      fees: { fixed: 0, percentage: 2.0 },
      limits: { min: 1, max: 25000 },
      supportedCurrencies: ['SAR', 'USD']
    },
    {
      id: 'stc_pay',
      type: 'wallet',
      provider: 'STC',
      displayName: 'STC Pay',
      isActive: true,
      fees: { fixed: 0, percentage: 1.0 },
      limits: { min: 1, max: 30000 },
      supportedCurrencies: ['SAR']
    },
    {
      id: 'installment_3',
      type: 'installment',
      provider: 'TAMARA',
      displayName: 'تقسيط 3 أشهر',
      isActive: true,
      fees: { fixed: 0, percentage: 3.0 },
      limits: { min: 100, max: 20000 },
      supportedCurrencies: ['SAR']
    },
    {
      id: 'installment_6',
      type: 'installment',
      provider: 'TABBY',
      displayName: 'تقسيط 6 أشهر',
      isActive: true,
      fees: { fixed: 0, percentage: 5.0 },
      limits: { min: 200, max: 50000 },
      supportedCurrencies: ['SAR']
    },
    {
      id: 'paypal',
      type: 'wallet',
      provider: 'PAYPAL',
      displayName: 'PayPal',
      isActive: true,
      fees: { fixed: 0, percentage: 3.4 },
      limits: { min: 1, max: 100000 },
      supportedCurrencies: ['SAR', 'USD', 'EUR', 'GBP']
    }
  ];

  /**
   * الحصول على طرق الدفع المتاحة
   */
  static getAvailablePaymentMethods(amount: number, currency: string = 'SAR'): PaymentMethod[] {
    return this.PAYMENT_METHODS.filter(method => 
      method.isActive &&
      method.supportedCurrencies.includes(currency) &&
      amount >= method.limits.min &&
      amount <= method.limits.max
    );
  }

  /**
   * معالجة طلب الدفع
   */
  static async processPayment(paymentRequest: PaymentRequest): Promise<PaymentResult> {
    try {
      console.log('🔄 معالجة طلب الدفع:', paymentRequest);

      // التحقق من صحة البيانات
      await this.validatePaymentRequest(paymentRequest);

      // الحصول على طريقة الدفع
      const paymentMethod = this.PAYMENT_METHODS.find(m => m.id === paymentRequest.paymentMethodId);
      if (!paymentMethod) {
        throw new Error('طريقة الدفع غير مدعومة');
      }

      // حساب الرسوم
      const fees = this.calculateFees(paymentRequest.amount, paymentMethod);
      const netAmount = paymentRequest.amount - fees;

      // إنشاء معاملة الدفع
      const transaction = await this.createPaymentTransaction(paymentRequest, fees);

      // معالجة الدفع حسب النوع
      let result: PaymentResult;
      
      switch (paymentMethod.type) {
        case 'card':
          result = await this.processCardPayment(paymentRequest, paymentMethod, transaction.id);
          break;
        case 'wallet':
          result = await this.processWalletPayment(paymentRequest, paymentMethod, transaction.id);
          break;
        case 'installment':
          result = await this.processInstallmentPayment(paymentRequest, paymentMethod, transaction.id);
          break;
        case 'cash':
          result = await this.processCashPayment(paymentRequest, transaction.id);
          break;
        default:
          throw new Error('نوع الدفع غير مدعوم');
      }

      // تحديث حالة المعاملة
      await this.updateTransactionStatus(transaction.id, result.status, result);

      return {
        ...result,
        fees,
        netAmount
      };

    } catch (error) {
      console.error('❌ خطأ في معالجة الدفع:', error);
      throw new Error(`فشل في معالجة الدفع: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  }

  /**
   * معالجة دفع البطاقات
   */
  private static async processCardPayment(
    request: PaymentRequest,
    method: PaymentMethod,
    transactionId: string
  ): Promise<PaymentResult> {
    try {
      // تشفير بيانات الدفع
      const encryptedPaymentData = await ApexEncryptionEngine.encryptSensitiveData({
        amount: request.amount,
        currency: request.currency,
        orderId: request.orderId,
        customerId: request.customerId
      });

      // إرسال للمعالج المناسب
      const processorResponse = await this.callPaymentProcessor(method.provider, {
        transactionId,
        encryptedData: encryptedPaymentData,
        paymentMethod: method.id
      });

      return {
        transactionId,
        status: processorResponse.status,
        amount: request.amount,
        fees: 0, // سيتم حسابها في الدالة الرئيسية
        netAmount: 0, // سيتم حسابها في الدالة الرئيسية
        paymentUrl: processorResponse.paymentUrl,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000) // 15 دقيقة
      };

    } catch (error) {
      console.error('❌ خطأ في معالجة دفع البطاقة:', error);
      return {
        transactionId,
        status: 'failed',
        amount: request.amount,
        fees: 0,
        netAmount: 0,
        errorMessage: error instanceof Error ? error.message : 'خطأ في معالجة البطاقة'
      };
    }
  }

  /**
   * معالجة دفع المحافظ الرقمية
   */
  private static async processWalletPayment(
    request: PaymentRequest,
    method: PaymentMethod,
    transactionId: string
  ): Promise<PaymentResult> {
    try {
      // معالجة خاصة لـ PayPal
      if (method.provider === 'PAYPAL') {
        return await this.processPayPalPayment(request, transactionId);
      }

      // معالجة باقي المحافظ الرقمية (Apple Pay, Google Pay, STC Pay)
      const qrData = {
        transactionId,
        amount: request.amount,
        currency: request.currency,
        provider: method.provider,
        expiresAt: Date.now() + 10 * 60 * 1000 // 10 دقائق
      };

      const qrCode = await this.generateQRCode(qrData);

      return {
        transactionId,
        status: 'pending',
        amount: request.amount,
        fees: 0,
        netAmount: 0,
        qrCode,
        expiresAt: new Date(qrData.expiresAt)
      };

    } catch (error) {
      console.error('❌ خطأ في معالجة دفع المحفظة:', error);
      return {
        transactionId,
        status: 'failed',
        amount: request.amount,
        fees: 0,
        netAmount: 0,
        errorMessage: 'خطأ في إنشاء رمز الدفع'
      };
    }
  }

  /**
   * معالجة دفع PayPal
   */
  private static async processPayPalPayment(
    request: PaymentRequest,
    transactionId: string
  ): Promise<PaymentResult> {
    try {
      // إنشاء PayPal order
      const paypalOrderData = {
        intent: 'CAPTURE',
        purchase_units: [{
          reference_id: transactionId,
          amount: {
            currency_code: request.currency,
            value: request.amount.toFixed(2)
          },
          description: request.description || `Order #${request.orderId}`
        }],
        application_context: {
          brand_name: 'مِخْلاة',
          landing_page: 'NO_PREFERENCE',
          user_action: 'PAY_NOW',
          return_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success`,
          cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/cancel`
        }
      };

      // هنا يجب استدعاء PayPal API
      // في التطبيق الحقيقي، استخدم PayPal SDK
      const paypalResponse = await this.createPayPalOrder(paypalOrderData);

      return {
        transactionId,
        status: 'pending',
        amount: request.amount,
        fees: 0,
        netAmount: 0,
        paymentUrl: paypalResponse.approvalUrl,
        expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30 دقيقة
      };

    } catch (error) {
      console.error('❌ خطأ في معالجة PayPal:', error);
      return {
        transactionId,
        status: 'failed',
        amount: request.amount,
        fees: 0,
        netAmount: 0,
        errorMessage: 'فشل في إنشاء طلب PayPal'
      };
    }
  }

  /**
   * معالجة دفع التقسيط
   */
  private static async processInstallmentPayment(
    request: PaymentRequest,
    method: PaymentMethod,
    transactionId: string
  ): Promise<PaymentResult> {
    try {
      if (!request.installmentPlan) {
        throw new Error('خطة التقسيط مطلوبة');
      }

      // إنشاء خطة التقسيط
      const installmentPlan = await this.createInstallmentPlan(
        transactionId,
        request.installmentPlan,
        request.customerId
      );

      // إرسال للمعالج
      const processorResponse = await this.callInstallmentProcessor(method.provider, {
        transactionId,
        installmentPlan,
        customerData: {
          id: request.customerId,
          amount: request.amount
        }
      });

      return {
        transactionId,
        status: processorResponse.status,
        amount: request.amount,
        fees: 0,
        netAmount: 0,
        paymentUrl: processorResponse.approvalUrl
      };

    } catch (error) {
      console.error('❌ خطأ في معالجة دفع التقسيط:', error);
      return {
        transactionId,
        status: 'failed',
        amount: request.amount,
        fees: 0,
        netAmount: 0,
        errorMessage: 'خطأ في معالجة التقسيط'
      };
    }
  }

  /**
   * معالجة الدفع النقدي
   */
  private static async processCashPayment(
    request: PaymentRequest,
    transactionId: string
  ): Promise<PaymentResult> {
    return {
      transactionId,
      status: 'pending',
      amount: request.amount,
      fees: 0,
      netAmount: request.amount
    };
  }

  /**
   * حفظ طريقة دفع للمستخدم
   */
  static async savePaymentMethod(
    userId: string,
    paymentMethodData: {
      type: 'card' | 'wallet';
      provider: string;
      encryptedDetails: string;
      maskedDetails: string;
      expiryDate?: string;
    }
  ): Promise<string> {
    try {
      const savedMethodsRef = collection(db, 'saved_payment_methods');
      
      // التحقق من عدم وجود طريقة دفع مماثلة
      const existingQuery = query(
        savedMethodsRef,
        where('userId', '==', userId),
        where('maskedDetails', '==', paymentMethodData.maskedDetails)
      );
      
      const existingSnapshot = await getDocs(existingQuery);
      if (!existingSnapshot.empty) {
        throw new Error('طريقة الدفع محفوظة مسبقاً');
      }

      // حفظ طريقة الدفع الجديدة
      const docRef = await addDoc(savedMethodsRef, {
        userId,
        type: paymentMethodData.type,
        provider: paymentMethodData.provider,
        encryptedDetails: paymentMethodData.encryptedDetails,
        maskedDetails: paymentMethodData.maskedDetails,
        expiryDate: paymentMethodData.expiryDate,
        isDefault: false,
        isActive: true,
        createdAt: serverTimestamp()
      });

      console.log('✅ تم حفظ طريقة الدفع:', docRef.id);
      return docRef.id;

    } catch (error) {
      console.error('❌ خطأ في حفظ طريقة الدفع:', error);
      throw new Error('فشل في حفظ طريقة الدفع');
    }
  }

  /**
   * الحصول على طرق الدفع المحفوظة للمستخدم
   */
  static async getSavedPaymentMethods(userId: string): Promise<SavedPaymentMethod[]> {
    try {
      const savedMethodsRef = collection(db, 'saved_payment_methods');
      const q = query(
        savedMethodsRef,
        where('userId', '==', userId),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc')
      );

      const snapshot = await getDocs(q);
      const methods: SavedPaymentMethod[] = [];

      snapshot.forEach(doc => {
        const data = doc.data();
        methods.push({
          id: doc.id,
          userId: data.userId,
          type: data.type,
          provider: data.provider,
          maskedDetails: data.maskedDetails,
          expiryDate: data.expiryDate,
          isDefault: data.isDefault,
          createdAt: data.createdAt.toDate()
        });
      });

      return methods;

    } catch (error) {
      console.error('❌ خطأ في جلب طرق الدفع المحفوظة:', error);
      return [];
    }
  }

  // ===== PRIVATE HELPER METHODS =====

  private static async validatePaymentRequest(request: PaymentRequest): Promise<void> {
    if (!request.orderId || !request.customerId) {
      throw new Error('معرف الطلب ومعرف العميل مطلوبان');
    }

    if (request.amount <= 0) {
      throw new Error('مبلغ الدفع يجب أن يكون أكبر من صفر');
    }

    if (!request.paymentMethodId) {
      throw new Error('طريقة الدفع مطلوبة');
    }
  }

  private static calculateFees(amount: number, method: PaymentMethod): number {
    return method.fees.fixed + (amount * method.fees.percentage / 100);
  }

  private static async createPaymentTransaction(
    request: PaymentRequest,
    fees: number
  ): Promise<{ id: string }> {
    const transactionsRef = collection(db, 'payment_transactions');
    const docRef = await addDoc(transactionsRef, {
      orderId: request.orderId,
      customerId: request.customerId,
      amount: request.amount,
      currency: request.currency,
      fees,
      paymentMethodId: request.paymentMethodId,
      status: 'pending',
      createdAt: serverTimestamp(),
      metadata: request.metadata || {}
    });

    return { id: docRef.id };
  }

  private static async updateTransactionStatus(
    transactionId: string,
    status: string,
    result: PaymentResult
  ): Promise<void> {
    const transactionRef = doc(db, 'payment_transactions', transactionId);
    await updateDoc(transactionRef, {
      status,
      updatedAt: serverTimestamp(),
      result: {
        paymentUrl: result.paymentUrl,
        qrCode: result.qrCode,
        errorMessage: result.errorMessage
      }
    });
  }

  private static async callPaymentProcessor(provider: string, data: any): Promise<any> {
    // محاكاة استدعاء معالج الدفع
    console.log(`📞 استدعاء معالج الدفع ${provider}:`, data);
    
    // في التطبيق الحقيقي، هنا سيتم استدعاء API المعالج
    return {
      status: 'processing',
      paymentUrl: `https://payment.${provider.toLowerCase()}.com/pay/${data.transactionId}`
    };
  }

  private static async callInstallmentProcessor(provider: string, data: any): Promise<any> {
    console.log(`📞 استدعاء معالج التقسيط ${provider}:`, data);
    
    return {
      status: 'pending',
      approvalUrl: `https://${provider.toLowerCase()}.com/approve/${data.transactionId}`
    };
  }

  private static async generateQRCode(data: any): Promise<string> {
    // في التطبيق الحقيقي، استخدم مكتبة QR Code
    const qrData = Buffer.from(JSON.stringify(data)).toString('base64');
    return `data:image/png;base64,${qrData}`;
  }

  private static async createInstallmentPlan(
    transactionId: string,
    plan: any,
    customerId: string
  ): Promise<any> {
    const installmentRef = collection(db, 'installment_plans');
    const docRef = await addDoc(installmentRef, {
      transactionId,
      customerId,
      months: plan.months,
      monthlyAmount: plan.monthlyAmount,
      totalAmount: plan.totalAmount,
      interestRate: plan.interestRate,
      status: 'pending',
      createdAt: serverTimestamp()
    });

    return { id: docRef.id, ...plan };
  }

  /**
   * إنشاء طلب PayPal
   */
  private static async createPayPalOrder(orderData: any): Promise<{ approvalUrl: string; orderId: string }> {
    try {
      const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
      const clientSecret = process.env.PAYPAL_CLIENT_SECRET;
      const baseUrl = process.env.NODE_ENV === 'production'
        ? 'https://api.paypal.com'
        : 'https://api.sandbox.paypal.com';

      if (!clientId || !clientSecret) {
        throw new Error('PayPal credentials not configured');
      }

      // الحصول على access token
      const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

      const tokenResponse = await fetch(`${baseUrl}/v1/oauth2/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${auth}`
        },
        body: 'grant_type=client_credentials'
      });

      if (!tokenResponse.ok) {
        throw new Error('Failed to get PayPal access token');
      }

      const tokenData = await tokenResponse.json();
      const accessToken = tokenData.access_token;

      // إنشاء الطلب
      const orderResponse = await fetch(`${baseUrl}/v2/checkout/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify(orderData)
      });

      if (!orderResponse.ok) {
        const errorData = await orderResponse.json();
        throw new Error(`PayPal order creation failed: ${errorData.message || orderResponse.statusText}`);
      }

      const orderResult = await orderResponse.json();

      // البحث عن رابط الموافقة
      const approvalLink = orderResult.links?.find((link: any) => link.rel === 'approve');

      if (!approvalLink) {
        throw new Error('PayPal approval link not found');
      }

      return {
        orderId: orderResult.id,
        approvalUrl: approvalLink.href
      };

    } catch (error) {
      console.error('❌ خطأ في إنشاء طلب PayPal:', error);
      throw error;
    }
  }
}

export default AdvancedPaymentService;
export type { PaymentMethod, PaymentRequest, PaymentResult, SavedPaymentMethod };
