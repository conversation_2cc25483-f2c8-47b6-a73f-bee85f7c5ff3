// src/app/[locale]/forgot-password/page.tsx
import type { Locale } from '@/lib/i18n';
import ForgotPasswordClient from '@/components/auth/ForgotPasswordClient';

interface ForgotPasswordPageProps {
  params: { locale: Locale };
}

export default async function ForgotPasswordPage({ params }: ForgotPasswordPageProps) {
  const paramsData = await Promise.resolve(params);
  const locale = paramsData.locale;

  return <ForgotPasswordClient locale={locale} />;
}
