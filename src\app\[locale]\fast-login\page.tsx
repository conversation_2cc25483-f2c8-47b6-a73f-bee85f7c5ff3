// src/app/[locale]/fast-login/page.tsx
import type { Locale } from '@/lib/i18n';
import FastLoginForm from '@/components/auth/FastLoginForm';

interface FastLoginPageProps {
  params: { locale: Locale };
}

export default async function FastLoginPage({ params }: FastLoginPageProps) {
  const paramsData = await Promise.resolve(params);
  const locale = paramsData.locale;

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <FastLoginForm locale={locale} />
      </div>
    </div>
  );
}
