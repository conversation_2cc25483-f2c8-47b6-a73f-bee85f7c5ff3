// src/app/[locale]/merchant/reports/page.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import EnhancedKPIDashboard from '@/components/analytics/EnhancedKPIDashboard';
import AdvancedChartsPanel from '@/components/analytics/AdvancedChartsPanel';
import {
  Calendar,
  Download,
  FileText,
  TrendingUp,
  Users,
  ShoppingCart,
  DollarSign,
  BarChart3,
  PieChart,
  Activity,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { advancedReportsService, type SalesReport } from '@/services/advancedReportsService';

export default function MerchantReportsPage() {
  const { user } = useAuth();
  const { t } = useLocale();
  const [salesReport, setSalesReport] = useState<SalesReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [reportType, setReportType] = useState<'overview' | 'detailed' | 'comparison'>('overview');

  useEffect(() => {
    if (user?.uid) {
      loadSalesReport();
    }
  }, [user?.uid, selectedPeriod]);

  const loadSalesReport = async () => {
    if (!user?.uid) return;

    setLoading(true);
    setError(null);

    try {
      const endDate = new Date();
      const startDate = new Date();

      switch (selectedPeriod) {
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(startDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
      }

      const report = await advancedReportsService.generateSalesReport(user.uid, {
        startDate,
        endDate,
        type: selectedPeriod === '7d' ? 'weekly' :
              selectedPeriod === '30d' ? 'monthly' :
              selectedPeriod === '90d' ? 'quarterly' : 'yearly'
      });

      setSalesReport(report);
    } catch (err) {
      console.error('خطأ في تحميل التقرير:', err);
      setError('فشل في تحميل التقرير. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: 'pdf' | 'excel' | 'csv') => {
    // TODO: تطبيق تصدير التقارير
    console.log(`تصدير التقرير بصيغة: ${format}`);
    alert(`سيتم تطبيق تصدير التقارير بصيغة ${format} قريباً`);
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} ريال`;
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>

        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {[...Array(4)].map((_, i) => (
                  <Skeleton key={i} className="h-24" />
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-96" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button onClick={loadSalesReport} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            إعادة المحاولة
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* العنوان والأدوات */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">التقارير والتحليلات</h1>
            <p className="text-muted-foreground">
              تحليل شامل لأداء متجرك ومبيعاتك
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Select value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7 أيام</SelectItem>
                <SelectItem value="30d">30 يوم</SelectItem>
                <SelectItem value="90d">90 يوم</SelectItem>
                <SelectItem value="1y">سنة</SelectItem>
              </SelectContent>
            </Select>

            <Button onClick={loadSalesReport} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-1" />
              تحديث
            </Button>

            <Button onClick={() => handleExport('pdf')} variant="outline" size="sm">
              <Download className="w-4 h-4 mr-1" />
              تصدير
            </Button>
          </div>
        </div>

        {/* معلومات التقرير */}
        {salesReport && (
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              <span>
                من {salesReport.period.startDate.toDate().toLocaleDateString('ar-SA')}
                إلى {salesReport.period.endDate.toDate().toLocaleDateString('ar-SA')}
              </span>
            </div>
            <Badge variant="secondary">
              {salesReport.period.type === 'weekly' ? 'أسبوعي' :
               salesReport.period.type === 'monthly' ? 'شهري' :
               salesReport.period.type === 'quarterly' ? 'ربع سنوي' : 'سنوي'}
            </Badge>
          </div>
        )}
      </div>

      {/* التبويبات الرئيسية */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            نظرة عامة
          </TabsTrigger>
          <TabsTrigger value="sales" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            المبيعات
          </TabsTrigger>
          <TabsTrigger value="customers" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            العملاء
          </TabsTrigger>
          <TabsTrigger value="products" className="flex items-center gap-2">
            <ShoppingCart className="h-4 w-4" />
            المنتجات
          </TabsTrigger>
        </TabsList>

        {/* نظرة عامة */}
        <TabsContent value="overview" className="space-y-6">
          <EnhancedKPIDashboard
            timeRange={selectedPeriod}
            onTimeRangeChange={setSelectedPeriod}
            onRefresh={loadSalesReport}
            loading={loading}
          />

          <AdvancedChartsPanel
            salesReport={salesReport}
            loading={loading}
            onRefresh={loadSalesReport}
            onExport={handleExport}
          />
        </TabsContent>

        {/* المبيعات */}
        <TabsContent value="sales" className="space-y-6">
          {salesReport && (
            <>
              {/* ملخص المبيعات */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      إجمالي الإيرادات
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(salesReport.summary.totalRevenue)}
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      <TrendingUp className="w-4 h-4 text-green-500" />
                      <span className="text-green-500">
                        +{formatPercentage(salesReport.summary.growthRate)}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      إجمالي الطلبات
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {salesReport.summary.totalOrders.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      طلب مكتمل
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      متوسط قيمة الطلب
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(salesReport.summary.averageOrderValue)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      لكل طلب
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      معدل التحويل
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatPercentage(salesReport.summary.conversionRate)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      من الزوار
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* الرسم البياني للمبيعات */}
              <AdvancedChartsPanel
                salesReport={salesReport}
                loading={loading}
                onRefresh={loadSalesReport}
                onExport={handleExport}
              />
            </>
          )}
        </TabsContent>

        {/* العملاء */}
        <TabsContent value="customers" className="space-y-6">
          {salesReport && (
            <>
              {/* إحصائيات العملاء */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      إجمالي العملاء
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {salesReport.customerAnalytics.totalCustomers.toLocaleString()}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      عملاء جدد
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {salesReport.customerAnalytics.newCustomers.toLocaleString()}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      معدل الاحتفاظ
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatPercentage(salesReport.customerAnalytics.customerRetentionRate)}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      القيمة الدائمة للعميل
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(salesReport.customerAnalytics.averageCustomerLifetimeValue)}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* أفضل العملاء */}
              <Card>
                <CardHeader>
                  <CardTitle>أفضل العملاء</CardTitle>
                  <CardDescription>
                    العملاء الأكثر إنفاقاً في الفترة المحددة
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {salesReport.customerAnalytics.topCustomers.slice(0, 5).map((customer, index) => (
                      <div key={customer.customerId} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-bold">
                            {index + 1}
                          </div>
                          <div>
                            <div className="font-medium">{customer.customerName}</div>
                            <div className="text-sm text-muted-foreground">
                              {customer.totalOrders} طلب
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold">{formatCurrency(customer.totalSpent)}</div>
                          <div className="text-sm text-muted-foreground">
                            متوسط: {formatCurrency(customer.averageOrderValue)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        {/* المنتجات */}
        <TabsContent value="products" className="space-y-6">
          {salesReport && (
            <Card>
              <CardHeader>
                <CardTitle>أداء المنتجات</CardTitle>
                <CardDescription>
                  تحليل أداء المنتجات الأكثر مبيعاً
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {salesReport.productPerformance.slice(0, 10).map((product, index) => (
                    <div key={product.productId} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{product.productName}</div>
                          <div className="text-sm text-muted-foreground">
                            {product.category} • {product.totalSales} مبيعة
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{formatCurrency(product.totalRevenue)}</div>
                        <div className="text-sm text-muted-foreground">
                          تقييم: {product.averageRating.toFixed(1)}/5
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
