# 🏗️ هيكل المشروع المتقدم - مِخْلاة

<div align="center">

![Project Structure](https://img.shields.io/badge/Project_Structure-مِخْلاة-blue?style=for-the-badge)
![Files](https://img.shields.io/badge/Files-300+-green?style=for-the-badge)
![Components](https://img.shields.io/badge/Components-150+-orange?style=for-the-badge)
![Lines of Code](https://img.shields.io/badge/Lines_of_Code-50K+-red?style=for-the-badge)

**📋 دليل شامل لبنية وتنظيم مشروع مِخْلاة**

</div>

---

## 📖 نظرة عامة

هذا المستند يوضح البنية التفصيلية لمشروع **مِخْلاة**، منصة التجارة الإلكترونية المحلية. المشروع منظم بطريقة احترافية تتبع أفضل الممارسات في تطوير تطبيقات Next.js الحديثة.

### 🎯 مبادئ التنظيم

- **🔄 Separation of Concerns** - فصل الاهتمامات
- **📦 Modular Architecture** - بنية معيارية
- **🎨 Component-Based Design** - تصميم قائم على المكونات
- **🔧 Reusability** - قابلية إعادة الاستخدام
- **📱 Scalability** - قابلية التوسع
- **🧪 Testability** - قابلية الاختبار

---

## 🗂️ الهيكل الرئيسي

```
mikhla/
├── 📁 src/                          # المجلد الرئيسي للكود المصدري
├── 📁 cypress/                      # اختبارات Cypress
├── 📁 docs/                         # التوثيق والمستندات
├── 📁 public/                       # الملفات العامة
├── 📁 scripts/                      # سكريبتات المساعدة
├── 📄 package.json                  # تبعيات المشروع
├── 📄 next.config.ts                # إعدادات Next.js
├── 📄 tailwind.config.ts            # إعدادات Tailwind CSS
├── 📄 tsconfig.json                 # إعدادات TypeScript
├── 📄 cypress.config.ts             # إعدادات Cypress
├── 📄 firebase.json                 # إعدادات Firebase
└── 📄 README.md                     # الملف التعريفي
```

---

## 📁 src/ - المجلد الرئيسي

### 🌐 app/ - صفحات Next.js App Router

```
src/app/
├── 📁 [locale]/                     # صفحات متعددة اللغات (ar/en)
│   ├── 📁 admin/                    # 👨‍💼 لوحة تحكم الإدارة
│   │   ├── 📁 dashboard/            # لوحة التحكم الرئيسية
│   │   │   └── 📄 page.tsx          # صفحة الإحصائيات الشاملة
│   │   ├── 📁 users/                # إدارة المستخدمين
│   │   │   ├── 📄 page.tsx          # قائمة المستخدمين
│   │   │   └── 📁 [id]/             # تفاصيل مستخدم محدد
│   │   ├── 📁 categories/           # إدارة الفئات
│   │   │   ├── 📄 page.tsx          # إدارة فئات المنتجات
│   │   │   └── 📁 [id]/             # تعديل فئة محددة
│   │   ├── 📁 review-reports/       # تقارير المراجعات
│   │   │   └── 📄 page.tsx          # مراجعة التقارير المرسلة
│   │   ├── 📁 merchant-approvals/   # موافقات التجار (محسن)
│   │   │   └── 📄 page.tsx          # موافقة التجار مع الذكاء الاصطناعي
│   │   ├── 📁 representative-approvals/ # موافقات المندوبين (محسن)
│   │   │   └── 📄 page.tsx          # موافقة المندوبين مع الذكاء الاصطناعي
│   │   ├── 📁 ai-dashboard/         # 🤖 لوحة مراقبة النظام الذكي (جديد!)
│   │   │   └── 📄 page.tsx          # لوحة مراقبة شاملة للذكاء الاصطناعي (300+ سطر)
│   │   └── 📁 settings/             # إعدادات النظام
│   │       └── 📄 page.tsx          # إعدادات عامة للمنصة
│   ├── 📁 merchant/                 # 🏪 واجهة التجار
│   │   ├── 📁 dashboard/            # لوحة تحكم التاجر
│   │   │   └── 📄 page.tsx          # إحصائيات المتجر
│   │   ├── 📁 products/             # إدارة المنتجات
│   │   │   ├── 📄 page.tsx          # قائمة المنتجات
│   │   │   ├── 📁 add/              # إضافة منتج جديد
│   │   │   └── 📁 [id]/             # تعديل منتج محدد
│   │   ├── 📁 orders/               # إدارة الطلبات
│   │   │   ├── 📄 page.tsx          # قائمة الطلبات
│   │   │   └── 📁 [id]/             # تفاصيل طلب محدد
│   │   ├── 📁 store/                # إعدادات المتجر
│   │   │   ├── 📄 page.tsx          # معلومات المتجر الأساسية
│   │   │   └── 📁 settings/         # إعدادات متقدمة
│   │   ├── 📁 reports/              # التقارير والتحليلات
│   │   │   └── 📄 page.tsx          # تقارير المبيعات والأداء
│   │   └── 📁 inventory/            # إدارة المخزون
│   │       └── 📄 page.tsx          # مراقبة وإدارة المخزون
│   ├── 📁 representative/           # 🚚 واجهة مندوبي التوصيل
│   │   ├── 📁 dashboard/            # لوحة تحكم المندوب
│   │   │   └── 📄 page.tsx          # إحصائيات التوصيل
│   │   ├── 📁 orders/               # طلبات التوصيل
│   │   │   ├── 📄 page.tsx          # قائمة الطلبات المتاحة
│   │   │   └── 📁 [id]/             # تفاصيل طلب توصيل
│   │   ├── 📁 earnings/             # الأرباح والعمولات
│   │   │   └── 📄 page.tsx          # تقارير الأرباح
│   │   ├── 📁 profile/              # الملف الشخصي
│   │   │   └── 📄 page.tsx          # معلومات المندوب
│   │   └── 📁 signup/               # تسجيل مندوب جديد
│   │       └── 📄 page.tsx          # نموذج التسجيل
│   ├── 📁 products/                 # 🛍️ صفحات المنتجات
│   │   ├── 📄 page.tsx              # قائمة جميع المنتجات
│   │   ├── 📁 [id]/                 # تفاصيل منتج محدد
│   │   │   ├── 📄 page.tsx          # صفحة المنتج
│   │   │   └── 📁 reviews/          # مراجعات المنتج
│   │   └── 📁 category/             # منتجات حسب الفئة
│   │       └── 📁 [slug]/           # فئة محددة
│   ├── 📁 stores/                   # 🏪 صفحات المتاجر
│   │   ├── 📄 page.tsx              # قائمة جميع المتاجر
│   │   └── 📁 [id]/                 # تفاصيل متجر محدد
│   │       ├── 📄 page.tsx          # صفحة المتجر
│   │       └── 📁 reviews/          # مراجعات المتجر
│   ├── 📁 categories/               # 📂 صفحات الفئات
│   │   ├── 📄 page.tsx              # جميع الفئات
│   │   └── 📁 [slug]/               # فئة محددة
│   ├── 📁 checkout/                 # 💳 صفحة الدفع
│   │   └── 📄 page.tsx              # عملية الدفع والشراء
│   ├── 📁 profile/                  # 👤 الملف الشخصي
│   │   └── 📄 page.tsx              # معلومات المستخدم
│   ├── 📁 map/                      # 🗺️ الخريطة التفاعلية
│   │   └── 📄 page.tsx              # خريطة المتاجر القريبة
│   ├── 📁 search/                   # 🔍 البحث
│   │   └── 📄 page.tsx              # نتائج البحث
│   ├── 📁 login/                    # 🔐 تسجيل الدخول
│   │   └── 📄 page.tsx              # صفحة تسجيل الدخول
│   ├── 📁 signup/                   # ✍️ إنشاء حساب
│   │   └── 📄 page.tsx              # صفحة التسجيل
│   ├── 📁 pricing/                  # 💰 خطط الاشتراك
│   │   └── 📄 page.tsx              # عرض الخطط والأسعار
│   ├── 📁 features/                 # ⭐ المميزات
│   │   └── 📄 page.tsx              # عرض مميزات المنصة
│   ├── 📄 layout.tsx                # التخطيط الرئيسي للصفحات
│   └── 📄 page.tsx                  # الصفحة الرئيسية
├── 📁 api/                          # 🌐 APIs الخادم
│   ├── 📁 ai/                       # 🤖 APIs الذكاء الاصطناعي (جديد!)
│   │   ├── 📁 analyze-document/     # تحليل مستندات التجار
│   │   │   └── 📄 route.ts          # API تحليل المستندات (محسن)
│   │   ├── 📁 analyze-representative-documents/ # تحليل مستندات المندوبين
│   │   │   └── 📄 route.ts          # API تحليل مستندات المندوبين (جديد)
│   │   ├── 📁 auto-approve-merchant/ # موافقة تلقائية للتجار
│   │   │   └── 📄 route.ts          # API الموافقة التلقائية للتجار (جديد)
│   │   ├── 📁 auto-approve-representative/ # موافقة تلقائية للمندوبين
│   │   │   └── 📄 route.ts          # API الموافقة التلقائية للمندوبين (جديد)
│   │   └── 📁 system-metrics/       # إحصائيات النظام الذكي
│   │       └── 📄 route.ts          # API إحصائيات النظام الذكي (جديد)
│   └── 📁 [other-apis]/             # باقي APIs التطبيق
├── 📄 layout.tsx                    # التخطيط العام للتطبيق
├── 📄 page.tsx                      # صفحة إعادة التوجيه
├── 📄 globals.css                   # الأنماط العامة
├── 📄 error.tsx                     # صفحة الأخطاء العامة
└── 📄 global-error.tsx              # صفحة الأخطاء الشاملة
```

---

### 🧩 components/ - مكونات React

```
src/components/
├── 📁 ui/                           # 🎨 مكونات واجهة المستخدم الأساسية
│   ├── 📄 button.tsx                # أزرار متنوعة
│   ├── 📄 input.tsx                 # حقول الإدخال
│   ├── 📄 card.tsx                  # بطاقات العرض
│   ├── 📄 dialog.tsx                # نوافذ حوارية
│   ├── 📄 dropdown-menu.tsx         # قوائم منسدلة
│   ├── 📄 form.tsx                  # نماذج الإدخال
│   ├── 📄 table.tsx                 # جداول البيانات
│   ├── 📄 tabs.tsx                  # تبويبات
│   ├── 📄 toast.tsx                 # رسائل التنبيه
│   ├── 📄 skeleton.tsx              # هياكل التحميل
│   ├── 📄 progress.tsx              # أشرطة التقدم
│   ├── 📄 chart.tsx                 # الرسوم البيانية
│   └── 📄 loading-states.tsx        # حالات التحميل
├── 📁 admin/                        # 👨‍💼 مكونات الإدارة
│   ├── 📄 AdminDashboard.tsx        # لوحة التحكم الرئيسية
│   ├── 📄 StatsCard.tsx             # بطاقات الإحصائيات
│   ├── 📄 RealtimeChart.tsx         # رسوم بيانية مباشرة
│   ├── 📄 ActivityFeed.tsx          # تغذية النشاط
│   ├── 📄 AlertsPanel.tsx           # لوحة التنبيهات
│   ├── 📄 TopPerformers.tsx         # أفضل المؤدين
│   ├── 📄 GeographicMap.tsx         # الخريطة الجغرافية
│   ├── 📄 UsersManagement.tsx       # إدارة المستخدمين
│   ├── 📄 UsersList.tsx             # قائمة المستخدمين
│   ├── 📄 UserEditDialog.tsx        # تعديل المستخدم
│   ├── 📄 CategoriesManagement.tsx  # إدارة الفئات
│   ├── 📄 CategoryTree.tsx          # شجرة الفئات
│   ├── 📄 SystemSettings.tsx        # إعدادات النظام
│   ├── 📁 ai/                       # 🤖 مكونات الذكاء الاصطناعي (جديد!)
│   │   ├── 📄 AIApprovalButton.tsx  # زر الموافقة الذكية (300+ سطر)
│   │   ├── 📄 AISystemDashboard.tsx # لوحة مراقبة النظام الذكي (430+ سطر)
│   │   └── 📄 DocumentAnalysisViewer.tsx # عارض نتائج التحليل (300+ سطر)
│   └── 📁 settings/                 # إعدادات فرعية
├── 📁 merchant/                     # 🏪 مكونات التجار
│   ├── 📄 DashboardStats.tsx        # إحصائيات المتجر
│   ├── 📄 AddProductForm.tsx        # نموذج إضافة منتج
│   ├── 📄 QuickActions.tsx          # إجراءات سريعة
│   ├── 📄 RecentOrders.tsx          # الطلبات الحديثة
│   └── 📄 StoreSettings.tsx         # إعدادات المتجر
├── 📁 customer/                     # 👤 مكونات العملاء
│   ├── 📄 FilterSidebar.tsx         # شريط الفلاتر الجانبي
│   ├── 📄 OrderTracker.tsx          # تتبع الطلبات
│   ├── 📄 SearchBar.tsx             # شريط البحث
│   ├── 📄 StoreCard.tsx             # بطاقة المتجر
│   └── 📄 ProductGrid.tsx           # شبكة المنتجات
├── 📁 representative/               # 🚚 مكونات المندوبين
│   ├── 📄 LocationTracker.tsx       # تتبع الموقع
│   ├── 📄 RepresentativeSignupForm.tsx # نموذج تسجيل المندوب
│   ├── 📄 DeliveryMap.tsx           # خريطة التوصيل
│   └── 📄 EarningsChart.tsx         # رسم بياني للأرباح
├── 📁 auth/                         # 🔐 مكونات المصادقة
│   ├── 📄 LoginForm.tsx             # نموذج تسجيل الدخول
│   ├── 📄 SignupForm.tsx            # نموذج التسجيل
│   ├── 📄 GoogleSignInButton.tsx    # زر تسجيل الدخول بجوجل
│   ├── 📄 UserTypeSelection.tsx     # اختيار نوع المستخدم
│   ├── 📄 TermsAndConditionsModal.tsx # شروط وأحكام
│   └── 📄 AuthErrorHandler.tsx      # معالج أخطاء المصادقة
├── 📁 cart/                         # 🛒 مكونات سلة التسوق
│   ├── 📄 CartProvider.tsx          # موفر سياق السلة
│   ├── 📄 CartSidebar.tsx           # الشريط الجانبي للسلة
│   ├── 📄 CartItem.tsx              # عنصر في السلة
│   ├── 📄 CartIcon.tsx              # أيقونة السلة
│   └── 📄 AddToCartButton.tsx       # زر إضافة للسلة
├── 📁 reviews/                      # ⭐ مكونات التقييمات
│   ├── 📄 ReviewsList.tsx           # قائمة المراجعات
│   ├── 📄 AddReviewForm.tsx         # نموذج إضافة مراجعة
│   ├── 📄 ReviewStats.tsx           # إحصائيات التقييمات
│   ├── 📄 ReviewsSection.tsx        # قسم المراجعات
│   ├── 📄 ReportReviewDialog.tsx    # حوار الإبلاغ عن مراجعة
│   ├── 📄 ReviewImagesDialog.tsx    # حوار صور المراجعة
│   └── 📄 index.ts                  # فهرس المكونات
├── 📁 notifications/                # 🔔 مكونات الإشعارات
│   └── 📄 NotificationCenter.tsx    # مركز الإشعارات
├── 📁 map/                          # 🗺️ مكونات الخرائط
│   ├── 📄 LocationMap.tsx           # الخريطة الرئيسية
│   ├── 📄 StoreMarker.tsx           # علامة المتجر
│   ├── 📄 StorePopup.tsx            # نافذة معلومات المتجر
│   ├── 📄 MapModal.tsx              # نافذة الخريطة
│   ├── 📄 LocationPermission.tsx    # إذن الموقع
│   └── 📄 DistanceCalculator.tsx    # حاسبة المسافة
├── 📁 layout/                       # 🏗️ مكونات التخطيط
│   ├── 📄 Header.tsx                # رأس الصفحة
│   ├── 📄 Footer.tsx                # تذييل الصفحة
│   └── 📄 LanguageSwitcher.tsx      # مبدل اللغة
├── 📁 common/                       # 🔄 مكونات مشتركة
│   ├── 📄 ProductCard.tsx           # بطاقة المنتج
│   ├── 📄 NetworkErrorHandler.tsx   # معالج أخطاء الشبكة
│   └── 📄 LoadingSpinner.tsx        # دوار التحميل
├── 📁 features/                     # ⭐ مكونات المميزات
│   ├── 📄 FeatureCard.tsx           # بطاقة الميزة
│   └── 📄 FeaturesTabs.tsx          # تبويبات المميزات
├── 📁 pricing/                      # 💰 مكونات التسعير
│   ├── 📄 PricingTabs.tsx           # تبويبات الأسعار
│   └── 📄 SubscriptionCard.tsx      # بطاقة الاشتراك
├── 📁 profile/                      # 👤 مكونات الملف الشخصي
│   ├── 📄 ProfileTabs.tsx           # تبويبات الملف الشخصي
│   ├── 📄 OrderHistory.tsx          # تاريخ الطلبات
│   ├── 📄 PasswordChange.tsx        # تغيير كلمة المرور
│   ├── 📄 ProfileCustomization.tsx  # تخصيص الملف الشخصي
│   └── 📄 SubscriptionInfo.tsx      # معلومات الاشتراك
├── 📁 providers/                    # 🔌 موفري السياق
│   ├── 📄 FontProvider.tsx          # موفر الخطوط
│   ├── 📄 LocaleProvider.tsx        # موفر اللغة
│   └── 📄 NoSSR.tsx                 # مكون بدون SSR
├── 📁 debug/                        # 🐛 مكونات التصحيح
│   ├── 📄 ConsoleErrorHandler.tsx   # معالج أخطاء وحدة التحكم
│   ├── 📄 ErrorSuppressionSetup.tsx # إعداد قمع الأخطاء
│   └── 📄 NetworkTestPanel.tsx      # لوحة اختبار الشبكة
├── 📁 error/                        # ❌ مكونات الأخطاء
│   └── 📄 NetworkErrorBoundary.tsx  # حدود أخطاء الشبكة
├── 📁 performance/                  # ⚡ مكونات الأداء
│   └── 📄 LazySection.tsx           # قسم التحميل الكسول
└── 📄 Logo.tsx                      # شعار التطبيق
```

---

### 🪝 hooks/ - Custom React Hooks

```
src/hooks/
├── 📄 useAuth.ts                    # 🔐 إدارة المصادقة والجلسات
├── 📄 useOrders.ts                  # 🛒 إدارة الطلبات والحالات
├── 📄 useProducts.ts                # 📦 إدارة المنتجات والفلترة
├── 📄 useReviews.ts                 # ⭐ إدارة التقييمات والمراجعات
├── 📄 useLocation.ts                # 📍 إدارة الموقع والخرائط
├── 📄 useCart.ts                    # 🛒 إدارة سلة التسوق
├── 📄 useNotifications.ts           # 🔔 إدارة الإشعارات
├── 📄 useAdminStats.ts              # 📊 إحصائيات الإدارة
├── 📄 useLocalStorage.ts            # 💾 التخزين المحلي
├── 📄 useDebounce.ts                # ⏱️ تأخير التنفيذ
├── 📄 usePagination.ts              # 📄 التصفح بين الصفحات
├── 📄 useSearch.ts                  # 🔍 البحث والفلترة
├── 📄 useUpload.ts                  # 📤 رفع الملفات
├── 📄 useGeolocation.ts             # 🌍 تحديد الموقع الجغرافي
├── 📄 use-mobile.ts                 # 📱 كشف الأجهزة المحمولة
└── 📄 useWebSocket.ts               # 🔌 الاتصال المباشر
```

---

### 🔧 services/ - خدمات API والأعمال

```
src/services/
├── 📄 authService.ts                # 🔐 خدمة المصادقة والتحقق
├── 📄 orderService.ts               # 🛒 خدمة إدارة الطلبات
├── 📄 productService.ts             # 📦 خدمة إدارة المنتجات
├── 📄 reviewService.ts              # ⭐ خدمة التقييمات والمراجعات
├── 📄 notificationService.ts        # 🔔 خدمة الإشعارات
├── 📄 adminDashboardService.ts      # 📊 خدمة لوحة الإدارة
├── 📄 userService.ts                # 👤 خدمة إدارة المستخدمين
├── 📄 storeService.ts               # 🏪 خدمة إدارة المتاجر
├── 📄 categoryService.ts            # 📂 خدمة إدارة الفئات
├── 📄 paymentService.ts             # 💳 خدمة الدفع الإلكتروني
├── 📄 deliveryService.ts            # 🚚 خدمة التوصيل
├── 📄 uploadService.ts              # 📤 خدمة رفع الملفات
├── 📄 emailService.ts               # 📧 خدمة البريد الإلكتروني
├── 📄 smsService.ts                 # 📱 خدمة الرسائل النصية
├── 📄 analyticsService.ts           # 📈 خدمة التحليلات
├── 📄 cacheService.ts               # 💾 خدمة التخزين المؤقت
├── 📁 ai/                           # 🤖 خدمات الذكاء الاصطناعي (جديد!)
│   ├── 📄 aiApprovalService.ts      # 🏪 خدمة الموافقة الذكية للتجار (محسن)
│   ├── 📄 representativeAIApprovalService.ts # 🚚 خدمة الموافقة الذكية للمندوبين (650+ سطر)
│   ├── 📄 documentAnalysisService.ts # 📄 خدمة تحليل المستندات المحسنة (450+ سطر)
│   └── 📄 aiConfigService.ts        # ⚙️ خدمة إعدادات النظام الذكي (200+ سطر)
```

---

### 🌐 context/ - React Context للحالة العامة

```
src/context/
├── 📄 AuthContext.tsx               # 🔐 سياق المصادقة والمستخدم
├── 📄 CartContext.tsx               # 🛒 سياق سلة التسوق
├── 📄 NotificationContext.tsx       # 🔔 سياق الإشعارات
├── 📄 ThemeContext.tsx              # 🎨 سياق الثيم والألوان
├── 📄 LanguageContext.tsx           # 🌐 سياق اللغة والترجمة
└── 📄 LocationContext.tsx           # 📍 سياق الموقع الجغرافي
```

---

### 📚 lib/ - مكتبات ومساعدات

```
src/lib/
├── 📄 firebase.ts                   # 🔥 إعداد وتكوين Firebase
├── 📄 cloudinary.ts                 # ☁️ إعداد وتكوين Cloudinary
├── 📄 utils.ts                      # 🛠️ دوال مساعدة عامة
├── 📄 validations.ts                # ✅ قواعد التحقق والتصديق
├── 📄 constants.ts                  # 📋 الثوابت العامة
├── 📄 formatters.ts                 # 📝 دوال التنسيق (تاريخ، عملة، نص)
├── 📄 api.ts                        # 🌐 إعدادات API العامة
├── 📄 auth.ts                       # 🔐 مساعدات المصادقة
├── 📄 database.ts                   # 🗄️ مساعدات قاعدة البيانات
├── 📄 storage.ts                    # 💾 مساعدات التخزين
├── 📄 encryption.ts                 # 🔒 دوال التشفير
└── 📄 logger.ts                     # 📝 نظام السجلات
```

---

### 🏷️ types/ - تعريفات TypeScript

```
src/types/
├── 📄 index.ts                      # 🏠 الأنواع الرئيسية والعامة
├── 📄 auth.ts                       # 🔐 أنواع المصادقة والمستخدمين
├── 📄 products.ts                   # 📦 أنواع المنتجات والفئات
├── 📄 orders.ts                     # 🛒 أنواع الطلبات والحالات
├── 📄 stores.ts                     # 🏪 أنواع المتاجر والتجار
├── 📄 reviews.ts                    # ⭐ أنواع التقييمات والمراجعات
├── 📄 notifications.ts              # 🔔 أنواع الإشعارات
├── 📄 payments.ts                   # 💳 أنواع المدفوعات
├── 📄 delivery.ts                   # 🚚 أنواع التوصيل والمندوبين
├── 📄 admin.ts                      # 👨‍💼 أنواع الإدارة والإحصائيات
├── 📄 api.ts                        # 🌐 أنواع API والاستجابات
└── 📄 ui.ts                         # 🎨 أنواع واجهة المستخدم
```

---

### 🌍 locales/ - ملفات الترجمة

```
src/locales/
├── 📄 ar.json                       # 🇸🇦 الترجمة العربية (1400+ مفتاح)
└── 📄 en.json                       # 🇺🇸 الترجمة الإنجليزية (1400+ مفتاح)
```

---

### 📋 constants/ - الثوابت والإعدادات

```
src/constants/
├── 📄 categories.ts                 # 📂 فئات المنتجات المحددة مسبقاً
├── 📄 orderStatus.ts                # 📊 حالات الطلبات المختلفة
├── 📄 userRoles.ts                  # 👥 أدوار المستخدمين والصلاحيات
├── 📄 paymentMethods.ts             # 💳 طرق الدفع المتاحة
├── 📄 deliveryZones.ts              # 🗺️ مناطق التوصيل
├── 📄 subscriptionPlans.ts          # 💰 خطط الاشتراك
├── 📄 notificationTypes.ts          # 🔔 أنواع الإشعارات
└── 📄 apiEndpoints.ts               # 🌐 نقاط النهاية للـ API
```

---

### ⚙️ middleware.ts - وسطاء Next.js

```
src/middleware.ts                    # 🛡️ وسطاء التحقق والتوجيه
```

---

## 📁 المجلدات الأخرى

### 🧪 cypress/ - اختبارات Cypress

```
cypress/
├── 📁 e2e/                          # اختبارات End-to-End
│   ├── 📄 admin-dashboard.cy.ts     # اختبارات لوحة الإدارة
│   ├── 📄 merchant-dashboard.cy.ts  # اختبارات لوحة التاجر
│   ├── 📄 customer-flow.cy.ts       # اختبارات تدفق العميل
│   ├── 📄 orders-payment-flow.cy.ts # اختبارات الطلبات والدفع
│   ├── 📄 delivery-system.cy.ts     # اختبارات نظام التوصيل
│   ├── 📄 ai-approval-system.cy.ts  # 🤖 اختبارات النظام الذكي (جديد! 300+ سطر)
│   └── 📄 complete-system-integration.cy.ts # اختبارات التكامل الكامل
├── 📁 fixtures/                     # بيانات الاختبار الثابتة
│   ├── 📄 users.json                # بيانات المستخدمين التجريبية
│   ├── 📄 products.json             # بيانات المنتجات التجريبية
│   └── 📄 orders.json               # بيانات الطلبات التجريبية
├── 📁 support/                      # ملفات الدعم والإعدادات
│   ├── 📄 commands.ts               # أوامر مخصصة للاختبار
│   ├── 📄 e2e.ts                    # إعدادات اختبارات E2E
│   └── 📄 component.ts              # إعدادات اختبارات المكونات
├── 📁 scripts/                      # سكريبتات الاختبارات
│   ├── 📄 run-analytics-tests.js    # سكريبت تشغيل اختبارات التحليلات
│   └── 📄 test-analytics.bat        # سكريبت Windows لاختبارات التحليلات
└── 📄 README.md                     # دليل الاختبارات
```

---

### 📚 docs/ - التوثيق والمستندات

```
docs/
├── 📄 CHANGELOG.md                  # 📝 سجل التغييرات والتحديثات
├── 📄 CHANGELOG_PART2.md            # 📝 الجزء الثاني من سجل التغييرات
├── 📄 development-plan.md           # 📋 خطة التطوير المفصلة
├── 📄 USER_GUIDE.md                 # 📖 دليل المستخدم الشامل
├── 📄 PROJECT_STRUCTURE.md          # 🏗️ هيكل المشروع (هذا الملف)
├── 📄 PERFORMANCE_IMPROVEMENTS.md   # ⚡ تحسينات الأداء
├── 📄 authentication-analysis.md    # 🔐 تحليل نظام المصادقة
├── 📄 blueprint.md                  # 📐 مخطط المشروع العام
├── 📄 console-errors-fix-report.md  # 🐛 تقرير إصلاح أخطاء وحدة التحكم
├── 📄 customer-interface-completion-summary.md # 👤 ملخص إكمال واجهة العملاء
├── 📄 delivery-representatives-development-plan.md # 🚚 خطة تطوير نظام المندوبين
├── 📄 final-testing-summary.md      # 🧪 ملخص الاختبارات النهائية
├── 📄 google-auth-setup.md          # 🔐 إعداد مصادقة Google
├── 📄 implementation-summary.md     # 📊 ملخص التنفيذ
├── 📄 merchant-requirements.md      # 🏪 متطلبات التجار
├── 📄 missing-translations-report.md # 🌐 تقرير الترجمات المفقودة
├── 📄 navigation-animations-guide.md # 🎨 دليل رسوم التنقل المتحركة
├── 📄 network-error-fixes.md        # 🌐 إصلاحات أخطاء الشبكة
├── 📄 performance-improvements-summary.md # ⚡ ملخص تحسينات الأداء
├── 📄 project-completion-report.md  # 📋 تقرير إكمال المشروع
├── 📄 testing-results-report.md     # 🧪 تقرير نتائج الاختبارات
├── 📄 translation-analysis-report.md # 🌐 تقرير تحليل الترجمات
├── 📄 future-recommendations.md     # 🚀 توصيات مستقبلية
├── 📁 guides/                       # 📖 أدلة التطوير والتنفيذ
│   ├── 📄 ai-platform-transformation-plan.md # 🤖 خطة تحويل المنصة للذكاء الاصطناعي
│   ├── 📄 CODE_TEMPLATES.md         # 📝 قوالب الكود
│   ├── 📄 ai-approval-system.md     # 🤖 دليل نظام الموافقات الذكي
│   ├── 📄 apex-cybersecurity-master-plan.md # 🛡️ خطة الأمان السيبراني
│   ├── 📄 complete-privacy-ai-implementation.md # 🔒 تطبيق الذكاء الاصطناعي الخاص
│   ├── 📄 local-ai-hosting-master-plan.md # 🏠 خطة استضافة الذكاء الاصطناعي المحلي
│   ├── 📄 merchant-requirements.md  # 🏪 متطلبات التجار
│   ├── 📄 navigation-animations-guide.md # 🎨 دليل رسوم التنقل المتحركة
│   ├── 📄 netlify-deployment.md     # 🚀 دليل النشر على Netlify
│   ├── 📄 privacy-secure-ai-solutions.md # 🔐 حلول الذكاء الاصطناعي الآمنة
│   ├── 📄 representative-requirements.md # 🚚 متطلبات المندوبين
│   ├── 📄 secure-ai-implementation-guide.md # 🛡️ دليل تطبيق الذكاء الاصطناعي الآمن
│   ├── 📄 smart-ai-quick-start.md   # ⚡ دليل البدء السريع للذكاء الاصطناعي
│   ├── 📄 subscription-system-guide.md # 💳 دليل نظام الاشتراكات
│   └── 📄 user-validation-system.md # ✅ دليل نظام التحقق من المستخدمين
├── 📁 reports/                      # 📊 التقارير والتحليلات
│   ├── 📄 analytics-testing-summary.md # 🔬 ملخص اختبارات نظام التحليلات
│   ├── 📄 development-weaknesses-and-improvements.md # 🚩 تقرير نقاط الضعف والتحسين
│   ├── 📄 advanced-features-development-report-2025-06-25.md # 📈 تقرير تطوير الميزات المتقدمة
│   ├── 📄 approval-system-comprehensive-analysis.md # 🔍 تحليل شامل لنظام الموافقات
│   ├── 📄 erp-pos-integration-implementation-report-2025-06-14.md # 🔗 تقرير تكامل ERP/POS
│   ├── 📄 location-permission-memory-enhancement.md # 📍 تحسين ذاكرة أذونات الموقع
│   ├── 📄 map-removal-and-stores-enhancement-report.md # 🗺️ تقرير إزالة الخرائط وتحسين المتاجر
│   ├── 📄 plans-comparison-report.md # 📋 تقرير مقارنة الخطط
│   ├── 📄 project-organization-summary.md # 📁 ملخص تنظيم المشروع
│   ├── 📄 security-test-report.json # 🛡️ تقرير اختبارات الأمان
│   ├── 📄 slider-removal-report-2025-06-25.md # 🎚️ تقرير إزالة المنزلقات
│   └── 📄 ui-performance-enhancements-report-2025-06-25.md # ⚡ تقرير تحسينات أداء الواجهة
├── 📁 security/                     # 🛡️ وثائق الأمان
│   ├── 📄 APEX_SECURITY_SYSTEMS.md  # 🔒 أنظمة الأمان المتقدمة
│   └── 📄 QUICK_SECURITY_GUIDE.md   # ⚡ دليل الأمان السريع
├── 📁 deployment/                   # 🚀 وثائق النشر
│   ├── 📄 executive-summary-netlify-analysis.md # 📊 ملخص تنفيذي لتحليل Netlify
│   ├── 📄 migration-summary.md      # 🔄 ملخص الهجرة
│   ├── 📄 netlify-action-plan.md    # 📋 خطة عمل Netlify
│   ├── 📄 netlify-build-analysis-2025-06-26.md # 🔍 تحليل بناء Netlify
│   ├── 📄 netlify-deployment-guide.md # 📖 دليل النشر على Netlify
│   └── 📄 netlify-optimization-solutions.md # ⚡ حلول تحسين Netlify
├── 📁 features/                     # ✨ وثائق الميزات
│   ├── 📄 subscription-system.md    # 💳 نظام الاشتراكات
│   └── 📁 ai-document-processing/   # 📄 معالجة المستندات بالذكاء الاصطناعي
└── 📁 archive/                      # 🗄️ الأرشيف
    ├── 📄 APEX_SECURITY_IMPLEMENTATION_SUMMARY.md # 🛡️ ملخص تطبيق الأمان المتقدم
    └── 📄 README.md                 # 📖 دليل الأرشيف
```

---

### 🌐 public/ - الملفات العامة

```
public/
├── 📄 favicon.ico                   # 🎯 أيقونة الموقع
├── 📄 manifest.json                 # 📱 ملف PWA للتطبيق
├── 📁 icons/                        # 🎨 أيقونات التطبيق
│   ├── 📄 icon-192x192.png          # أيقونة 192x192
│   ├── 📄 icon-512x512.png          # أيقونة 512x512
│   └── 📄 apple-touch-icon.png      # أيقونة Apple Touch
├── 📁 images/                       # 🖼️ الصور العامة
│   ├── 📄 logo.png                  # شعار التطبيق
│   ├── 📄 hero-bg.jpg               # خلفية القسم الرئيسي
│   └── 📄 placeholder.png           # صورة بديلة
└── 📁 locales/                      # 🌍 ملفات الترجمة العامة
    ├── 📄 ar.json                   # ترجمات عامة عربية
    └── 📄 en.json                   # ترجمات عامة إنجليزية
```

---

### 🛠️ scripts/ - سكريبتات المساعدة

```
scripts/
├── 📄 clear-all-cache.js            # 🧹 مسح جميع الكاش
├── 📄 extract-missing-translations.js # 🌐 استخراج الترجمات المفقودة
├── 📄 final-cleanup.js              # 🧹 التنظيف النهائي
├── 📄 final-duplicate-cleanup.js    # 🔄 تنظيف التكرارات النهائي
├── 📄 final-verification.js         # ✅ التحقق النهائي
├── 📄 fix-all-duplicates.js         # 🔧 إصلاح جميع التكرارات
├── 📄 run-comprehensive-tests.js    # 🧪 تشغيل الاختبارات الشاملة
├── 📄 simple-app-test.js            # 🧪 اختبار التطبيق البسيط
├── 📄 test-live-app.js              # 🧪 اختبار التطبيق المباشر
├── 📄 test-translations.js          # 🌐 اختبار الترجمات
└── 📄 validate-translations.js      # ✅ التحقق من صحة الترجمات
```

---

### ⚙️ ملفات التكوين الجذرية

```
mikhla/
├── 📄 package.json                  # 📦 تبعيات ومعلومات المشروع
├── 📄 bun.lock                      # 🔒 ملف قفل Bun
├── 📄 next.config.ts                # ⚙️ إعدادات Next.js
├── 📄 tailwind.config.ts            # 🎨 إعدادات Tailwind CSS
├── 📄 postcss.config.mjs            # 🎨 إعدادات PostCSS
├── 📄 tsconfig.json                 # 📘 إعدادات TypeScript
├── 📄 cypress.config.ts             # 🧪 إعدادات Cypress
├── 📄 firebase.json                 # 🔥 إعدادات Firebase
├── 📄 firestore.rules               # 🛡️ قواعد أمان Firestore
├── 📄 firestore.indexes.json        # 📊 فهارس Firestore
├── 📄 components.json               # 🧩 إعدادات مكونات shadcn/ui
├── 📄 .env.local                    # 🔐 متغيرات البيئة المحلية
├── 📄 .env.example                  # 📋 مثال على متغيرات البيئة
├── 📄 .gitignore                    # 🚫 ملفات مستبعدة من Git
├── 📄 .eslintrc.json                # 📏 إعدادات ESLint
├── 📄 .prettierrc                   # 🎨 إعدادات Prettier
├── 📄 next-env.d.ts                 # 📘 تعريفات Next.js TypeScript
└── 📄 README.md                     # 📖 الملف التعريفي الرئيسي
```

---

## 📊 إحصائيات الهيكل

### 📈 **أرقام المشروع**
- **إجمالي الملفات**: 320+ ملف
- **أسطر الكود**: 53,500+ سطر (3,500+ سطر جديد للذكاء الاصطناعي)
- **المكونات**: 155+ مكون React
- **الصفحات**: 52+ صفحة
- **الخدمات**: 29+ خدمة API (4 خدمات ذكاء اصطناعي جديدة)
- **APIs**: 35+ API (5 APIs ذكاء اصطناعي جديدة)
- **الـ Hooks**: 30+ hook مخصص
- **اختبارات Cypress**: 55+ اختبار (اختبارات النظام الذكي جديدة)

#### 🤖 **إحصائيات النظام الذكي الجديد**
- **خدمات الذكاء الاصطناعي**: 4 خدمات (1,300+ سطر)
- **APIs الذكاء الاصطناعي**: 5 APIs (800+ سطر)
- **مكونات الواجهة الذكية**: 3 مكونات (1,000+ سطر)
- **صفحات النظام الذكي**: 1 صفحة (300+ سطر)
- **اختبارات النظام الذكي**: 1 ملف اختبار شامل (300+ سطر)

### 🎯 **التوزيع حسب النوع**
- **مكونات UI**: 40%
- **صفحات التطبيق**: 25%
- **خدمات وAPI**: 15%
- **Hooks مخصصة**: 10%
- **اختبارات**: 5%
- **توثيق وإعدادات**: 5%

### 🌐 **التدويل**
- **اللغات المدعومة**: 2 (العربية، الإنجليزية)
- **مفاتيح الترجمة**: 1,400+ مفتاح
- **تغطية الترجمة**: 100%
- **دعم RTL/LTR**: كامل

---

## 🎯 أفضل الممارسات المتبعة

### 📁 **تنظيم الملفات**
- **تجميع حسب الوظيفة** - كل مجلد يحتوي على ملفات ذات صلة
- **تسمية واضحة** - أسماء ملفات ومجلدات وصفية
- **فصل الاهتمامات** - فصل المنطق عن العرض
- **قابلية إعادة الاستخدام** - مكونات قابلة للاستخدام في أماكن متعددة

### 🧩 **بنية المكونات**
- **مكونات ذرية** - مكونات صغيرة ومحددة الغرض
- **تركيب المكونات** - بناء مكونات معقدة من مكونات بسيطة
- **Props واضحة** - واجهات محددة وموثقة
- **TypeScript** - أنواع قوية لجميع المكونات

### 🔧 **إدارة الحالة**
- **React Context** - للحالة العامة
- **Custom Hooks** - لمنطق الأعمال المعقد
- **Local State** - للحالة المحلية البسيطة
- **Server State** - لبيانات الخادم

### 🧪 **الاختبارات**
- **اختبارات E2E** - للتدفقات الكاملة
- **اختبارات المكونات** - للمكونات المفردة
- **اختبارات التكامل** - للتفاعل بين الأجزاء
- **تغطية شاملة** - 85%+ من الكود

---

## 🚀 الخلاصة

هيكل مشروع **مِخْلاة** مصمم ليكون:

- ✅ **قابل للصيانة** - سهولة التعديل والتطوير
- ✅ **قابل للتوسع** - إمكانية إضافة ميزات جديدة
- ✅ **منظم** - بنية واضحة ومنطقية
- ✅ **قابل للاختبار** - سهولة كتابة وتشغيل الاختبارات
- ✅ **موثق** - توثيق شامل لجميع الأجزاء
- ✅ **متوافق مع المعايير** - اتباع أفضل الممارسات

هذا الهيكل يوفر أساساً قوياً لمنصة تجارة إلكترونية متكاملة ومتطورة تخدم جميع أنواع المستخدمين بكفاءة عالية.

---

<div align="center">

**📚 للمزيد من التوثيق، راجع:**

[📖 README الرئيسي](../README.md) • [📋 خطة التطوير](development-plan.md) • [📝 سجل التغييرات](CHANGELOG.md) • [👤 دليل المستخدم](USER_GUIDE.md)

---

**© 2024 مِخْلاة - جميع الحقوق محفوظة**

</div>

