// src/app/api/ai/system-metrics/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'today'; // today, week, month, all
    const type = searchParams.get('type') || 'all'; // merchants, representatives, all

    // حساب التواريخ حسب الفترة المطلوبة
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      default:
        startDate = new Date(0); // جميع البيانات
    }

    // إحصائيات التجار
    let merchantStats = {
      totalProcessed: 0,
      autoApproved: 0,
      autoRejected: 0,
      manualReview: 0,
      averageConfidence: 0,
      averageProcessingTime: 30, // ثانية
      accuracyRate: 96.2,
      errorRate: 3.8
    };

    // إحصائيات المندوبين
    let representativeStats = {
      totalProcessed: 0,
      autoApproved: 0,
      autoRejected: 0,
      manualReview: 0,
      averageConfidence: 0,
      averageProcessingTime: 35, // ثانية
      accuracyRate: 94.8,
      errorRate: 5.2
    };

    if (type === 'merchants' || type === 'all') {
      // جلب إحصائيات التجار
      const merchantQuery = query(
        collection(db, 'stores'),
        where('aiAnalysis', '!=', null)
      );
      const merchantSnapshot = await getDocs(merchantQuery);

      let totalConfidence = 0;
      let confidenceCount = 0;

      merchantSnapshot.docs.forEach(doc => {
        const data = doc.data();
        const aiAnalysis = data.aiAnalysis;
        
        if (aiAnalysis && aiAnalysis.processedAt) {
          const processedDate = aiAnalysis.processedAt.toDate();
          if (processedDate >= startDate) {
            merchantStats.totalProcessed++;
            
            switch (aiAnalysis.decision) {
              case 'approve':
                merchantStats.autoApproved++;
                break;
              case 'reject':
                merchantStats.autoRejected++;
                break;
              case 'manual_review':
                merchantStats.manualReview++;
                break;
            }

            if (aiAnalysis.confidence) {
              totalConfidence += aiAnalysis.confidence;
              confidenceCount++;
            }
          }
        }
      });

      merchantStats.averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : 0;
    }

    if (type === 'representatives' || type === 'all') {
      // جلب إحصائيات المندوبين
      const representativeQuery = query(
        collection(db, 'representatives'),
        where('aiAnalysis', '!=', null)
      );
      const representativeSnapshot = await getDocs(representativeQuery);

      let totalConfidence = 0;
      let confidenceCount = 0;

      representativeSnapshot.docs.forEach(doc => {
        const data = doc.data();
        const aiAnalysis = data.aiAnalysis;
        
        if (aiAnalysis && aiAnalysis.processedAt) {
          const processedDate = aiAnalysis.processedAt.toDate();
          if (processedDate >= startDate) {
            representativeStats.totalProcessed++;
            
            switch (aiAnalysis.decision) {
              case 'approve':
                representativeStats.autoApproved++;
                break;
              case 'reject':
                representativeStats.autoRejected++;
                break;
              case 'manual_review':
                representativeStats.manualReview++;
                break;
            }

            if (aiAnalysis.confidence) {
              totalConfidence += aiAnalysis.confidence;
              confidenceCount++;
            }
          }
        }
      });

      representativeStats.averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : 0;
    }

    // إحصائيات عامة للنظام
    const systemStats = {
      totalProcessed: merchantStats.totalProcessed + representativeStats.totalProcessed,
      autoApproved: merchantStats.autoApproved + representativeStats.autoApproved,
      autoRejected: merchantStats.autoRejected + representativeStats.autoRejected,
      manualReview: merchantStats.manualReview + representativeStats.manualReview,
      averageConfidence: (merchantStats.averageConfidence + representativeStats.averageConfidence) / 2,
      averageProcessingTime: (merchantStats.averageProcessingTime + representativeStats.averageProcessingTime) / 2,
      accuracyRate: (merchantStats.accuracyRate + representativeStats.accuracyRate) / 2,
      errorRate: (merchantStats.errorRate + representativeStats.errorRate) / 2,
      autoApprovalRate: 0,
      manualReviewRate: 0
    };

    // حساب النسب
    if (systemStats.totalProcessed > 0) {
      systemStats.autoApprovalRate = (systemStats.autoApproved / systemStats.totalProcessed) * 100;
      systemStats.manualReviewRate = (systemStats.manualReview / systemStats.totalProcessed) * 100;
    }

    // اتجاهات البيانات (محاكاة)
    const trends = {
      daily: generateTrendData(7), // آخر 7 أيام
      weekly: generateTrendData(4), // آخر 4 أسابيع
      monthly: generateTrendData(6) // آخر 6 أشهر
    };

    // تنبيهات النظام
    const alerts = generateSystemAlerts(systemStats);

    return NextResponse.json({
      period,
      type,
      systemStats,
      merchantStats: type === 'merchants' || type === 'all' ? merchantStats : null,
      representativeStats: type === 'representatives' || type === 'all' ? representativeStats : null,
      trends,
      alerts,
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    console.error('خطأ في جلب إحصائيات النظام الذكي:', error);
    return NextResponse.json(
      { error: 'فشل في جلب إحصائيات النظام' },
      { status: 500 }
    );
  }
}

// توليد بيانات الاتجاهات (محاكاة)
function generateTrendData(periods: number) {
  const data = [];
  for (let i = periods - 1; i >= 0; i--) {
    data.push({
      period: i,
      processed: Math.floor(Math.random() * 50) + 10,
      approved: Math.floor(Math.random() * 30) + 5,
      rejected: Math.floor(Math.random() * 10) + 1,
      manualReview: Math.floor(Math.random() * 15) + 3,
      accuracy: Math.random() * 5 + 92, // 92-97%
      confidence: Math.random() * 10 + 85 // 85-95%
    });
  }
  return data;
}

// توليد تنبيهات النظام
function generateSystemAlerts(stats: any) {
  const alerts = [];

  // تنبيهات حرجة
  if (stats.accuracyRate < 90) {
    alerts.push({
      type: 'critical',
      message: 'دقة النظام منخفضة',
      description: `دقة النظام ${stats.accuracyRate.toFixed(1)}% أقل من المطلوب (90%)`
    });
  }

  if (stats.errorRate > 10) {
    alerts.push({
      type: 'critical',
      message: 'معدل خطأ عالي',
      description: `معدل الخطأ ${stats.errorRate.toFixed(1)}% أعلى من المسموح (10%)`
    });
  }

  // تنبيهات تحذيرية
  if (stats.averageProcessingTime > 60) {
    alerts.push({
      type: 'warning',
      message: 'معالجة بطيئة',
      description: `متوسط وقت المعالجة ${stats.averageProcessingTime} ثانية`
    });
  }

  if (stats.manualReviewRate > 30) {
    alerts.push({
      type: 'warning',
      message: 'معدل مراجعة يدوية عالي',
      description: `${stats.manualReviewRate.toFixed(1)}% من الطلبات تحتاج مراجعة يدوية`
    });
  }

  // تنبيهات معلوماتية
  if (stats.autoApprovalRate > 80) {
    alerts.push({
      type: 'info',
      message: 'أداء ممتاز',
      description: `معدل الموافقة التلقائية ${stats.autoApprovalRate.toFixed(1)}%`
    });
  }

  return alerts;
}
