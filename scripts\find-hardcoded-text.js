#!/usr/bin/env node

/**
 * سكريبت للبحث عن النصوص العربية والإنجليزية المكتوبة مباشرة في المكونات
 * بدلاً من استخدام نظام الترجمات
 */

const fs = require('fs');
const path = require('path');

// المجلدات المراد فحصها
const DIRECTORIES_TO_SCAN = [
  'src/components',
  'src/app',
  'src/pages'
];

// الملفات المراد تجاهلها
const IGNORE_PATTERNS = [
  /\.cy\.(ts|tsx|js|jsx)$/, // ملفات Cypress
  /\.test\.(ts|tsx|js|jsx)$/, // ملفات الاختبار
  /\.spec\.(ts|tsx|js|jsx)$/, // ملفات المواصفات
  /node_modules/, // مجلد node_modules
  /\.next/, // مجلد Next.js build
  /dist/, // مجلد التوزيع
  /build/, // مجلد البناء
];

// أنماط النصوص المكتوبة مباشرة
const HARDCODED_PATTERNS = {
  arabic: {
    // نصوص عربية داخل JSX
    jsxText: />\s*[\u0600-\u06FF\s]+\s*</g,
    // نصوص عربية في strings
    stringLiterals: /['"`][\u0600-\u06FF\s]+['"`]/g,
    // نصوص عربية في template literals
    templateLiterals: /`[^`]*[\u0600-\u06FF][^`]*`/g
  },
  english: {
    // نصوص إنجليزية شائعة في UI
    commonUIText: /['"`](Login|Sign Up|Email|Password|Submit|Cancel|Save|Delete|Edit|Create|Update|Home|Dashboard|Profile|Settings|Logout)['"`]/gi,
    // رسائل خطأ إنجليزية
    errorMessages: /['"`](Error|Failed|Success|Warning|Invalid|Required|Please|Try again)[\w\s]*['"`]/gi
  }
};

/**
 * فحص ملف واحد للبحث عن النصوص المكتوبة مباشرة
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const results = [];

    // فحص النصوص العربية
    Object.entries(HARDCODED_PATTERNS.arabic).forEach(([type, pattern]) => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const lineNumber = content.substring(0, content.indexOf(match)).split('\n').length;
          results.push({
            file: filePath,
            line: lineNumber,
            type: `arabic_${type}`,
            text: match.trim(),
            language: 'ar'
          });
        });
      }
    });

    // فحص النصوص الإنجليزية
    Object.entries(HARDCODED_PATTERNS.english).forEach(([type, pattern]) => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const lineNumber = content.substring(0, content.indexOf(match)).split('\n').length;
          results.push({
            file: filePath,
            line: lineNumber,
            type: `english_${type}`,
            text: match.trim(),
            language: 'en'
          });
        });
      }
    });

    return results;
  } catch (error) {
    console.warn(`⚠️ تعذر قراءة الملف: ${filePath}`);
    return [];
  }
}

/**
 * فحص مجلد بشكل تكراري
 */
function scanDirectory(dirPath) {
  const results = [];
  
  try {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      // تجاهل الملفات والمجلدات المحددة
      if (IGNORE_PATTERNS.some(pattern => pattern.test(itemPath))) {
        return;
      }
      
      if (stat.isDirectory()) {
        results.push(...scanDirectory(itemPath));
      } else if (stat.isFile() && /\.(ts|tsx|js|jsx)$/.test(item)) {
        results.push(...scanFile(itemPath));
      }
    });
  } catch (error) {
    console.warn(`⚠️ تعذر قراءة المجلد: ${dirPath}`);
  }
  
  return results;
}

/**
 * تجميع النتائج وعرضها
 */
function displayResults(results) {
  if (results.length === 0) {
    console.log('✅ لم يتم العثور على نصوص مكتوبة مباشرة!');
    return;
  }

  console.log(`🔍 تم العثور على ${results.length} نص مكتوب مباشرة:\n`);

  // تجميع النتائج حسب الملف
  const groupedResults = results.reduce((acc, result) => {
    if (!acc[result.file]) {
      acc[result.file] = [];
    }
    acc[result.file].push(result);
    return acc;
  }, {});

  // عرض النتائج
  Object.entries(groupedResults).forEach(([file, fileResults]) => {
    console.log(`📁 ${file}:`);
    fileResults.forEach(result => {
      const flag = result.language === 'ar' ? '🇸🇦' : '🇺🇸';
      console.log(`   ${flag} السطر ${result.line}: ${result.text}`);
      console.log(`      النوع: ${result.type}`);
    });
    console.log('');
  });

  // إحصائيات
  const arabicCount = results.filter(r => r.language === 'ar').length;
  const englishCount = results.filter(r => r.language === 'en').length;
  
  console.log('📊 الإحصائيات:');
  console.log(`   🇸🇦 نصوص عربية: ${arabicCount}`);
  console.log(`   🇺🇸 نصوص إنجليزية: ${englishCount}`);
  console.log(`   📝 إجمالي: ${results.length}`);
}

/**
 * اقتراح حلول للنصوص المكتوبة مباشرة
 */
function suggestSolutions(results) {
  if (results.length === 0) return;

  console.log('\n💡 اقتراحات للإصلاح:');
  console.log('1. استبدال النصوص المكتوبة مباشرة بمفاتيح ترجمة:');
  console.log('   مثال: "تسجيل الدخول" → {t(\'login\')}');
  console.log('');
  console.log('2. إضافة المفاتيح المطلوبة لملف الترجمات:');
  console.log('   src/locales/translations.json');
  console.log('');
  console.log('3. استيراد واستخدام hook الترجمة:');
  console.log('   import { useLocale } from \'@/hooks/use-locale\';');
  console.log('   const { t } = useLocale();');
  console.log('');
  console.log('4. تشغيل الاختبارات للتأكد من عمل الترجمات:');
  console.log('   npm run test:translations');
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔍 البحث عن النصوص المكتوبة مباشرة في المشروع...\n');

  let allResults = [];

  // فحص كل مجلد
  DIRECTORIES_TO_SCAN.forEach(dir => {
    if (fs.existsSync(dir)) {
      console.log(`📂 فحص المجلد: ${dir}`);
      const results = scanDirectory(dir);
      allResults.push(...results);
    } else {
      console.log(`⚠️ المجلد غير موجود: ${dir}`);
    }
  });

  console.log('');
  displayResults(allResults);
  suggestSolutions(allResults);

  // إنهاء البرنامج بحالة خطأ إذا وجدت نصوص مكتوبة مباشرة
  if (allResults.length > 0) {
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  scanFile,
  scanDirectory,
  HARDCODED_PATTERNS
};
