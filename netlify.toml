# إعدادات Netlify لمشروع مِخْلاة مع الذكاء الاصطناعي المحلي
# 🤖 نظام ذكاء اصطناعي محلي حقيقي - خصوصية 100%
# 🐍 Python يحمل النماذج أثناء البناء، JavaScript/WASM في المتصفح
# 🔒 لا إرسال بيانات للخارج - معالجة محلية بالكامل

[build]
  # أمر البناء - يثبت Bun ثم يستخدم Python لتحميل النماذج المحلية ثم Next.js
  command = "bash scripts/install-bun-netlify.sh && python ai-models/scripts/netlify-build-setup.py && bun --bun next build"

  # مجلد النشر
  publish = ".next"

  # حد أقصى لحجم الملفات (500MB)
  functions_max_size = "52428800"

  # حد أقصى لوقت البناء (30 دقيقة)
  command_timeout = "1800"

[build.processing]
  # تحسين الصور
  skip_processing = false

[build.processing.css]
  # تحسين CSS
  bundle = true
  minify = true

[build.processing.js]
  # تحسين JavaScript
  bundle = true
  minify = true

[build.processing.html]
  # تحسين HTML
  pretty_urls = true

[build.processing.images]
  # تحسين الصور
  compress = true

# إعدادات الوظائف (Functions)
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# إعدادات إعادة التوجيه والقواعد
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/ai-models/*"
  to = "/ai-models/:splat"
  status = 200
  force = true

# إعدادات الرؤوس (Headers) للأمان
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self'; object-src 'none'; child-src 'none'; worker-src 'self' blob:; frame-ancestors 'none'; base-uri 'self'; form-action 'self';"

# رؤوس خاصة لملفات النماذج
[[headers]]
  for = "/ai-models/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, HEAD, OPTIONS"
    Cross-Origin-Embedder-Policy = "require-corp"
    Cross-Origin-Opener-Policy = "same-origin"

# رؤوس خاصة لملفات WASM
[[headers]]
  for = "*.wasm"
  [headers.values]
    Content-Type = "application/wasm"
    Cache-Control = "public, max-age=31536000, immutable"

# رؤوس خاصة لملفات ONNX
[[headers]]
  for = "*.onnx"
  [headers.values]
    Content-Type = "application/octet-stream"
    Cache-Control = "public, max-age=31536000, immutable"

# إعدادات متغيرات البيئة للبناء
[build.environment]
  # تحسين Node.js
  NODE_OPTIONS = "--max-old-space-size=4096"
  NPM_FLAGS = "--production=false"
  NETLIFY_CACHE_NEXTJS = "true"
  NEXT_TELEMETRY_DISABLED = "1"
  NEXT_PRIVATE_STANDALONE = "true"
  NETLIFY_BUILD_CACHE = "true"
  NETLIFY_CACHE_NODE_MODULES = "true"
  NETLIFY_SKIP_EDGE_HANDLERS = "false"
  NETLIFY_ENABLE_BROTLI = "true"

  # إعدادات Bun
  BUN_VERSION = "1.2.19"
  USE_BUN = "true"
  PACKAGE_MANAGER = "bun"

  # إعدادات Python للذكاء الاصطناعي المحلي
  PYTHON_VERSION = "3.9"
  PIP_CACHE_DIR = "/opt/buildhome/.cache/pip"
  PYTHONPATH = "/opt/buildhome/python"
  AI_MODELS_DOWNLOAD = "true"
  LOCAL_AI_SETUP = "true"

# إعدادات متغيرات البيئة للإنتاج
[context.production.environment]
  NODE_ENV = "production"
  USE_LOCAL_AI = "true"
  ENCRYPT_EXTERNAL = "false"
  AUDIT_LOGGING = "true"
  NETLIFY_DEPLOYMENT = "true"
  # مسارات النماذج
  AI_MODELS_PATH = "/ai-models"
  ONNX_WASM_PATH = "/ai-models/wasm"
  # إعدادات الأداء
  AI_MAX_CONCURRENT_ANALYSIS = "3"
  AI_ANALYSIS_TIMEOUT = "30000"
  AI_CACHE_ENABLED = "true"
  # إعدادات الأمان
  AI_SECURITY_LEVEL = "high"
  AI_AUDIT_ENABLED = "true"
  AI_LOCAL_ONLY = "true"

[context.deploy-preview.environment]
  NODE_ENV = "staging"
  USE_LOCAL_AI = "true"
  ENCRYPT_EXTERNAL = "false"
  AUDIT_LOGGING = "false"

[context.branch-deploy.environment]
  NODE_ENV = "development"
  USE_LOCAL_AI = "true"
  ENCRYPT_EXTERNAL = "false"
  AUDIT_LOGGING = "false"

# قواعد إعادة الكتابة للتطبيق
[[redirects]]
  from = "/"
  to = "/ar"
  status = 302
  conditions = {Language = ["ar"]}

[[redirects]]
  from = "/"
  to = "/en"
  status = 302
  conditions = {Language = ["en"]}

# إعدادات الأمان المتقدم
[context.production]
  command = "bash scripts/install-bun-netlify.sh && python ai-models/scripts/netlify-build-setup.py && bun --bun next build"

# قواعد خاصة للملفات الكبيرة
[[headers]]
  for = "/ai-models/models/*"
  [headers.values]
    # تحسين التحميل للملفات الكبيرة
    Accept-Ranges = "bytes"
    Cache-Control = "public, max-age=31536000, immutable"
    
    # ضغط الملفات
    Content-Encoding = "gzip"

# إعدادات الأخطاء المخصصة
[[redirects]]
  from = "/404"
  to = "/ar/404"
  status = 404

[[redirects]]
  from = "/500"
  to = "/ar/500"
  status = 500

# إعدادات الأمان للـ API
[[headers]]
  for = "/api/*"
  [headers.values]
    X-Robots-Tag = "noindex"
    Cache-Control = "no-cache, no-store, must-revalidate"

# إعدادات خاصة بالملفات الثابتة
[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# ملاحظات مهمة:
# 1. تأكد من رفع مجلد ai-models مع النماذج
# 2. النماذج ستكون متاحة على /ai-models/
# 3. الحد الأقصى لحجم الموقع على Netlify هو 500MB
# 4. استخدم ضغط الملفات لتوفير المساحة
# 5. تأكد من تفعيل HTTPS في الإنتاج
