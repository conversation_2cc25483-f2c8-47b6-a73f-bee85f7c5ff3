// src/components/analytics/EnhancedKPIDashboard.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Target,
  Activity,
  BarChart3,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  Zap,
  Award,
  Eye,
  Heart,
  Percent
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { advancedReportsService, type SalesReport } from '@/services/advancedReportsService';

interface KPIMetric {
  id: string;
  title: string;
  value: number;
  previousValue: number;
  target: number;
  unit: string;
  format: 'number' | 'currency' | 'percentage';
  growth: number;
  status: 'excellent' | 'good' | 'warning' | 'critical';
  description: string;
  icon: React.ReactNode;
  color: string;
}

interface EnhancedKPIDashboardProps {
  timeRange?: '7d' | '30d' | '90d' | '1y';
  onTimeRangeChange?: (range: '7d' | '30d' | '90d' | '1y') => void;
  onRefresh?: () => void;
  loading?: boolean;
  className?: string;
}

export default function EnhancedKPIDashboard({
  timeRange = '30d',
  onTimeRangeChange,
  onRefresh,
  loading = false,
  className
}: EnhancedKPIDashboardProps) {
  const { user } = useAuth();
  const [kpiMetrics, setKpiMetrics] = useState<KPIMetric[]>([]);
  const [salesReport, setSalesReport] = useState<SalesReport | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.uid) {
      loadKPIData();
    }
  }, [user?.uid, timeRange]);

  const loadKPIData = async () => {
    if (!user?.uid) return;

    try {
      setError(null);
      
      // إنتاج تقرير المبيعات
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeRange) {
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(startDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
      }

      const report = await advancedReportsService.generateSalesReport(user.uid, {
        startDate,
        endDate,
        type: timeRange === '7d' ? 'weekly' : 
              timeRange === '30d' ? 'monthly' :
              timeRange === '90d' ? 'quarterly' : 'yearly'
      });

      setSalesReport(report);
      
      // تحويل البيانات إلى مقاييس KPI
      const metrics = generateKPIMetrics(report);
      setKpiMetrics(metrics);

    } catch (err) {
      console.error('خطأ في تحميل بيانات KPI:', err);
      setError('فشل في تحميل بيانات الأداء');
    }
  };

  const generateKPIMetrics = (report: SalesReport): KPIMetric[] => {
    const metrics: KPIMetric[] = [
      {
        id: 'revenue',
        title: 'إجمالي الإيرادات',
        value: report.summary.totalRevenue,
        previousValue: report.summary.totalRevenue * 0.85, // محاكاة القيمة السابقة
        target: report.summary.totalRevenue * 1.2,
        unit: 'ريال',
        format: 'currency',
        growth: report.summary.growthRate,
        status: getKPIStatus(report.summary.growthRate),
        description: 'إجمالي الإيرادات المحققة في الفترة المحددة',
        icon: <DollarSign className="w-5 h-5" />,
        color: 'text-green-600'
      },
      {
        id: 'orders',
        title: 'إجمالي الطلبات',
        value: report.summary.totalOrders,
        previousValue: Math.floor(report.summary.totalOrders * 0.9),
        target: Math.floor(report.summary.totalOrders * 1.15),
        unit: 'طلب',
        format: 'number',
        growth: 10, // محاكاة
        status: getKPIStatus(10),
        description: 'عدد الطلبات المكتملة في الفترة المحددة',
        icon: <ShoppingCart className="w-5 h-5" />,
        color: 'text-blue-600'
      },
      {
        id: 'customers',
        title: 'إجمالي العملاء',
        value: report.summary.totalCustomers,
        previousValue: Math.floor(report.summary.totalCustomers * 0.95),
        target: Math.floor(report.summary.totalCustomers * 1.1),
        unit: 'عميل',
        format: 'number',
        growth: 5, // محاكاة
        status: getKPIStatus(5),
        description: 'عدد العملاء النشطين في الفترة المحددة',
        icon: <Users className="w-5 h-5" />,
        color: 'text-purple-600'
      },
      {
        id: 'aov',
        title: 'متوسط قيمة الطلب',
        value: report.summary.averageOrderValue,
        previousValue: report.summary.averageOrderValue * 0.92,
        target: report.summary.averageOrderValue * 1.08,
        unit: 'ريال',
        format: 'currency',
        growth: 8, // محاكاة
        status: getKPIStatus(8),
        description: 'متوسط قيمة الطلب الواحد',
        icon: <Target className="w-5 h-5" />,
        color: 'text-orange-600'
      },
      {
        id: 'conversion',
        title: 'معدل التحويل',
        value: report.summary.conversionRate,
        previousValue: report.summary.conversionRate * 0.88,
        target: report.summary.conversionRate * 1.2,
        unit: '%',
        format: 'percentage',
        growth: 12, // محاكاة
        status: getKPIStatus(12),
        description: 'نسبة الزوار الذين قاموا بعملية شراء',
        icon: <Percent className="w-5 h-5" />,
        color: 'text-indigo-600'
      },
      {
        id: 'retention',
        title: 'معدل الاحتفاظ',
        value: report.customerAnalytics.customerRetentionRate,
        previousValue: report.customerAnalytics.customerRetentionRate * 0.93,
        target: report.customerAnalytics.customerRetentionRate * 1.05,
        unit: '%',
        format: 'percentage',
        growth: 7, // محاكاة
        status: getKPIStatus(7),
        description: 'نسبة العملاء الذين عادوا للشراء مرة أخرى',
        icon: <Heart className="w-5 h-5" />,
        color: 'text-pink-600'
      },
      {
        id: 'satisfaction',
        title: 'رضا العملاء',
        value: 4.2, // محاكاة
        previousValue: 4.0,
        target: 4.5,
        unit: '/5',
        format: 'number',
        growth: 5, // محاكاة
        status: getKPIStatus(5),
        description: 'متوسط تقييم رضا العملاء',
        icon: <Star className="w-5 h-5" />,
        color: 'text-yellow-600'
      },
      {
        id: 'products',
        title: 'المنتجات النشطة',
        value: report.summary.totalProducts,
        previousValue: Math.floor(report.summary.totalProducts * 0.98),
        target: Math.floor(report.summary.totalProducts * 1.05),
        unit: 'منتج',
        format: 'number',
        growth: 2, // محاكاة
        status: getKPIStatus(2),
        description: 'عدد المنتجات المتاحة للبيع',
        icon: <Package className="w-5 h-5" />,
        color: 'text-teal-600'
      }
    ];

    return metrics;
  };

  const getKPIStatus = (growth: number): 'excellent' | 'good' | 'warning' | 'critical' => {
    if (growth >= 15) return 'excellent';
    if (growth >= 5) return 'good';
    if (growth >= -5) return 'warning';
    return 'critical';
  };

  const formatValue = (value: number, format: string, unit: string): string => {
    switch (format) {
      case 'currency':
        return `${value.toLocaleString()} ${unit}`;
      case 'percentage':
        return `${value.toFixed(1)}${unit}`;
      case 'number':
        return `${value.toLocaleString()} ${unit}`;
      default:
        return `${value} ${unit}`;
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'good':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
        return <Award className="w-4 h-4" />;
      case 'good':
        return <CheckCircle className="w-4 h-4" />;
      case 'warning':
        return <Clock className="w-4 h-4" />;
      case 'critical':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* العنوان والأدوات */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <BarChart3 className="w-6 h-6" />
          مؤشرات الأداء الرئيسية
        </h2>
        
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        )}
      </div>

      {/* شبكة مؤشرات الأداء */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiMetrics.map((metric) => (
          <Card key={metric.id} className="relative overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className={`p-2 rounded-lg ${metric.color} bg-opacity-10`}>
                  {metric.icon}
                </div>
                <Badge 
                  variant="outline" 
                  className={`${getStatusColor(metric.status)} text-xs`}
                >
                  {getStatusIcon(metric.status)}
                  <span className="mr-1">
                    {metric.status === 'excellent' ? 'ممتاز' :
                     metric.status === 'good' ? 'جيد' :
                     metric.status === 'warning' ? 'تحذير' : 'حرج'}
                  </span>
                </Badge>
              </div>
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {metric.title}
              </CardTitle>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-3">
                {/* القيمة الحالية */}
                <div className="text-2xl font-bold">
                  {formatValue(metric.value, metric.format, metric.unit)}
                </div>

                {/* النمو */}
                <div className="flex items-center gap-2">
                  {metric.growth > 0 ? (
                    <TrendingUp className="w-4 h-4 text-green-500" />
                  ) : metric.growth < 0 ? (
                    <TrendingDown className="w-4 h-4 text-red-500" />
                  ) : (
                    <Activity className="w-4 h-4 text-gray-500" />
                  )}
                  <span className={`text-sm font-medium ${
                    metric.growth > 0 ? 'text-green-600' :
                    metric.growth < 0 ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {metric.growth > 0 ? '+' : ''}{metric.growth.toFixed(1)}%
                  </span>
                  <span className="text-xs text-muted-foreground">
                    من الفترة السابقة
                  </span>
                </div>

                {/* التقدم نحو الهدف */}
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">التقدم نحو الهدف</span>
                    <span className="font-medium">
                      {((metric.value / metric.target) * 100).toFixed(0)}%
                    </span>
                  </div>
                  <Progress 
                    value={(metric.value / metric.target) * 100} 
                    className="h-2"
                  />
                </div>

                {/* الوصف */}
                <p className="text-xs text-muted-foreground">
                  {metric.description}
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* ملخص الأداء العام */}
      {salesReport && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5" />
              ملخص الأداء العام
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-1">
                  {kpiMetrics.filter(m => m.status === 'excellent').length}
                </div>
                <div className="text-sm text-muted-foreground">مؤشرات ممتازة</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-1">
                  {kpiMetrics.filter(m => m.status === 'good').length}
                </div>
                <div className="text-sm text-muted-foreground">مؤشرات جيدة</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600 mb-1">
                  {kpiMetrics.filter(m => m.status === 'warning' || m.status === 'critical').length}
                </div>
                <div className="text-sm text-muted-foreground">مؤشرات تحتاج تحسين</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
