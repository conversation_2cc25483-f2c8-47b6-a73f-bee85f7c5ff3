"use client";

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Gift, Star, ArrowRight, Home, User } from "lucide-react";
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { merchantPlans, customerPlans, representativePlans } from '@/constants/plans';
import type { SubscriptionPlan } from '@/types';

export default function SubscriptionSuccessPage() {
  const { t, locale } = useLocale();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [paymentId, setPaymentId] = useState<string | null>(null);
  const [subscriptionType, setSubscriptionType] = useState<'free' | 'paid'>('paid');

  useEffect(() => {
    const planId = searchParams.get('plan');
    const payment = searchParams.get('payment');
    const type = searchParams.get('type');
    
    if (planId) {
      const allPlans = [...merchantPlans, ...customerPlans, ...representativePlans];
      const plan = allPlans.find(p => p.id === planId);
      setSelectedPlan(plan || null);
    }
    
    if (payment) {
      setPaymentId(payment);
    }
    
    if (type === 'free') {
      setSubscriptionType('free');
    }
  }, [searchParams]);

  const formatPrice = (plan: SubscriptionPlan) => {
    if (plan.priceDisplayKey === 'free') {
      return t('free');
    }
    if (plan.priceValue !== undefined && plan.currencyKey && plan.periodKey) {
      return t(plan.priceDisplayKey, {
        price: plan.priceValue,
        currency: t(plan.currencyKey),
        period: t(plan.periodKey)
      });
    }
    return '';
  };

  const getDashboardUrl = () => {
    if (!selectedPlan) return `/${locale}`;
    
    if (selectedPlan.id.startsWith('merchant')) {
      return `/${locale}/merchant/dashboard`;
    } else if (selectedPlan.id.startsWith('representative')) {
      return `/${locale}/representative/dashboard`;
    } else {
      return `/${locale}/dashboard`;
    }
  };

  const getWelcomeMessage = () => {
    if (!selectedPlan) return t('subscriptionSuccessful');
    
    if (subscriptionType === 'free') {
      return t('freePlanActivated');
    } else {
      return t('paidPlanActivated');
    }
  };

  if (!selectedPlan) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="text-center py-12">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h1 className="text-2xl font-bold mb-2">{t('subscriptionSuccessful')}</h1>
              <p className="text-muted-foreground mb-6">
                {t('subscriptionProcessedSuccessfully')}
              </p>
              <Button onClick={() => router.push(`/${locale}`)}>
                <Home className="w-4 h-4 mr-2" />
                {t('backToHome')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-4xl mx-auto">
        {/* رسالة النجاح الرئيسية */}
        <Card className="mb-8">
          <CardContent className="text-center py-12">
            <div className="mb-6">
              <CheckCircle className="w-20 h-20 text-green-500 mx-auto mb-4" />
              <h1 className="text-3xl font-bold mb-2">{getWelcomeMessage()}</h1>
              <p className="text-muted-foreground text-lg">
                {t('welcomeToNewPlan', { planName: t(selectedPlan.nameKey) })}
              </p>
            </div>

            {/* معلومات الخطة */}
            <div className="bg-muted p-6 rounded-lg mb-6 max-w-md mx-auto">
              <div className="flex items-center justify-between mb-2">
                <span className="font-semibold">{t(selectedPlan.nameKey)}</span>
                {selectedPlan.isPopular && (
                  <Badge variant="default">{t('popular')}</Badge>
                )}
              </div>
              <div className="text-2xl font-bold text-primary mb-2">
                {formatPrice(selectedPlan)}
              </div>
              {paymentId && (
                <div className="text-sm text-muted-foreground">
                  {t('paymentId')}: {paymentId}
                </div>
              )}
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={() => router.push(getDashboardUrl())}
                size="lg"
                className="bg-primary hover:bg-primary/90"
              >
                <ArrowRight className="w-4 h-4 mr-2" />
                {t('goToDashboard')}
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => router.push(`/${locale}/profile`)}
                size="lg"
              >
                <User className="w-4 h-4 mr-2" />
                {t('viewProfile')}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* ميزات الخطة */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Gift className="w-5 h-5 mr-2" />
                {t('whatYouGet')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {selectedPlan.features.slice(0, 6).map((feature) => (
                  <li key={feature.nameKey} className="flex items-center space-x-2 rtl:space-x-reverse">
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm">{t(feature.nameKey)}</span>
                  </li>
                ))}
                {selectedPlan.features.length > 6 && (
                  <li className="text-sm text-muted-foreground">
                    {t('andMoreFeatures', { count: selectedPlan.features.length - 6 })}
                  </li>
                )}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Star className="w-5 h-5 mr-2" />
                {t('nextSteps')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                <li className="flex items-start space-x-2 rtl:space-x-reverse">
                  <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                    1
                  </div>
                  <span className="text-sm">{t('completeProfile')}</span>
                </li>
                <li className="flex items-start space-x-2 rtl:space-x-reverse">
                  <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                    2
                  </div>
                  <span className="text-sm">{t('exploreDashboard')}</span>
                </li>
                <li className="flex items-start space-x-2 rtl:space-x-reverse">
                  <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                    3
                  </div>
                  <span className="text-sm">{t('startUsingFeatures')}</span>
                </li>
                {subscriptionType === 'paid' && (
                  <li className="flex items-start space-x-2 rtl:space-x-reverse">
                    <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                      4
                    </div>
                    <span className="text-sm">{t('receiveInvoiceEmail')}</span>
                  </li>
                )}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* رسالة شكر */}
        <Card className="mt-8">
          <CardContent className="text-center py-8">
            <h3 className="text-xl font-semibold mb-2">{t('thankYou')}</h3>
            <p className="text-muted-foreground">
              {t('subscriptionThankYouMessage')}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
