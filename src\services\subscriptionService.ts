// src/services/subscriptionService.ts
import { 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs,
  serverTimestamp,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { SubscriptionPlan } from '@/types';

// أنواع البيانات للاشتراكات
export interface UserSubscription {
  id: string;
  userId: string;
  planId: string;
  planType: 'customer' | 'merchant' | 'representative';
  status: 'active' | 'cancelled' | 'expired' | 'pending';
  startDate: Timestamp;
  endDate?: Timestamp;
  paymentId?: string;
  transactionId?: string;
  amount?: number;
  currency?: string;
  autoRenew: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface SubscriptionHistory {
  id: string;
  userId: string;
  subscriptionId: string;
  action: 'created' | 'activated' | 'cancelled' | 'renewed' | 'upgraded' | 'downgraded';
  fromPlanId?: string;
  toPlanId: string;
  amount?: number;
  paymentId?: string;
  reason?: string;
  createdAt: Timestamp;
}

class SubscriptionService {
  private subscriptionsCollection = collection(db, 'subscriptions');
  private historyCollection = collection(db, 'subscription_history');

  // ===== إنشاء وإدارة الاشتراكات =====

  /**
   * إنشاء اشتراك جديد
   */
  async createSubscription(
    userId: string,
    planId: string,
    planType: 'customer' | 'merchant' | 'representative',
    paymentId?: string,
    transactionId?: string,
    amount?: number,
    currency: string = 'SAR'
  ): Promise<string> {
    try {
      const subscriptionId = `${userId}_${planId}_${Date.now()}`;
      const subscriptionRef = doc(this.subscriptionsCollection, subscriptionId);

      const subscriptionData: Omit<UserSubscription, 'id'> = {
        userId,
        planId,
        planType,
        status: paymentId ? 'pending' : 'active', // إذا كان هناك دفع، الحالة معلقة حتى تأكيد الدفع
        startDate: serverTimestamp() as Timestamp,
        paymentId,
        transactionId,
        amount,
        currency: amount ? currency : undefined,
        autoRenew: amount ? true : false, // الخطط المدفوعة تجدد تلقائياً
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
      };

      await setDoc(subscriptionRef, subscriptionData);

      // إضافة سجل في التاريخ
      await this.addSubscriptionHistory(
        userId,
        subscriptionId,
        'created',
        undefined,
        planId,
        amount,
        paymentId
      );

      return subscriptionId;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new Error('فشل في إنشاء الاشتراك');
    }
  }

  /**
   * تفعيل اشتراك (للخطط المجانية أو بعد تأكيد الدفع)
   */
  async activateSubscription(subscriptionId: string, transactionId?: string): Promise<boolean> {
    try {
      const subscriptionRef = doc(this.subscriptionsCollection, subscriptionId);
      const subscriptionDoc = await getDoc(subscriptionRef);

      if (!subscriptionDoc.exists()) {
        throw new Error('الاشتراك غير موجود');
      }

      const updateData: Partial<UserSubscription> = {
        status: 'active',
        updatedAt: serverTimestamp() as Timestamp,
      };

      if (transactionId) {
        updateData.transactionId = transactionId;
      }

      await updateDoc(subscriptionRef, updateData);

      const subscription = subscriptionDoc.data() as UserSubscription;
      
      // إضافة سجل في التاريخ
      await this.addSubscriptionHistory(
        subscription.userId,
        subscriptionId,
        'activated',
        undefined,
        subscription.planId,
        subscription.amount,
        subscription.paymentId
      );

      return true;
    } catch (error) {
      console.error('Error activating subscription:', error);
      throw new Error('فشل في تفعيل الاشتراك');
    }
  }

  /**
   * إلغاء اشتراك
   */
  async cancelSubscription(subscriptionId: string, reason?: string): Promise<boolean> {
    try {
      const subscriptionRef = doc(this.subscriptionsCollection, subscriptionId);
      const subscriptionDoc = await getDoc(subscriptionRef);

      if (!subscriptionDoc.exists()) {
        throw new Error('الاشتراك غير موجود');
      }

      await updateDoc(subscriptionRef, {
        status: 'cancelled',
        autoRenew: false,
        updatedAt: serverTimestamp() as Timestamp,
      });

      const subscription = subscriptionDoc.data() as UserSubscription;
      
      // إضافة سجل في التاريخ
      await this.addSubscriptionHistory(
        subscription.userId,
        subscriptionId,
        'cancelled',
        subscription.planId,
        subscription.planId,
        undefined,
        undefined,
        reason
      );

      return true;
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      throw new Error('فشل في إلغاء الاشتراك');
    }
  }

  // ===== استعلام الاشتراكات =====

  /**
   * جلب الاشتراك النشط للمستخدم
   */
  async getUserActiveSubscription(userId: string): Promise<UserSubscription | null> {
    try {
      const q = query(
        this.subscriptionsCollection,
        where('userId', '==', userId),
        where('status', '==', 'active'),
        orderBy('createdAt', 'desc'),
        limit(1)
      );

      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return { id: doc.id, ...doc.data() } as UserSubscription;
    } catch (error) {
      console.error('Error getting user active subscription:', error);
      return null;
    }
  }

  /**
   * جلب جميع اشتراكات المستخدم
   */
  async getUserSubscriptions(userId: string): Promise<UserSubscription[]> {
    try {
      const q = query(
        this.subscriptionsCollection,
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as UserSubscription[];
    } catch (error) {
      console.error('Error getting user subscriptions:', error);
      return [];
    }
  }

  /**
   * جلب تاريخ الاشتراكات للمستخدم
   */
  async getUserSubscriptionHistory(userId: string): Promise<SubscriptionHistory[]> {
    try {
      const q = query(
        this.historyCollection,
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as SubscriptionHistory[];
    } catch (error) {
      console.error('Error getting user subscription history:', error);
      return [];
    }
  }

  // ===== مراقبة الاشتراكات =====

  /**
   * مراقبة الاشتراك النشط للمستخدم
   */
  subscribeToUserActiveSubscription(
    userId: string,
    callback: (subscription: UserSubscription | null) => void
  ): () => void {
    const q = query(
      this.subscriptionsCollection,
      where('userId', '==', userId),
      where('status', '==', 'active'),
      orderBy('createdAt', 'desc'),
      limit(1)
    );

    return onSnapshot(q, (snapshot) => {
      if (snapshot.empty) {
        callback(null);
      } else {
        const doc = snapshot.docs[0];
        callback({ id: doc.id, ...doc.data() } as UserSubscription);
      }
    }, (error) => {
      console.error('Error in subscription subscription:', error);
      callback(null);
    });
  }

  // ===== وظائف مساعدة =====

  /**
   * إضافة سجل في تاريخ الاشتراكات
   */
  private async addSubscriptionHistory(
    userId: string,
    subscriptionId: string,
    action: SubscriptionHistory['action'],
    fromPlanId?: string,
    toPlanId?: string,
    amount?: number,
    paymentId?: string,
    reason?: string
  ): Promise<void> {
    try {
      const historyRef = doc(this.historyCollection);
      
      const historyData: Omit<SubscriptionHistory, 'id'> = {
        userId,
        subscriptionId,
        action,
        fromPlanId,
        toPlanId: toPlanId || '',
        amount,
        paymentId,
        reason,
        createdAt: serverTimestamp() as Timestamp,
      };

      await setDoc(historyRef, historyData);
    } catch (error) {
      console.error('Error adding subscription history:', error);
      // لا نرمي خطأ هنا لأن هذا ليس حرجاً
    }
  }

  /**
   * التحقق من صحة الاشتراك
   */
  async validateSubscription(userId: string, requiredPlanType?: string): Promise<boolean> {
    try {
      const activeSubscription = await this.getUserActiveSubscription(userId);
      
      if (!activeSubscription) {
        return false;
      }

      if (requiredPlanType && activeSubscription.planType !== requiredPlanType) {
        return false;
      }

      // التحقق من انتهاء الصلاحية إذا كان هناك تاريخ انتهاء
      if (activeSubscription.endDate) {
        const now = new Date();
        const endDate = activeSubscription.endDate.toDate();
        
        if (now > endDate) {
          // انتهت صلاحية الاشتراك، تحديث الحالة
          await updateDoc(doc(this.subscriptionsCollection, activeSubscription.id), {
            status: 'expired',
            updatedAt: serverTimestamp()
          });
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error validating subscription:', error);
      return false;
    }
  }
}

export const subscriptionService = new SubscriptionService();
