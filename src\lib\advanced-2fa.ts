// src/lib/advanced-2fa.ts
// 🔐 نظام المصادقة الثنائية المتقدم - Apex Level

import { auth, db } from './firebase';
import { doc, setDoc, getDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { ApexEncryptionEngine } from './encryption';
import CryptoJS from 'crypto-js';

// ===== INTERFACES =====

export interface TwoFactorConfig {
  enabled: boolean;
  methods: {
    totp: boolean;
    sms: boolean;
    email: boolean;
    backup: boolean;
  };
  backupCodes: string[];
  lastUsed?: Date;
  setupDate?: Date;
}

export interface TOTPSetup {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

export interface VerificationResult {
  success: boolean;
  method: string;
  remainingAttempts?: number;
  lockoutUntil?: Date;
}

// ===== APEX 2FA ENGINE =====

export class Apex2FAEngine {
  private static readonly TOTP_WINDOW = 30; // 30 seconds
  private static readonly BACKUP_CODES_COUNT = 10;
  private static readonly MAX_ATTEMPTS = 3;
  private static readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

  /**
   * إعداد المصادقة الثنائية للمستخدم
   */
  static async setup2FA(userId: string): Promise<TOTPSetup> {
    try {
      // توليد مفتاح سري للـ TOTP
      const secret = this.generateTOTPSecret();
      
      // توليد رموز النسخ الاحتياطية
      const backupCodes = this.generateBackupCodes();
      
      // توليد QR Code
      const qrCode = await this.generateQRCode(userId, secret);
      
      // حفظ الإعدادات (مشفرة)
      const config: TwoFactorConfig = {
        enabled: false, // سيتم تفعيلها بعد التحقق
        methods: {
          totp: true,
          sms: false,
          email: false,
          backup: true
        },
        backupCodes: backupCodes,
        setupDate: new Date()
      };
      
      await this.save2FAConfig(userId, config, secret);
      
      return {
        secret,
        qrCode,
        backupCodes
      };
    } catch (error) {
      console.error('🔴 خطأ في إعداد 2FA:', error);
      throw new Error('فشل في إعداد المصادقة الثنائية');
    }
  }

  /**
   * تفعيل المصادقة الثنائية بعد التحقق
   */
  static async enable2FA(userId: string, totpCode: string): Promise<boolean> {
    try {
      // التحقق من الرمز
      const isValid = await this.verifyTOTP(userId, totpCode);
      
      if (!isValid) {
        throw new Error('رمز التحقق غير صحيح');
      }
      
      // تفعيل 2FA
      const configRef = doc(db, 'user_2fa_config', userId);
      await updateDoc(configRef, {
        'config.enabled': true,
        'config.lastUsed': new Date()
      });
      
      // تسجيل الحدث الأمني
      await this.logSecurityEvent(userId, '2FA_ENABLED', {
        timestamp: new Date(),
        method: 'totp'
      });
      
      return true;
    } catch (error) {
      console.error('🔴 خطأ في تفعيل 2FA:', error);
      throw error;
    }
  }

  /**
   * التحقق من رمز TOTP
   */
  static async verifyTOTP(userId: string, code: string): Promise<boolean> {
    try {
      // التحقق من محاولات الدخول
      const canAttempt = await this.checkAttemptLimit(userId);
      if (!canAttempt.allowed) {
        throw new Error(`تم تجاوز عدد المحاولات. المحاولة التالية بعد: ${canAttempt.lockoutUntil}`);
      }
      
      // جلب المفتاح السري
      const secret = await this.getTOTPSecret(userId);
      if (!secret) {
        throw new Error('لم يتم إعداد المصادقة الثنائية');
      }
      
      // التحقق من الرمز
      const isValid = this.validateTOTPCode(secret, code);
      
      if (isValid) {
        // إعادة تعيين محاولات الدخول
        await this.resetAttemptLimit(userId);
        
        // تسجيل نجاح التحقق
        await this.logSecurityEvent(userId, '2FA_SUCCESS', {
          timestamp: new Date(),
          method: 'totp'
        });
      } else {
        // تسجيل فشل المحاولة
        await this.recordFailedAttempt(userId);
        
        await this.logSecurityEvent(userId, '2FA_FAILED', {
          timestamp: new Date(),
          method: 'totp',
          remainingAttempts: canAttempt.remainingAttempts - 1
        });
      }
      
      return isValid;
    } catch (error) {
      console.error('🔴 خطأ في التحقق من TOTP:', error);
      throw error;
    }
  }

  /**
   * التحقق من رمز النسخ الاحتياطية
   */
  static async verifyBackupCode(userId: string, code: string): Promise<boolean> {
    try {
      const config = await this.get2FAConfig(userId);
      if (!config || !config.backupCodes) {
        return false;
      }
      
      // البحث عن الرمز
      const codeIndex = config.backupCodes.indexOf(code);
      if (codeIndex === -1) {
        await this.logSecurityEvent(userId, '2FA_BACKUP_FAILED', {
          timestamp: new Date(),
          code: code.substring(0, 2) + '***'
        });
        return false;
      }
      
      // إزالة الرمز المستخدم
      config.backupCodes.splice(codeIndex, 1);
      await this.save2FAConfig(userId, config);
      
      // تسجيل الاستخدام
      await this.logSecurityEvent(userId, '2FA_BACKUP_SUCCESS', {
        timestamp: new Date(),
        remainingCodes: config.backupCodes.length
      });
      
      // تنبيه إذا نفدت الرموز
      if (config.backupCodes.length === 0) {
        await this.logSecurityEvent(userId, '2FA_BACKUP_DEPLETED', {
          timestamp: new Date(),
          warning: 'جميع رموز النسخ الاحتياطية مستخدمة'
        });
      }
      
      return true;
    } catch (error) {
      console.error('🔴 خطأ في التحقق من رمز النسخ الاحتياطي:', error);
      return false;
    }
  }

  /**
   * إلغاء تفعيل المصادقة الثنائية
   */
  static async disable2FA(userId: string, password: string): Promise<boolean> {
    try {
      // التحقق من كلمة المرور (يجب إضافة التحقق الفعلي)
      // const isPasswordValid = await this.verifyPassword(userId, password);
      // if (!isPasswordValid) {
      //   throw new Error('كلمة المرور غير صحيحة');
      // }
      
      // حذف إعدادات 2FA
      const configRef = doc(db, 'user_2fa_config', userId);
      await deleteDoc(configRef);
      
      // تسجيل الحدث
      await this.logSecurityEvent(userId, '2FA_DISABLED', {
        timestamp: new Date(),
        method: 'password_verification'
      });
      
      return true;
    } catch (error) {
      console.error('🔴 خطأ في إلغاء 2FA:', error);
      throw error;
    }
  }

  // ===== PRIVATE METHODS =====

  private static generateTOTPSecret(): string {
    return CryptoJS.lib.WordArray.random(20).toString(CryptoJS.enc.Base32);
  }

  private static generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < this.BACKUP_CODES_COUNT; i++) {
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  private static async generateQRCode(userId: string, secret: string): Promise<string> {
    const issuer = 'Mikhla';
    const label = `${issuer}:${userId}`;
    const otpauth = `otpauth://totp/${label}?secret=${secret}&issuer=${issuer}`;
    
    // في التطبيق الحقيقي، استخدم مكتبة QR Code
    return `data:image/svg+xml;base64,${btoa(`<svg>QR Code for: ${otpauth}</svg>`)}`;
  }

  private static validateTOTPCode(secret: string, code: string): boolean {
    const currentTime = Math.floor(Date.now() / 1000);
    const timeWindow = Math.floor(currentTime / this.TOTP_WINDOW);
    
    // فحص النافذة الحالية والسابقة والتالية
    for (let i = -1; i <= 1; i++) {
      const testTime = timeWindow + i;
      const expectedCode = this.generateTOTPCode(secret, testTime);
      if (expectedCode === code) {
        return true;
      }
    }
    
    return false;
  }

  private static generateTOTPCode(secret: string, timeWindow: number): string {
    // تطبيق مبسط لـ TOTP - في الإنتاج استخدم مكتبة متخصصة
    const time = timeWindow.toString(16).padStart(16, '0');
    const hmac = CryptoJS.HmacSHA1(time, secret);
    const offset = parseInt(hmac.toString().slice(-1), 16);
    const code = parseInt(hmac.toString().slice(offset * 2, offset * 2 + 8), 16) % 1000000;
    return code.toString().padStart(6, '0');
  }

  private static async save2FAConfig(
    userId: string, 
    config: TwoFactorConfig, 
    secret?: string
  ): Promise<void> {
    const configRef = doc(db, 'user_2fa_config', userId);
    
    const encryptedConfig = await ApexEncryptionEngine.encryptWithPFS(config);
    const data: any = { config: encryptedConfig };
    
    if (secret) {
      const encryptedSecret = await ApexEncryptionEngine.encryptWithPFS(secret);
      data.secret = encryptedSecret;
    }
    
    await setDoc(configRef, data);
  }

  private static async get2FAConfig(userId: string): Promise<TwoFactorConfig | null> {
    try {
      const configRef = doc(db, 'user_2fa_config', userId);
      const configSnap = await getDoc(configRef);
      
      if (!configSnap.exists()) {
        return null;
      }
      
      const data = configSnap.data();
      return await ApexEncryptionEngine.decryptWithVerification(data.config);
    } catch (error) {
      console.error('🔴 خطأ في جلب إعدادات 2FA:', error);
      return null;
    }
  }

  private static async getTOTPSecret(userId: string): Promise<string | null> {
    try {
      const configRef = doc(db, 'user_2fa_config', userId);
      const configSnap = await getDoc(configRef);
      
      if (!configSnap.exists()) {
        return null;
      }
      
      const data = configSnap.data();
      return await ApexEncryptionEngine.decryptWithVerification(data.secret);
    } catch (error) {
      console.error('🔴 خطأ في جلب مفتاح TOTP:', error);
      return null;
    }
  }

  private static async checkAttemptLimit(userId: string): Promise<{
    allowed: boolean;
    remainingAttempts: number;
    lockoutUntil?: Date;
  }> {
    // تطبيق مبسط - في الإنتاج استخدم Redis أو قاعدة بيانات سريعة
    return {
      allowed: true,
      remainingAttempts: this.MAX_ATTEMPTS
    };
  }

  private static async resetAttemptLimit(userId: string): Promise<void> {
    // إعادة تعيين محاولات الدخول
  }

  private static async recordFailedAttempt(userId: string): Promise<void> {
    // تسجيل محاولة فاشلة
  }

  private static async logSecurityEvent(
    userId: string, 
    event: string, 
    data: any
  ): Promise<void> {
    const logEntry = {
      userId,
      event,
      data,
      timestamp: new Date(),
      ip: 'unknown', // سيتم تطويرها
      userAgent: navigator?.userAgent || 'unknown'
    };
    
    console.log('🔍 Security Event:', logEntry);
    
    // في الإنتاج، احفظ في قاعدة بيانات المراجعة
  }
}

export default Apex2FAEngine;
