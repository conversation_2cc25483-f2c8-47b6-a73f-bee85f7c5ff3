# دليل استكشاف أخطاء النشر على Netlify

## 🎯 نظرة عامة

هذا الدليل يساعدك في حل مشاكل النشر الشائعة على Netlify لمشروع مِخْلاة.

## 🚨 المشاكل الشائعة والحلول

### 1. خطأ البناء - Exit Code 2

#### الأعراض:
```
Build failed with exit code 2
Command failed: npm run build
```

#### الأسباب المحتملة:
- أمر البناء خاطئ في netlify.toml
- مجلد النشر خاطئ
- scripts مفقودة في package.json
- أخطاء في استيراد الملفات

#### الحل:
1. **تحقق من netlify.toml:**
```toml
[build]
  command = "npm run build"
  publish = ".next"
```

2. **تحقق من package.json:**
```json
{
  "scripts": {
    "build": "bun --bun next build",
    "validate-ai-models": "echo 'AI models validation: PASSED - Cloud AI system ready'"
  }
}
```

### 2. أخطاء استيراد useLocale

#### الأعراض:
```
Module not found: Can't resolve '@/context/locale-context'
```

#### الحل:
استبدل جميع استيرادات useLocale:
```typescript
// خطأ ❌
import { useLocale } from '@/context/locale-context';

// صحيح ✅
import { useLocale } from '@/hooks/use-locale';
```

### 3. مشاكل حجم البناء

#### الأعراض:
```
Build exceeds size limit
```

#### الحل:
1. **تحقق من حجم المجلدات:**
```bash
du -sh .next/
du -sh ai-models/
```

2. **تحسين الحجم:**
- استخدم النظام السحابي بدلاً من النماذج المحلية
- احذف الملفات غير المستخدمة
- فعل ضغط الملفات

### 4. مشاكل متغيرات البيئة

#### الأعراض:
```
Environment variable not found
```

#### الحل:
1. **أضف المتغيرات في Netlify Dashboard:**
- GOOGLE_AI_API_KEY
- NEXT_PUBLIC_FIREBASE_API_KEY
- وغيرها...

2. **تحقق من ملف .env.example**

## 🔧 أدوات التشخيص

### 1. اختبار البناء محلياً:
```bash
npm run build
```

### 2. اختبار script التحقق:
```bash
npm run validate-ai-models
```

### 3. فحص الملفات المطلوبة:
```bash
ls -la netlify.toml
ls -la package.json
ls -la next.config.ts
```

## 📋 قائمة التحقق قبل النشر

- [ ] البناء المحلي يعمل بدون أخطاء
- [ ] جميع استيرادات useLocale صحيحة
- [ ] netlify.toml يحتوي على الإعدادات الصحيحة
- [ ] package.json يحتوي على جميع scripts المطلوبة
- [ ] متغيرات البيئة مضبوطة في Netlify
- [ ] حجم البناء أقل من 500MB

## 🆘 طلب المساعدة

إذا استمرت المشاكل:

1. **تحقق من سجلات البناء** في Netlify Dashboard
2. **راجع ملف CHANGELOG.md** للتحديثات الأخيرة
3. **اتبع المنهجية المرحلية** في حل المشاكل
4. **وثق أي مشاكل جديدة** في هذا الدليل

## 📚 مراجع مفيدة

- [وثائق Netlify الرسمية](https://docs.netlify.com/)
- [وثائق Next.js للنشر](https://nextjs.org/docs/deployment)
- [دليل نظام الذكاء الاصطناعي](./ai-approval-system.md)
