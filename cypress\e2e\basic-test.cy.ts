describe('اختبار أساسي', () => {
  it('يجب أن تعمل الصفحة الرئيسية', () => {
    cy.visit('http://localhost:9002')
    cy.wait(3000)
    cy.get('body').should('be.visible')
  })

  it('يجب أن تعمل صفحة إنشاء الحساب', () => {
    cy.visit('http://localhost:9002/ar/signup')
    cy.wait(5000)
    cy.get('body').should('be.visible')
    cy.url().should('include', '/signup')
  })

  it('يجب أن يعمل تبديل اللغة', () => {
    cy.visit('http://localhost:9002/ar')
    cy.wait(3000)
    
    // البحث عن أي زر يحتوي على EN
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("EN")').length > 0) {
        cy.get('button:contains("EN")').first().click()
        cy.wait(2000)
        cy.url().should('include', '/en/')
      } else {
        cy.log('لم يتم العثور على زر تبديل اللغة')
      }
    })
  })
})
