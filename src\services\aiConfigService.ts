// src/services/aiConfigService.ts
import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// إعدادات النظام الذكي
export interface AISystemConfig {
  // إعدادات عامة
  enabled: boolean;
  version: string;
  lastUpdated: Date;
  
  // إعدادات التجار
  merchants: {
    enabled: boolean;
    autoApprovalEnabled: boolean;
    nameMatchThreshold: number;
    documentValidityThreshold: number;
    maxRiskScore: number;
    minConfidenceScore: number;
    requiredDocuments: string[];
  };
  
  // إعدادات المندوبين
  representatives: {
    enabled: boolean;
    autoApprovalEnabled: boolean;
    nameMatchThreshold: number;
    documentValidityThreshold: number;
    maxRiskScore: number;
    minConfidenceScore: number;
    requiredDocuments: string[];
    maxDocumentAge: number;
    minLicenseValidityDays: number;
  };
  
  // إعدادات مزودي الخدمة
  providers: {
    primary: 'google' | 'aws' | 'azure' | 'mock';
    fallback: 'google' | 'aws' | 'azure' | 'mock';
    google: {
      enabled: boolean;
      projectId: string;
      keyFile: string;
    };
    aws: {
      enabled: boolean;
      accessKeyId: string;
      secretAccessKey: string;
      region: string;
    };
    azure: {
      enabled: boolean;
      subscriptionKey: string;
      endpoint: string;
    };
  };
  
  // إعدادات الأداء
  performance: {
    maxFileSize: number;
    supportedFormats: string[];
    maxProcessingTime: number;
    retryAttempts: number;
    cacheEnabled: boolean;
    cacheDuration: number;
  };
  
  // إعدادات التنبيهات
  alerts: {
    enabled: boolean;
    lowAccuracyThreshold: number;
    highErrorRateThreshold: number;
    slowProcessingThreshold: number;
    highManualReviewThreshold: number;
    emailNotifications: boolean;
    slackNotifications: boolean;
  };
}

// الإعدادات الافتراضية
const DEFAULT_CONFIG: AISystemConfig = {
  enabled: true,
  version: '1.0.0',
  lastUpdated: new Date(),
  
  merchants: {
    enabled: true,
    autoApprovalEnabled: true,
    nameMatchThreshold: 85,
    documentValidityThreshold: 90,
    maxRiskScore: 25,
    minConfidenceScore: 80,
    requiredDocuments: ['commercial_registration', 'freelance_document']
  },
  
  representatives: {
    enabled: true,
    autoApprovalEnabled: true,
    nameMatchThreshold: 85,
    documentValidityThreshold: 90,
    maxRiskScore: 30,
    minConfidenceScore: 80,
    requiredDocuments: ['driving_license', 'vehicle_inspection'],
    maxDocumentAge: 365,
    minLicenseValidityDays: 90
  },
  
  providers: {
    primary: process.env.AI_PRIMARY_PROVIDER as 'google' | 'aws' | 'azure' || 'google',
    fallback: process.env.AI_FALLBACK_PROVIDER as 'google' | 'aws' | 'azure' || 'aws',
    google: {
      enabled: !!process.env.GOOGLE_AI_API_KEY,
      projectId: process.env.FIREBASE_PROJECT_ID || '',
      keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS || ''
    },
    aws: {
      enabled: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY),
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      region: process.env.AWS_REGION || 'us-east-1'
    },
    azure: {
      enabled: !!(process.env.AZURE_COGNITIVE_SERVICES_KEY && process.env.AZURE_COGNITIVE_SERVICES_ENDPOINT),
      subscriptionKey: process.env.AZURE_COGNITIVE_SERVICES_KEY || '',
      endpoint: process.env.AZURE_COGNITIVE_SERVICES_ENDPOINT || ''
    }
  },
  
  performance: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    supportedFormats: ['pdf', 'jpg', 'png', 'jpeg'],
    maxProcessingTime: 60, // ثانية
    retryAttempts: 3,
    cacheEnabled: true,
    cacheDuration: 3600 // ساعة واحدة
  },
  
  alerts: {
    enabled: true,
    lowAccuracyThreshold: 90,
    highErrorRateThreshold: 10,
    slowProcessingThreshold: 60,
    highManualReviewThreshold: 30,
    emailNotifications: true,
    slackNotifications: false
  }
};

export class AIConfigService {
  private static configCache: AISystemConfig | null = null;
  private static lastCacheUpdate: Date | null = null;
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 دقائق

  /**
   * جلب إعدادات النظام الذكي
   */
  static async getConfig(): Promise<AISystemConfig> {
    // التحقق من الكاش
    if (this.configCache && this.lastCacheUpdate) {
      const now = new Date();
      const timeDiff = now.getTime() - this.lastCacheUpdate.getTime();
      if (timeDiff < this.CACHE_DURATION) {
        return this.configCache;
      }
    }

    try {
      const configDocRef = doc(db, 'system_config', 'ai_settings');
      const configDocSnap = await getDoc(configDocRef);

      if (configDocSnap.exists()) {
        const config = configDocSnap.data() as AISystemConfig;
        
        // تحديث الكاش
        this.configCache = config;
        this.lastCacheUpdate = new Date();
        
        return config;
      } else {
        // إنشاء إعدادات افتراضية
        await this.createDefaultConfig();
        return DEFAULT_CONFIG;
      }
    } catch (error) {
      console.error('خطأ في جلب إعدادات النظام الذكي:', error);
      return DEFAULT_CONFIG;
    }
  }

  /**
   * تحديث إعدادات النظام الذكي
   */
  static async updateConfig(updates: Partial<AISystemConfig>): Promise<boolean> {
    try {
      const configDocRef = doc(db, 'system_config', 'ai_settings');
      
      const updateData = {
        ...updates,
        lastUpdated: serverTimestamp(),
        version: '1.0.0'
      };

      await updateDoc(configDocRef, updateData);
      
      // مسح الكاش لإجبار إعادة التحميل
      this.configCache = null;
      this.lastCacheUpdate = null;
      
      return true;
    } catch (error) {
      console.error('خطأ في تحديث إعدادات النظام الذكي:', error);
      return false;
    }
  }

  /**
   * إنشاء إعدادات افتراضية
   */
  private static async createDefaultConfig(): Promise<void> {
    try {
      const configDocRef = doc(db, 'system_config', 'ai_settings');
      await setDoc(configDocRef, {
        ...DEFAULT_CONFIG,
        lastUpdated: serverTimestamp()
      });
      
      this.configCache = DEFAULT_CONFIG;
      this.lastCacheUpdate = new Date();
    } catch (error) {
      console.error('خطأ في إنشاء الإعدادات الافتراضية:', error);
    }
  }

  /**
   * إعادة تعيين الإعدادات للقيم الافتراضية
   */
  static async resetToDefaults(): Promise<boolean> {
    try {
      const configDocRef = doc(db, 'system_config', 'ai_settings');
      await setDoc(configDocRef, {
        ...DEFAULT_CONFIG,
        lastUpdated: serverTimestamp()
      });
      
      // مسح الكاش
      this.configCache = null;
      this.lastCacheUpdate = null;
      
      return true;
    } catch (error) {
      console.error('خطأ في إعادة تعيين الإعدادات:', error);
      return false;
    }
  }

  /**
   * التحقق من صحة الإعدادات
   */
  static validateConfig(config: Partial<AISystemConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // التحقق من إعدادات التجار
    if (config.merchants) {
      if (config.merchants.nameMatchThreshold < 0 || config.merchants.nameMatchThreshold > 100) {
        errors.push('حد تطابق الاسم للتجار يجب أن يكون بين 0 و 100');
      }
      if (config.merchants.documentValidityThreshold < 0 || config.merchants.documentValidityThreshold > 100) {
        errors.push('حد صحة المستند للتجار يجب أن يكون بين 0 و 100');
      }
      if (config.merchants.maxRiskScore < 0 || config.merchants.maxRiskScore > 100) {
        errors.push('حد المخاطر الأقصى للتجار يجب أن يكون بين 0 و 100');
      }
    }

    // التحقق من إعدادات المندوبين
    if (config.representatives) {
      if (config.representatives.nameMatchThreshold < 0 || config.representatives.nameMatchThreshold > 100) {
        errors.push('حد تطابق الاسم للمندوبين يجب أن يكون بين 0 و 100');
      }
      if (config.representatives.documentValidityThreshold < 0 || config.representatives.documentValidityThreshold > 100) {
        errors.push('حد صحة المستند للمندوبين يجب أن يكون بين 0 و 100');
      }
      if (config.representatives.maxRiskScore < 0 || config.representatives.maxRiskScore > 100) {
        errors.push('حد المخاطر الأقصى للمندوبين يجب أن يكون بين 0 و 100');
      }
      if (config.representatives.minLicenseValidityDays < 1) {
        errors.push('أقل مدة صلاحية للرخصة يجب أن تكون يوم واحد على الأقل');
      }
    }

    // التحقق من إعدادات الأداء
    if (config.performance) {
      if (config.performance.maxFileSize < 1024) {
        errors.push('حجم الملف الأقصى يجب أن يكون 1KB على الأقل');
      }
      if (config.performance.maxProcessingTime < 10) {
        errors.push('وقت المعالجة الأقصى يجب أن يكون 10 ثوان على الأقل');
      }
      if (config.performance.retryAttempts < 1 || config.performance.retryAttempts > 10) {
        errors.push('عدد محاولات الإعادة يجب أن يكون بين 1 و 10');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * تصدير الإعدادات
   */
  static async exportConfig(): Promise<string> {
    try {
      const config = await this.getConfig();
      return JSON.stringify(config, null, 2);
    } catch (error) {
      console.error('خطأ في تصدير الإعدادات:', error);
      throw new Error('فشل في تصدير الإعدادات');
    }
  }

  /**
   * استيراد الإعدادات
   */
  static async importConfig(configJson: string): Promise<boolean> {
    try {
      const config = JSON.parse(configJson) as AISystemConfig;
      
      // التحقق من صحة الإعدادات
      const validation = this.validateConfig(config);
      if (!validation.valid) {
        console.error('إعدادات غير صحيحة:', validation.errors);
        return false;
      }

      // تحديث الإعدادات
      return await this.updateConfig(config);
    } catch (error) {
      console.error('خطأ في استيراد الإعدادات:', error);
      return false;
    }
  }

  /**
   * مسح الكاش
   */
  static clearCache(): void {
    this.configCache = null;
    this.lastCacheUpdate = null;
  }

  /**
   * الحصول على إعدادات التجار فقط
   */
  static async getMerchantConfig() {
    const config = await this.getConfig();
    return config.merchants;
  }

  /**
   * الحصول على إعدادات المندوبين فقط
   */
  static async getRepresentativeConfig() {
    const config = await this.getConfig();
    return config.representatives;
  }

  /**
   * الحصول على إعدادات مزودي الخدمة
   */
  static async getProviderConfig() {
    const config = await this.getConfig();
    return config.providers;
  }

  /**
   * الحصول على إعدادات الأداء
   */
  static async getPerformanceConfig() {
    const config = await this.getConfig();
    return config.performance;
  }

  /**
   * الحصول على إعدادات التنبيهات
   */
  static async getAlertConfig() {
    const config = await this.getConfig();
    return config.alerts;
  }

  /**
   * الحصول على حالة النظام
   */
  static async getSystemStatus(): Promise<{
    isConfigured: boolean;
    availableProviders: string[];
    primaryProvider: string;
    fallbackProvider: string;
    lastHealthCheck: Date;
    configVersion: string;
  }> {
    try {
      const config = await this.getConfig();
      const availableProviders: string[] = [];

      if (config.providers.google.enabled) availableProviders.push('google');
      if (config.providers.aws.enabled) availableProviders.push('aws');
      if (config.providers.azure.enabled) availableProviders.push('azure');

      return {
        isConfigured: availableProviders.length > 0,
        availableProviders,
        primaryProvider: config.providers.primary,
        fallbackProvider: config.providers.fallback,
        lastHealthCheck: new Date(),
        configVersion: '1.0.0'
      };
    } catch (error) {
      console.error('خطأ في الحصول على حالة النظام:', error);
      return {
        isConfigured: false,
        availableProviders: [],
        primaryProvider: 'google',
        fallbackProvider: 'aws',
        lastHealthCheck: new Date(),
        configVersion: '1.0.0'
      };
    }
  }

  /**
   * اختبار الاتصال بمزودي الخدمة
   */
  static async testProviderConnections(): Promise<{
    google: { connected: boolean; error?: string };
    aws: { connected: boolean; error?: string };
    azure: { connected: boolean; error?: string };
  }> {
    const config = await this.getConfig();
    const results = {
      google: { connected: false, error: undefined as string | undefined },
      aws: { connected: false, error: undefined as string | undefined },
      azure: { connected: false, error: undefined as string | undefined }
    };

    // اختبار Google AI
    if (config.providers.google.enabled) {
      try {
        // يمكن إضافة اختبار حقيقي هنا
        results.google.connected = true;
      } catch (error) {
        results.google.error = error instanceof Error ? error.message : 'خطأ غير معروف';
      }
    }

    // اختبار AWS
    if (config.providers.aws.enabled) {
      try {
        // يمكن إضافة اختبار حقيقي هنا
        results.aws.connected = true;
      } catch (error) {
        results.aws.error = error instanceof Error ? error.message : 'خطأ غير معروف';
      }
    }

    // اختبار Azure
    if (config.providers.azure.enabled) {
      try {
        // يمكن إضافة اختبار حقيقي هنا
        results.azure.connected = true;
      } catch (error) {
        results.azure.error = error instanceof Error ? error.message : 'خطأ غير معروف';
      }
    }

    return results;
  }
}
