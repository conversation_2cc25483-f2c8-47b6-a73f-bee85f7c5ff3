// src/lib/audit-system.ts
// 📊 نظام المراجعة والتدقيق المتقدم - Apex Level

import { db } from './firebase';
import { collection, addDoc, query, where, orderBy, limit, getDocs, doc, setDoc } from 'firebase/firestore';
import { ApexEncryptionEngine } from './encryption';
import CryptoJS from 'crypto-js';

// ===== INTERFACES =====

export interface AuditEvent {
  id?: string;
  eventId: string;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  eventType: AuditEventType;
  category: AuditCategory;
  severity: AuditSeverity;
  sourceIP: string;
  userAgent: string;
  resource: string;
  action: string;
  details: any;
  result: 'success' | 'failure' | 'warning';
  integrityHash: string;
  digitalSignature: string;
  complianceFlags: string[];
}

export interface AuditQuery {
  userId?: string;
  eventType?: AuditEventType;
  category?: AuditCategory;
  severity?: AuditSeverity;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
}

export interface AuditReport {
  summary: {
    totalEvents: number;
    successRate: number;
    failureRate: number;
    warningRate: number;
    topEventTypes: { type: string; count: number }[];
    topUsers: { userId: string; count: number }[];
  };
  timeline: { date: string; count: number }[];
  securityEvents: AuditEvent[];
  complianceStatus: {
    gdpr: boolean;
    saudiDataProtection: boolean;
    iso27001: boolean;
  };
}

export enum AuditEventType {
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_REGISTRATION = 'user_registration',
  PASSWORD_CHANGE = 'password_change',
  PROFILE_UPDATE = 'profile_update',
  DATA_ACCESS = 'data_access',
  DATA_MODIFICATION = 'data_modification',
  DATA_DELETION = 'data_deletion',
  PERMISSION_CHANGE = 'permission_change',
  SECURITY_VIOLATION = 'security_violation',
  SYSTEM_ERROR = 'system_error',
  API_CALL = 'api_call',
  FILE_UPLOAD = 'file_upload',
  FILE_DOWNLOAD = 'file_download',
  PAYMENT_TRANSACTION = 'payment_transaction',
  ORDER_CREATION = 'order_creation',
  ORDER_MODIFICATION = 'order_modification',
  ADMIN_ACTION = 'admin_action'
}

export enum AuditCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_MANAGEMENT = 'data_management',
  SECURITY = 'security',
  COMPLIANCE = 'compliance',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system'
}

export enum AuditSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// ===== APEX AUDIT SYSTEM =====

export class ApexAuditSystem {
  private static readonly RETENTION_PERIOD = 365 * 24 * 60 * 60 * 1000; // 1 year
  private static readonly BATCH_SIZE = 100;
  private static readonly ENCRYPTION_KEY = process.env.AUDIT_ENCRYPTION_KEY || 'audit-key-change-in-production';

  /**
   * تسجيل حدث مراجعة
   */
  static async logAuditEvent(
    eventType: AuditEventType,
    category: AuditCategory,
    details: any,
    context: {
      userId?: string;
      sessionId?: string;
      sourceIP?: string;
      userAgent?: string;
      resource?: string;
      action?: string;
      result?: 'success' | 'failure' | 'warning';
    } = {}
  ): Promise<string> {
    try {
      const eventId = this.generateEventId();
      const timestamp = new Date();
      
      // إنشاء حدث المراجعة
      const auditEvent: AuditEvent = {
        eventId,
        timestamp,
        userId: context.userId,
        sessionId: context.sessionId || this.generateSessionId(),
        eventType,
        category,
        severity: this.determineSeverity(eventType, context.result),
        sourceIP: this.hashIP(context.sourceIP || 'unknown'),
        userAgent: this.hashUserAgent(context.userAgent || 'unknown'),
        resource: context.resource || 'unknown',
        action: context.action || 'unknown',
        details: await this.sanitizeDetails(details),
        result: context.result || 'success',
        integrityHash: '',
        digitalSignature: '',
        complianceFlags: this.generateComplianceFlags(eventType, category)
      };

      // حساب hash السلامة
      auditEvent.integrityHash = await this.calculateIntegrityHash(auditEvent);
      
      // توقيع رقمي
      auditEvent.digitalSignature = await this.generateDigitalSignature(auditEvent);

      // تشفير البيانات الحساسة
      const encryptedEvent = await this.encryptAuditEvent(auditEvent);

      // حفظ في قاعدة البيانات
      const auditRef = collection(db, 'audit_logs');
      const docRef = await addDoc(auditRef, encryptedEvent);

      // تحديث الإحصائيات
      await this.updateAuditStatistics(eventType, category);

      // فحص التنبيهات
      await this.checkAlerts(auditEvent);

      return docRef.id;
    } catch (error) {
      console.error('🔴 خطأ في تسجيل حدث المراجعة:', error);
      throw new Error('فشل في تسجيل حدث المراجعة');
    }
  }

  /**
   * البحث في سجلات المراجعة
   */
  static async queryAuditLogs(queryParams: AuditQuery): Promise<AuditEvent[]> {
    try {
      let auditQuery = query(collection(db, 'audit_logs'));

      // تطبيق الفلاتر
      if (queryParams.userId) {
        auditQuery = query(auditQuery, where('userId', '==', queryParams.userId));
      }
      if (queryParams.eventType) {
        auditQuery = query(auditQuery, where('eventType', '==', queryParams.eventType));
      }
      if (queryParams.category) {
        auditQuery = query(auditQuery, where('category', '==', queryParams.category));
      }
      if (queryParams.startDate) {
        auditQuery = query(auditQuery, where('timestamp', '>=', queryParams.startDate));
      }
      if (queryParams.endDate) {
        auditQuery = query(auditQuery, where('timestamp', '<=', queryParams.endDate));
      }

      // ترتيب وحد
      auditQuery = query(
        auditQuery,
        orderBy('timestamp', 'desc'),
        limit(queryParams.limit || 100)
      );

      const querySnapshot = await getDocs(auditQuery);
      const events: AuditEvent[] = [];

      for (const doc of querySnapshot.docs) {
        try {
          const encryptedData = doc.data();
          const decryptedEvent = await this.decryptAuditEvent(encryptedData);
          
          // التحقق من سلامة البيانات
          const isValid = await this.verifyEventIntegrity(decryptedEvent);
          if (isValid) {
            events.push({ ...decryptedEvent, id: doc.id });
          } else {
            console.warn('⚠️ تم اكتشاف تلاعب في سجل المراجعة:', doc.id);
          }
        } catch (error) {
          console.error('🔴 خطأ في فك تشفير سجل المراجعة:', error);
        }
      }

      return events;
    } catch (error) {
      console.error('🔴 خطأ في البحث في سجلات المراجعة:', error);
      throw new Error('فشل في البحث في سجلات المراجعة');
    }
  }

  /**
   * إنشاء تقرير مراجعة شامل
   */
  static async generateAuditReport(
    startDate: Date,
    endDate: Date,
    userId?: string
  ): Promise<AuditReport> {
    try {
      const events = await this.queryAuditLogs({
        startDate,
        endDate,
        userId,
        limit: 10000
      });

      const summary = this.calculateSummary(events);
      const timeline = this.generateTimeline(events, startDate, endDate);
      const securityEvents = events.filter(e => e.category === AuditCategory.SECURITY);
      const complianceStatus = await this.checkComplianceStatus(events);

      return {
        summary,
        timeline,
        securityEvents,
        complianceStatus
      };
    } catch (error) {
      console.error('🔴 خطأ في إنشاء تقرير المراجعة:', error);
      throw new Error('فشل في إنشاء تقرير المراجعة');
    }
  }

  /**
   * تنظيف السجلات القديمة
   */
  static async cleanupOldLogs(): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - this.RETENTION_PERIOD);
      const oldLogsQuery = query(
        collection(db, 'audit_logs'),
        where('timestamp', '<', cutoffDate),
        limit(this.BATCH_SIZE)
      );

      const querySnapshot = await getDocs(oldLogsQuery);
      let deletedCount = 0;

      // في الإنتاج، استخدم batch delete
      for (const doc of querySnapshot.docs) {
        // أرشفة قبل الحذف إذا لزم الأمر
        await this.archiveAuditLog(doc.data());
        // await deleteDoc(doc.ref);
        deletedCount++;
      }

      return deletedCount;
    } catch (error) {
      console.error('🔴 خطأ في تنظيف السجلات القديمة:', error);
      return 0;
    }
  }

  // ===== PRIVATE METHODS =====

  private static generateEventId(): string {
    return 'audit_' + Date.now() + '_' + Math.random().toString(36).substring(2);
  }

  private static generateSessionId(): string {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2);
  }

  private static determineSeverity(
    eventType: AuditEventType,
    result?: string
  ): AuditSeverity {
    if (result === 'failure') {
      if (eventType === AuditEventType.USER_LOGIN) return AuditSeverity.WARNING;
      if (eventType === AuditEventType.SECURITY_VIOLATION) return AuditSeverity.CRITICAL;
      return AuditSeverity.ERROR;
    }
    
    if (eventType === AuditEventType.SECURITY_VIOLATION) return AuditSeverity.CRITICAL;
    if (eventType === AuditEventType.ADMIN_ACTION) return AuditSeverity.WARNING;
    
    return AuditSeverity.INFO;
  }

  private static hashIP(ip: string): string {
    // تشفير IP للخصوصية مع الحفاظ على إمكانية التتبع
    return CryptoJS.SHA256(ip + 'salt').toString().substring(0, 16);
  }

  private static hashUserAgent(userAgent: string): string {
    // تشفير User Agent للخصوصية
    return CryptoJS.SHA256(userAgent + 'salt').toString().substring(0, 16);
  }

  private static async sanitizeDetails(details: any): Promise<any> {
    // إزالة البيانات الحساسة من التفاصيل
    const sanitized = { ...details };
    
    // قائمة الحقول الحساسة
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'ssn', 'creditCard'];
    
    const sanitizeObject = (obj: any): any => {
      if (typeof obj !== 'object' || obj === null) return obj;
      
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
          result[key] = '[REDACTED]';
        } else if (typeof value === 'object') {
          result[key] = sanitizeObject(value);
        } else {
          result[key] = value;
        }
      }
      return result;
    };

    return sanitizeObject(sanitized);
  }

  private static generateComplianceFlags(
    eventType: AuditEventType,
    category: AuditCategory
  ): string[] {
    const flags: string[] = [];
    
    // GDPR compliance
    if (category === AuditCategory.DATA_MANAGEMENT) {
      flags.push('GDPR_RELEVANT');
    }
    
    // Saudi Data Protection Law
    if (eventType === AuditEventType.DATA_ACCESS || eventType === AuditEventType.DATA_MODIFICATION) {
      flags.push('SAUDI_DPL_RELEVANT');
    }
    
    // ISO 27001
    if (category === AuditCategory.SECURITY) {
      flags.push('ISO27001_RELEVANT');
    }
    
    return flags;
  }

  private static async calculateIntegrityHash(event: AuditEvent): Promise<string> {
    const eventString = JSON.stringify({
      eventId: event.eventId,
      timestamp: event.timestamp,
      userId: event.userId,
      eventType: event.eventType,
      details: event.details
    });
    
    return CryptoJS.SHA256(eventString).toString();
  }

  private static async generateDigitalSignature(event: AuditEvent): Promise<string> {
    // في الإنتاج، استخدم مفتاح خاص للتوقيع
    const signature = CryptoJS.HmacSHA256(event.integrityHash, this.ENCRYPTION_KEY);
    return signature.toString();
  }

  private static async encryptAuditEvent(event: AuditEvent): Promise<any> {
    try {
      // محاولة التشفير مع معالجة أفضل للأخطاء
      const encryptedDetails = await ApexEncryptionEngine.encryptWithPFS(event.details);

      return {
        ...event,
        details: encryptedDetails,
        encrypted: true
      };
    } catch (error) {
      console.error('🔴 خطأ في تشفير حدث المراجعة:', error);

      // في حالة فشل التشفير، احفظ البيانات بدون تشفير مع تحذير
      console.warn('⚠️ سيتم حفظ حدث المراجعة بدون تشفير');
      return {
        ...event,
        details: {
          ...event.details,
          encryptionError: 'فشل في التشفير - تم الحفظ بدون تشفير'
        },
        encrypted: false
      };
    }
  }

  private static async decryptAuditEvent(encryptedData: any): Promise<AuditEvent> {
    try {
      if (encryptedData.encrypted && encryptedData.details) {
        const decryptedDetails = await ApexEncryptionEngine.decryptWithVerification(encryptedData.details);
        return {
          ...encryptedData,
          details: decryptedDetails
        };
      }
      return encryptedData;
    } catch (error) {
      console.error('🔴 خطأ في فك تشفير حدث المراجعة:', error);
      throw error;
    }
  }

  private static async verifyEventIntegrity(event: AuditEvent): Promise<boolean> {
    try {
      const calculatedHash = await this.calculateIntegrityHash(event);
      return calculatedHash === event.integrityHash;
    } catch (error) {
      console.error('🔴 خطأ في التحقق من سلامة الحدث:', error);
      return false;
    }
  }

  private static async updateAuditStatistics(
    eventType: AuditEventType,
    category: AuditCategory
  ): Promise<void> {
    try {
      const statsRef = doc(db, 'audit_statistics', 'daily_stats');
      const today = new Date().toISOString().split('T')[0];
      
      // في الإنتاج، استخدم increment operations
      console.log(`📊 تحديث إحصائيات المراجعة: ${eventType} - ${category}`);
    } catch (error) {
      console.error('🔴 خطأ في تحديث إحصائيات المراجعة:', error);
    }
  }

  private static async checkAlerts(event: AuditEvent): Promise<void> {
    try {
      // فحص التنبيهات الأمنية
      if (event.severity === AuditSeverity.CRITICAL) {
        await this.sendSecurityAlert(event);
      }
      
      // فحص أنماط مشبوهة
      if (event.eventType === AuditEventType.USER_LOGIN && event.result === 'failure') {
        await this.checkFailedLoginPattern(event.userId);
      }
    } catch (error) {
      console.error('🔴 خطأ في فحص التنبيهات:', error);
    }
  }

  private static calculateSummary(events: AuditEvent[]): any {
    const total = events.length;
    const success = events.filter(e => e.result === 'success').length;
    const failure = events.filter(e => e.result === 'failure').length;
    const warning = events.filter(e => e.result === 'warning').length;

    // حساب أهم أنواع الأحداث
    const eventTypeCounts: { [key: string]: number } = {};
    const userCounts: { [key: string]: number } = {};

    events.forEach(event => {
      eventTypeCounts[event.eventType] = (eventTypeCounts[event.eventType] || 0) + 1;
      if (event.userId) {
        userCounts[event.userId] = (userCounts[event.userId] || 0) + 1;
      }
    });

    const topEventTypes = Object.entries(eventTypeCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([type, count]) => ({ type, count }));

    const topUsers = Object.entries(userCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([userId, count]) => ({ userId, count }));

    return {
      totalEvents: total,
      successRate: total > 0 ? (success / total) * 100 : 0,
      failureRate: total > 0 ? (failure / total) * 100 : 0,
      warningRate: total > 0 ? (warning / total) * 100 : 0,
      topEventTypes,
      topUsers
    };
  }

  private static generateTimeline(
    events: AuditEvent[],
    startDate: Date,
    endDate: Date
  ): { date: string; count: number }[] {
    const timeline: { [key: string]: number } = {};
    
    events.forEach(event => {
      const date = event.timestamp.toISOString().split('T')[0];
      timeline[date] = (timeline[date] || 0) + 1;
    });

    return Object.entries(timeline)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([date, count]) => ({ date, count }));
  }

  private static async checkComplianceStatus(events: AuditEvent[]): Promise<any> {
    // فحص الامتثال للمعايير المختلفة
    return {
      gdpr: true, // تحقق من متطلبات GDPR
      saudiDataProtection: true, // تحقق من قانون حماية البيانات السعودي
      iso27001: true // تحقق من معايير ISO 27001
    };
  }

  private static async archiveAuditLog(logData: any): Promise<void> {
    // أرشفة السجل قبل الحذف
    console.log('📦 أرشفة سجل المراجعة:', logData.eventId);
  }

  private static async sendSecurityAlert(event: AuditEvent): Promise<void> {
    console.log('🚨 تنبيه أمني:', event.eventType, event.severity);
  }

  private static async checkFailedLoginPattern(userId?: string): Promise<void> {
    if (!userId) return;
    console.log('🔍 فحص نمط فشل تسجيل الدخول للمستخدم:', userId);
  }
}

// ===== CONVENIENCE FUNCTIONS =====

export const logUserAction = (
  action: string,
  userId: string,
  details: any = {},
  result: 'success' | 'failure' | 'warning' = 'success'
) => {
  return ApexAuditSystem.logAuditEvent(
    AuditEventType.USER_LOGIN,
    AuditCategory.AUTHENTICATION,
    details,
    { userId, action, result }
  );
};

export const logDataAccess = (
  resource: string,
  userId: string,
  details: any = {}
) => {
  return ApexAuditSystem.logAuditEvent(
    AuditEventType.DATA_ACCESS,
    AuditCategory.DATA_MANAGEMENT,
    details,
    { userId, resource, action: 'read', result: 'success' }
  );
};

export const logSecurityViolation = (
  violation: string,
  userId?: string,
  details: any = {}
) => {
  return ApexAuditSystem.logAuditEvent(
    AuditEventType.SECURITY_VIOLATION,
    AuditCategory.SECURITY,
    { violation, ...details },
    { userId, action: 'security_check', result: 'failure' }
  );
};

export default ApexAuditSystem;
