// src/services/customerSegmentationService.ts
"use client";

import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc,
  getDocs, 
  query, 
  where, 
  orderBy,
  Timestamp,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { 
  CustomerSegment, 
  CustomerProfile
} from '@/types';

export class CustomerSegmentationService {
  private segmentsCollection = collection(db, 'customer_segments');
  private customersCollection = collection(db, 'customer_profiles');

  // ===== إدارة التقسيمات =====

  // إنشاء تقسيم جديد
  async createSegment(segmentData: Omit<CustomerSegment, 'id' | 'createdAt' | 'updatedAt' | 'lastCalculatedAt'>): Promise<string> {
    try {
      const segment: Omit<CustomerSegment, 'id'> = {
        ...segmentData,
        stats: {
          customerCount: 0,
          totalRevenue: 0,
          averageOrderValue: 0,
          conversionRate: 0,
          churnRate: 0
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      const segmentRef = await addDoc(this.segmentsCollection, segment);
      
      // حساب العملاء المطابقين للتقسيم
      if (segmentData.settings.autoUpdate) {
        await this.calculateSegmentMembers(segmentRef.id);
      }

      return segmentRef.id;
    } catch (error) {
      console.error('Error creating segment:', error);
      throw new Error('فشل في إنشاء التقسيم');
    }
  }

  // تحديث تقسيم
  async updateSegment(segmentId: string, updates: Partial<CustomerSegment>): Promise<void> {
    try {
      const segmentRef = doc(this.segmentsCollection, segmentId);
      await updateDoc(segmentRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });

      // إعادة حساب العضوية إذا تم تغيير المعايير
      if (updates.criteria) {
        await this.calculateSegmentMembers(segmentId);
      }
    } catch (error) {
      console.error('Error updating segment:', error);
      throw new Error('فشل في تحديث التقسيم');
    }
  }

  // حذف تقسيم
  async deleteSegment(segmentId: string): Promise<void> {
    try {
      const segmentRef = doc(this.segmentsCollection, segmentId);
      await deleteDoc(segmentRef);
    } catch (error) {
      console.error('Error deleting segment:', error);
      throw new Error('فشل في حذف التقسيم');
    }
  }

  // جلب تقسيمات التاجر
  async getMerchantSegments(merchantId: string): Promise<CustomerSegment[]> {
    try {
      const q = query(
        this.segmentsCollection,
        where('merchantId', '==', merchantId),
        orderBy('createdAt', 'desc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CustomerSegment[];
    } catch (error) {
      console.error('Error fetching merchant segments:', error);
      throw new Error('فشل في جلب تقسيمات التاجر');
    }
  }

  // جلب تقسيم محدد
  async getSegment(segmentId: string): Promise<CustomerSegment | null> {
    try {
      const segmentRef = doc(this.segmentsCollection, segmentId);
      const segmentDoc = await getDoc(segmentRef);
      
      if (segmentDoc.exists()) {
        return { id: segmentDoc.id, ...segmentDoc.data() } as CustomerSegment;
      }
      return null;
    } catch (error) {
      console.error('Error fetching segment:', error);
      throw new Error('فشل في جلب التقسيم');
    }
  }

  // ===== حساب العضوية =====

  // حساب العملاء المطابقين للتقسيم
  async calculateSegmentMembers(segmentId: string): Promise<void> {
    try {
      const segment = await this.getSegment(segmentId);
      if (!segment) {
        throw new Error('التقسيم غير موجود');
      }

      // جلب جميع عملاء التاجر
      const customersQuery = query(
        this.customersCollection,
        where('merchantId', '==', segment.merchantId)
      );

      const customersSnapshot = await getDocs(customersQuery);
      const allCustomers = customersSnapshot.docs.map(doc => 
        ({ id: doc.id, ...doc.data() }) as CustomerProfile
      );

      // فلترة العملاء حسب معايير التقسيم
      const matchingCustomers = this.filterCustomersBySegmentCriteria(allCustomers, segment.criteria);

      // حساب الإحصائيات
      const stats = this.calculateSegmentStats(matchingCustomers);

      // تحديث التقسيم
      const segmentRef = doc(this.segmentsCollection, segmentId);
      await updateDoc(segmentRef, {
        stats,
        lastCalculatedAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

    } catch (error) {
      console.error('Error calculating segment members:', error);
      throw new Error('فشل في حساب أعضاء التقسيم');
    }
  }

  // فلترة العملاء حسب معايير التقسيم
  private filterCustomersBySegmentCriteria(
    customers: CustomerProfile[], 
    criteria: CustomerSegment['criteria']
  ): CustomerProfile[] {
    return customers.filter(customer => {
      // معايير ديموغرافية
      if (criteria.demographic) {
        const demo = criteria.demographic;
        
        // العمر
        if (demo.ageRange && customer.personalInfo.dateOfBirth) {
          const age = this.calculateAge(customer.personalInfo.dateOfBirth.toDate());
          if (age < demo.ageRange.min || age > demo.ageRange.max) {
            return false;
          }
        }

        // الجنس
        if (demo.gender && demo.gender.length > 0) {
          if (!customer.personalInfo.gender || !demo.gender.includes(customer.personalInfo.gender)) {
            return false;
          }
        }

        // الموقع
        if (demo.location) {
          const customerCity = customer.addresses.find(addr => addr.isDefault)?.city;
          if (demo.location.cities && demo.location.cities.length > 0) {
            if (!customerCity || !demo.location.cities.includes(customerCity)) {
              return false;
            }
          }
        }
      }

      // معايير سلوكية
      if (criteria.behavioral) {
        const behavioral = criteria.behavioral;
        
        // عدد الطلبات
        if (behavioral.totalOrdersRange) {
          const totalOrders = customer.shoppingBehavior.totalOrders;
          if (totalOrders < behavioral.totalOrdersRange.min || totalOrders > behavioral.totalOrdersRange.max) {
            return false;
          }
        }

        // إجمالي الإنفاق
        if (behavioral.totalSpentRange) {
          const totalSpent = customer.shoppingBehavior.totalSpent;
          if (totalSpent < behavioral.totalSpentRange.min || totalSpent > behavioral.totalSpentRange.max) {
            return false;
          }
        }

        // آخر طلب
        if (behavioral.lastOrderDays && customer.shoppingBehavior.lastOrderDate) {
          const daysSinceLastOrder = Math.floor(
            (Date.now() - customer.shoppingBehavior.lastOrderDate.toMillis()) / (1000 * 60 * 60 * 24)
          );
          if (daysSinceLastOrder > behavioral.lastOrderDays) {
            return false;
          }
        }

        // متوسط قيمة الطلب
        if (behavioral.averageOrderValueRange) {
          const avgOrderValue = customer.shoppingBehavior.averageOrderValue;
          if (avgOrderValue < behavioral.averageOrderValueRange.min || avgOrderValue > behavioral.averageOrderValueRange.max) {
            return false;
          }
        }

        // الفئات المفضلة
        if (behavioral.favoriteCategories && behavioral.favoriteCategories.length > 0) {
          const hasMatchingCategory = behavioral.favoriteCategories.some(category =>
            customer.shoppingBehavior.favoriteCategories.includes(category)
          );
          if (!hasMatchingCategory) {
            return false;
          }
        }
      }

      // معايير التفاعل
      if (criteria.engagement) {
        const engagement = criteria.engagement;
        
        // نقاط الولاء
        if (engagement.loyaltyPointsRange) {
          const loyaltyPoints = customer.stats.loyaltyPoints;
          if (loyaltyPoints < engagement.loyaltyPointsRange.min || loyaltyPoints > engagement.loyaltyPointsRange.max) {
            return false;
          }
        }

        // عدد المراجعات
        if (engagement.reviewsCountRange) {
          const reviewsCount = customer.stats.reviewsCount;
          if (reviewsCount < engagement.reviewsCountRange.min || reviewsCount > engagement.reviewsCountRange.max) {
            return false;
          }
        }

        // نقاط التفاعل
        if (engagement.engagementScoreRange) {
          const engagementScore = customer.segmentation.engagementScore;
          if (engagementScore < engagement.engagementScoreRange.min || engagementScore > engagement.engagementScoreRange.max) {
            return false;
          }
        }

        // احتمالية فقدان العميل
        if (engagement.churnProbabilityRange) {
          const churnProbability = customer.segmentation.churnProbability;
          if (churnProbability < engagement.churnProbabilityRange.min || churnProbability > engagement.churnProbabilityRange.max) {
            return false;
          }
        }
      }

      // معايير مخصصة
      if (criteria.custom) {
        const custom = criteria.custom;
        
        // العلامات
        if (custom.tags && custom.tags.length > 0) {
          const hasMatchingTag = custom.tags.some(tag => customer.tags.includes(tag));
          if (!hasMatchingTag) {
            return false;
          }
        }

        // وجود شكاوى
        if (custom.hasComplaints !== undefined) {
          const hasComplaints = customer.stats.complaintsCount > 0;
          if (hasComplaints !== custom.hasComplaints) {
            return false;
          }
        }

        // عميل VIP
        if (custom.isVIP !== undefined) {
          const isVIP = customer.tags.includes('vip') || customer.segmentation.tier === 'platinum';
          if (isVIP !== custom.isVIP) {
            return false;
          }
        }

        // موافقة التسويق
        if (custom.marketingOptIn !== undefined) {
          if (customer.preferences.marketingOptIn !== custom.marketingOptIn) {
            return false;
          }
        }
      }

      return true;
    });
  }

  // حساب العمر
  private calculateAge(birthDate: Date): number {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }

  // حساب إحصائيات التقسيم
  private calculateSegmentStats(customers: CustomerProfile[]): CustomerSegment['stats'] {
    const customerCount = customers.length;
    
    if (customerCount === 0) {
      return {
        customerCount: 0,
        totalRevenue: 0,
        averageOrderValue: 0,
        conversionRate: 0,
        churnRate: 0
      };
    }

    const totalRevenue = customers.reduce((sum, customer) => 
      sum + customer.shoppingBehavior.totalSpent, 0
    );

    const averageOrderValue = customers.reduce((sum, customer) => 
      sum + customer.shoppingBehavior.averageOrderValue, 0
    ) / customerCount;

    // معدل التحويل (تقدير بناءً على عدد الطلبات)
    const totalOrders = customers.reduce((sum, customer) => 
      sum + customer.shoppingBehavior.totalOrders, 0
    );
    const conversionRate = customerCount > 0 ? (totalOrders / customerCount) * 100 : 0;

    // معدل فقدان العملاء
    const churnRate = customers.reduce((sum, customer) => 
      sum + customer.segmentation.churnProbability, 0
    ) / customerCount * 100;

    return {
      customerCount,
      totalRevenue,
      averageOrderValue,
      conversionRate,
      churnRate
    };
  }

  // ===== تقسيمات تلقائية =====

  // إنشاء تقسيمات افتراضية للتاجر
  async createDefaultSegments(merchantId: string): Promise<void> {
    try {
      const defaultSegments = [
        {
          name: 'العملاء الجدد',
          description: 'العملاء الذين انضموا خلال آخر 30 يوم',
          criteria: {
            behavioral: {
              totalOrdersRange: { min: 0, max: 1 }
            }
          },
          settings: {
            isActive: true,
            autoUpdate: true,
            color: '#10B981',
            icon: 'user-plus'
          }
        },
        {
          name: 'العملاء المخلصون',
          description: 'العملاء الذين لديهم أكثر من 10 طلبات',
          criteria: {
            behavioral: {
              totalOrdersRange: { min: 10, max: 999999 }
            }
          },
          settings: {
            isActive: true,
            autoUpdate: true,
            color: '#F59E0B',
            icon: 'star'
          }
        },
        {
          name: 'عملاء عالي القيمة',
          description: 'العملاء الذين أنفقوا أكثر من 5000 ريال',
          criteria: {
            behavioral: {
              totalSpentRange: { min: 5000, max: 999999 }
            }
          },
          settings: {
            isActive: true,
            autoUpdate: true,
            color: '#8B5CF6',
            icon: 'crown'
          }
        },
        {
          name: 'عملاء معرضون للفقدان',
          description: 'العملاء الذين لم يطلبوا خلال آخر 60 يوم',
          criteria: {
            behavioral: {
              lastOrderDays: 60
            },
            engagement: {
              churnProbabilityRange: { min: 0.5, max: 1 }
            }
          },
          settings: {
            isActive: true,
            autoUpdate: true,
            color: '#EF4444',
            icon: 'alert-triangle'
          }
        }
      ];

      const batch = writeBatch(db);

      for (const segmentData of defaultSegments) {
        const segmentRef = doc(this.segmentsCollection);
        const segment: Omit<CustomerSegment, 'id'> = {
          merchantId,
          ...segmentData,
          stats: {
            customerCount: 0,
            totalRevenue: 0,
            averageOrderValue: 0,
            conversionRate: 0,
            churnRate: 0
          },
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        };

        batch.set(segmentRef, segment);
      }

      await batch.commit();
    } catch (error) {
      console.error('Error creating default segments:', error);
      throw new Error('فشل في إنشاء التقسيمات الافتراضية');
    }
  }

  // تحديث جميع التقسيمات التلقائية
  async updateAllAutoSegments(merchantId: string): Promise<void> {
    try {
      const segments = await this.getMerchantSegments(merchantId);
      const autoSegments = segments.filter(segment => segment.settings.autoUpdate);

      for (const segment of autoSegments) {
        await this.calculateSegmentMembers(segment.id);
      }
    } catch (error) {
      console.error('Error updating auto segments:', error);
      throw new Error('فشل في تحديث التقسيمات التلقائية');
    }
  }
}

export const customerSegmentationService = new CustomerSegmentationService();
