"use client";

import { useEffect } from 'react';
import { Loader2 } from 'lucide-react';

interface SimpleRedirectProps {
  locale: string;
  userType?: string;
}

export default function SimpleRedirect({ locale, userType = 'customer' }: SimpleRedirectProps) {
  useEffect(() => {
    // تحديد مسار التوجيه
    let redirectPath = `/${locale}/dashboard`;
    
    if (userType === 'merchant') {
      redirectPath = `/${locale}/merchant/pending-approval`;
    } else if (userType === 'representative') {
      redirectPath = `/${locale}/representative/signup`;
    }
    
    console.log('🚀 SimpleRedirect: Redirecting to:', redirectPath);
    
    // إعادة توجيه فورية
    window.location.replace(redirectPath);
  }, [locale, userType]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
      <div className="flex flex-col items-center justify-center space-y-4 text-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="text-muted-foreground">جاري التحميل...</p>
        <p className="text-sm text-muted-foreground">جاري توجيهك للصفحة المناسبة...</p>
      </div>
    </div>
  );
}
