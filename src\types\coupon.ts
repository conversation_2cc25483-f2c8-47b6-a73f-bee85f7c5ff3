import type { Timestamp } from 'firebase/firestore';

// أنواع الكوبونات المدعومة
export type CouponType = 'percentage' | 'fixed' | 'free_shipping';

// حالات الكوبون
export type CouponStatus = 'active' | 'inactive' | 'expired' | 'used_up';

// نموذج بيانات الكوبون الرئيسي
export interface CouponDocument {
  id: string;
  merchantId: string;
  code: string;
  type: CouponType;
  value: number;
  minOrderAmount?: number;
  maxDiscount?: number;
  usageLimit: number;
  usedCount: number;
  validFrom: Timestamp;
  validUntil: Timestamp;
  isActive: boolean;
  applicableProducts?: string[];
  applicableCategories?: string[];
  excludedProducts?: string[];
  excludedCategories?: string[];
  customerRestrictions?: {
    firstTimeOnly?: boolean;
    specificCustomers?: string[];
    customerTiers?: string[];
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string;
  description?: string;
  internalNotes?: string;
}

// نتيجة التحقق من صحة الكوبون
export interface CouponValidation {
  isValid: boolean;
  discount: number;
  message: string;
  coupon?: CouponDocument;
  errorCode?: CouponErrorCode;
  freeShipping?: boolean;
}

// أكواد أخطاء الكوبونات
export enum CouponErrorCode {
  INVALID_CODE = 'INVALID_CODE',
  EXPIRED = 'EXPIRED',
  NOT_STARTED = 'NOT_STARTED',
  USAGE_LIMIT_REACHED = 'USAGE_LIMIT_REACHED',
  MIN_ORDER_NOT_MET = 'MIN_ORDER_NOT_MET',
  PRODUCT_NOT_APPLICABLE = 'PRODUCT_NOT_APPLICABLE',
  CUSTOMER_NOT_ELIGIBLE = 'CUSTOMER_NOT_ELIGIBLE',
  INACTIVE = 'INACTIVE',
  ALREADY_USED = 'ALREADY_USED'
}

// نتيجة تطبيق الخصم
export interface DiscountResult {
  originalAmount: number;
  discountAmount: number;
  finalAmount: number;
  couponCode: string;
  freeShipping: boolean;
  appliedAt: Timestamp;
}

// بيانات سلة التسوق للتحقق من الكوبون
export interface CartData {
  totalAmount: number;
  items: CartItem[];
  customerId: string;
  merchantId: string;
}

export interface CartItem {
  productId: string;
  categoryId: string;
  quantity: number;
  price: number;
  total: number;
}

// إحصائيات الكوبونات
export interface CouponAnalytics {
  totalCoupons: number;
  activeCoupons: number;
  expiredCoupons: number;
  totalUsage: number;
  totalDiscount: number;
  averageDiscount: number;
  topPerformingCoupons: CouponStats[];
  usageByType: Record<CouponType, number>;
  usageByMonth: MonthlyUsage[];
}

export interface CouponStats {
  couponId: string;
  code: string;
  usageCount: number;
  totalDiscount: number;
  conversionRate: number;
}

export interface MonthlyUsage {
  month: string;
  year: number;
  usageCount: number;
  totalDiscount: number;
}

// بيانات إنشاء كوبون جديد
export interface CreateCouponData {
  code: string;
  type: CouponType;
  value: number;
  minOrderAmount?: number;
  maxDiscount?: number;
  usageLimit: number;
  validFrom: Date;
  validUntil: Date;
  applicableProducts?: string[];
  applicableCategories?: string[];
  excludedProducts?: string[];
  excludedCategories?: string[];
  customerRestrictions?: {
    firstTimeOnly?: boolean;
    specificCustomers?: string[];
    customerTiers?: string[];
  };
  description?: string;
  internalNotes?: string;
}

// بيانات تحديث الكوبون
export interface UpdateCouponData extends Partial<CreateCouponData> {
  isActive?: boolean;
}

// سجل استخدام الكوبون
export interface CouponUsageLog {
  id: string;
  couponId: string;
  customerId: string;
  orderId: string;
  discountAmount: number;
  originalAmount: number;
  finalAmount: number;
  usedAt: Timestamp;
  merchantId: string;
}

// إعدادات الكوبونات للتاجر
export interface MerchantCouponSettings {
  merchantId: string;
  allowStackingCoupons: boolean;
  maxCouponsPerOrder: number;
  autoGenerateEnabled: boolean;
  defaultValidityDays: number;
  requireApproval: boolean;
  notificationSettings: {
    onCouponCreated: boolean;
    onCouponUsed: boolean;
    onCouponExpired: boolean;
    lowUsageAlert: boolean;
    lowUsageThreshold: number;
  };
  updatedAt: Timestamp;
}

// فلاتر البحث في الكوبونات
export interface CouponFilters {
  status?: CouponStatus[];
  type?: CouponType[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  usageRange?: {
    min: number;
    max: number;
  };
  searchTerm?: string;
}

// ترتيب الكوبونات
export interface CouponSortOptions {
  field: 'createdAt' | 'validUntil' | 'usedCount' | 'value';
  direction: 'asc' | 'desc';
}

// استجابة API للكوبونات
export interface CouponsResponse {
  coupons: CouponDocument[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// قالب كوبون للإنشاء السريع
export interface CouponTemplate {
  id: string;
  name: string;
  description: string;
  type: CouponType;
  value: number;
  validityDays: number;
  usageLimit: number;
  minOrderAmount?: number;
  maxDiscount?: number;
  isDefault: boolean;
}

// إعدادات الإشعارات للكوبونات
export interface CouponNotificationSettings {
  emailOnCreate: boolean;
  emailOnUse: boolean;
  emailOnExpiry: boolean;
  smsOnUse: boolean;
  pushOnUse: boolean;
  webhookUrl?: string;
}

// تقرير أداء الكوبونات
export interface CouponPerformanceReport {
  period: {
    start: Date;
    end: Date;
  };
  totalCoupons: number;
  totalUsage: number;
  totalDiscount: number;
  averageOrderValue: number;
  conversionRate: number;
  topCoupons: CouponStats[];
  categoryBreakdown: Record<string, number>;
  customerSegmentBreakdown: Record<string, number>;
  timeSeriesData: {
    date: string;
    usage: number;
    discount: number;
  }[];
}
