// cypress/e2e/local-ai-models-management.cy.ts - اختبارات إدارة النماذج المحلية

describe('🧠 إدارة النماذج المحلية - Transformers.js', () => {
  let networkRequests: any[] = [];
  let modelLoadEvents: any[] = [];

  beforeEach(() => {
    // مراقبة طلبات الشبكة
    networkRequests = [];
    modelLoadEvents = [];

    // مراقبة تحميل النماذج المحلية
    cy.window().then((win) => {
      win.addEventListener('model-load-start', (event: any) => {
        modelLoadEvents.push({
          type: 'load-start',
          modelId: event.detail.modelId,
          timestamp: Date.now()
        });
      });

      win.addEventListener('model-load-complete', (event: any) => {
        modelLoadEvents.push({
          type: 'load-complete',
          modelId: event.detail.modelId,
          timestamp: Date.now()
        });
      });
    });

    // اعتراض طلبات الشبكة
    cy.intercept('**/*', (req) => {
      networkRequests.push({
        url: req.url,
        method: req.method,
        timestamp: Date.now()
      });

      // السماح فقط بتحميل النماذج من Hugging Face CDN
      if (req.url.includes('huggingface.co') && req.url.includes('.onnx')) {
        // السماح بتحميل ملفات النماذج
        req.continue();
      } else if (req.url.includes('api.huggingface.co/models')) {
        // حظر API calls للمعالجة
        req.reply({ statusCode: 403, body: 'محظور: استخدم المعالجة المحلية' });
      }
    });

    cy.login('merchant');
    cy.visit('/ai-document-processing');
  });

  describe('🔧 إدارة النماذج', () => {
    it('يجب أن يعرض حالة النماذج المحلية', () => {
      // الانتقال لتبويب النماذج المحلية
      cy.get('[data-testid="models-tab"]').click();
      
      // التحقق من وجود قائمة النماذج
      cy.get('[data-testid="models-list"]').should('be.visible');
      
      // التحقق من النماذج الأساسية
      cy.contains('TrOCR للنصوص المطبوعة').should('be.visible');
      cy.contains('BERT متعدد اللغات').should('be.visible');
      cy.contains('مصنف المستندات').should('be.visible');
      
      // التحقق من أحجام النماذج
      cy.contains('~45MB').should('be.visible');
      cy.contains('~110MB').should('be.visible');
      cy.contains('~65MB').should('be.visible');
    });

    it('يجب أن يحمل النماذج الأساسية مسبقاً', () => {
      cy.get('[data-testid="models-tab"]').click();
      
      // النقر على زر التحميل المسبق
      cy.get('[data-testid="preload-models-btn"]').click();
      
      // التحقق من بدء التحميل
      cy.contains('جاري التحميل...').should('be.visible');
      
      // انتظار شريط التقدم
      cy.get('[data-testid="preload-progress"]').should('be.visible');
      
      // التحقق من تحديث حالة النماذج
      cy.get('[data-testid="model-status-ready"]', { timeout: 60000 })
        .should('have.length.at.least', 1);
      
      // التحقق من عدم وجود طلبات API خارجية للمعالجة
      cy.then(() => {
        const apiCalls = networkRequests.filter(req => 
          req.url.includes('api.huggingface.co') && 
          req.method === 'POST'
        );
        expect(apiCalls).to.have.length(0);
      });
    });

    it('يجب أن يعرض إحصائيات الذاكرة', () => {
      cy.get('[data-testid="memory-tab"]').click();
      
      // التحقق من عرض استخدام الذاكرة
      cy.get('[data-testid="memory-usage-chart"]').should('be.visible');
      cy.contains('استخدام الذاكرة').should('be.visible');
      
      // التحقق من الإحصائيات
      cy.get('[data-testid="loaded-models-count"]').should('be.visible');
      cy.get('[data-testid="available-memory"]').should('be.visible');
      cy.get('[data-testid="memory-usage-percentage"]').should('be.visible');
      
      // التحقق من تفصيل النماذج
      cy.get('[data-testid="model-breakdown"]').should('be.visible');
    });

    it('يجب أن يدير النماذج بكفاءة', () => {
      cy.get('[data-testid="models-tab"]').click();
      
      // تحميل نموذج واحد
      cy.get('[data-testid="load-model-btn"]').first().click();
      
      // التحقق من تحديث الحالة
      cy.get('[data-testid="model-status-downloading"]').should('be.visible');
      
      // انتظار اكتمال التحميل
      cy.get('[data-testid="model-status-ready"]', { timeout: 30000 })
        .should('be.visible');
      
      // إلغاء تحميل النموذج
      cy.get('[data-testid="unload-model-btn"]').first().click();
      
      // التحقق من تحرير الذاكرة
      cy.get('[data-testid="model-status-not-loaded"]').should('be.visible');
    });
  });

  describe('⚡ نظام التحسين التلقائي', () => {
    it('يجب أن يعرض لوحة التحسين', () => {
      cy.get('[data-testid="optimization-tab"]').click();
      
      // التحقق من الإحصائيات الرئيسية
      cy.get('[data-testid="total-optimizations"]').should('be.visible');
      cy.get('[data-testid="success-rate"]').should('be.visible');
      cy.get('[data-testid="memory-freed"]').should('be.visible');
      cy.get('[data-testid="performance-gain"]').should('be.visible');
      
      // التحقق من استراتيجيات التحسين
      cy.contains('تنظيف الذاكرة من النماذج غير المستخدمة').should('be.visible');
      cy.contains('تحسين الكاش وإزالة البيانات القديمة').should('be.visible');
      cy.contains('تحميل النماذج المتوقع استخدامها مسبقاً').should('be.visible');
    });

    it('يجب أن يشغل التحسين اليدوي', () => {
      cy.get('[data-testid="optimization-tab"]').click();
      
      // تحديد استراتيجيات التحسين
      cy.get('[data-testid="strategy-memory-cleanup"]').check();
      cy.get('[data-testid="strategy-cache-optimization"]').check();
      
      // تشغيل التحسين
      cy.get('[data-testid="run-optimization-btn"]').click();
      
      // التحقق من بدء التحسين
      cy.contains('جاري التحسين...').should('be.visible');
      
      // انتظار اكتمال التحسين
      cy.get('[data-testid="optimization-complete"]', { timeout: 10000 })
        .should('be.visible');
      
      // التحقق من تحديث الإحصائيات
      cy.get('[data-testid="recent-optimizations"]').should('contain', 'تم التحسين بنجاح');
    });

    it('يجب أن يدير إعدادات التحسين التلقائي', () => {
      cy.get('[data-testid="optimization-tab"]').click();
      
      // تفعيل التحسين التلقائي
      cy.get('[data-testid="auto-optimization-toggle"]').click();
      
      // تفعيل الوضع العدواني
      cy.get('[data-testid="aggressive-mode-toggle"]').click();
      
      // التحقق من حفظ الإعدادات
      cy.contains('تم تحديث إعدادات التحسين').should('be.visible');
    });
  });

  describe('📊 مراقبة الأداء', () => {
    it('يجب أن يعرض إحصائيات الأداء', () => {
      cy.get('[data-testid="performance-tab"]').click();
      
      // التحقق من وجود مقاييس الأداء
      cy.get('[data-testid="performance-metrics"]').should('be.visible');
      
      // في المستقبل سيتم عرض:
      // - أوقات تحميل النماذج
      // - معدلات نجاح التحميل
      // - أوقات الاستجابة
      // - إحصائيات الاستخدام
      
      cy.contains('سيتم عرض إحصائيات الأداء هنا').should('be.visible');
    });
  });

  describe('⚙️ الإعدادات المتقدمة', () => {
    it('يجب أن يدير إعدادات النظام', () => {
      cy.get('[data-testid="settings-tab"]').click();
      
      // التحقق من الإعدادات المتاحة
      cy.contains('التنظيف التلقائي').should('be.visible');
      cy.contains('استراتيجية التحميل المسبق').should('be.visible');
      cy.contains('الحد الأقصى للذاكرة').should('be.visible');
      
      // تغيير إعدادات التنظيف التلقائي
      cy.get('[data-testid="auto-cleanup-toggle"]').click();
      
      // التحقق من تحديث الحالة
      cy.get('[data-testid="auto-cleanup-status"]').should('contain', 'معطل');
    });
  });

  describe('🛡️ اختبار الخصوصية', () => {
    it('يجب ألا يرسل بيانات للخوادم الخارجية', () => {
      // رفع مستند للمعالجة
      cy.get('[data-testid="upload-tab"]').click();
      
      const testFile = new File(['test content'], 'test-document.jpg', {
        type: 'image/jpeg'
      });
      
      cy.get('[data-testid="file-input"]').selectFile(testFile, { force: true });
      
      // بدء المعالجة
      cy.get('[data-testid="process-document-btn"]').click();
      
      // انتظار اكتمال المعالجة
      cy.get('[data-testid="processing-complete"]', { timeout: 30000 })
        .should('be.visible');
      
      // التحقق من عدم وجود طلبات API خارجية للمعالجة
      cy.then(() => {
        const externalApiCalls = networkRequests.filter(req => 
          req.url.includes('api.huggingface.co') ||
          req.url.includes('api.openai.com') ||
          req.url.includes('generativelanguage.googleapis.com')
        );
        expect(externalApiCalls).to.have.length(0);
      });
    });

    it('يجب أن يعمل بدون اتصال إنترنت (بعد تحميل النماذج)', () => {
      // تحميل النماذج أولاً
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      
      // انتظار اكتمال التحميل
      cy.get('[data-testid="model-status-ready"]', { timeout: 60000 })
        .should('have.length.at.least', 3);
      
      // محاكاة انقطاع الإنترنت
      cy.intercept('**/*', { forceNetworkError: true });
      
      // محاولة معالجة مستند
      cy.get('[data-testid="upload-tab"]').click();
      
      const testFile = new File(['test content'], 'test-document.jpg', {
        type: 'image/jpeg'
      });
      
      cy.get('[data-testid="file-input"]').selectFile(testFile, { force: true });
      cy.get('[data-testid="process-document-btn"]').click();
      
      // التحقق من نجاح المعالجة المحلية
      cy.get('[data-testid="processing-complete"]', { timeout: 30000 })
        .should('be.visible');
      
      // التحقق من وجود نتائج
      cy.get('[data-testid="ocr-results"]').should('be.visible');
      cy.get('[data-testid="ner-results"]').should('be.visible');
      cy.get('[data-testid="classification-results"]').should('be.visible');
    });
  });

  describe('🚀 اختبار الأداء', () => {
    it('يجب أن يعالج المستندات في وقت معقول', () => {
      // تحميل النماذج مسبقاً
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      
      cy.get('[data-testid="model-status-ready"]', { timeout: 60000 })
        .should('have.length.at.least', 3);
      
      // قياس وقت المعالجة
      const startTime = Date.now();
      
      cy.get('[data-testid="upload-tab"]').click();
      
      const testFile = new File(['test content'], 'test-document.jpg', {
        type: 'image/jpeg'
      });
      
      cy.get('[data-testid="file-input"]').selectFile(testFile, { force: true });
      cy.get('[data-testid="process-document-btn"]').click();
      
      cy.get('[data-testid="processing-complete"]', { timeout: 10000 })
        .should('be.visible')
        .then(() => {
          const endTime = Date.now();
          const processingTime = endTime - startTime;
          
          // التحقق من أن المعالجة تمت في أقل من 10 ثواني
          expect(processingTime).to.be.lessThan(10000);
          
          // تسجيل وقت المعالجة
          cy.log(`وقت المعالجة: ${processingTime}ms`);
        });
    });

    it('يجب أن يدير الذاكرة بكفاءة', () => {
      // مراقبة استخدام الذاكرة
      cy.window().then((win) => {
        const initialMemory = (win.performance as any).memory?.usedJSHeapSize || 0;
        
        // تحميل عدة نماذج
        cy.get('[data-testid="models-tab"]').click();
        cy.get('[data-testid="preload-models-btn"]').click();
        
        cy.get('[data-testid="model-status-ready"]', { timeout: 60000 })
          .should('have.length.at.least', 3)
          .then(() => {
            const afterLoadMemory = (win.performance as any).memory?.usedJSHeapSize || 0;
            const memoryIncrease = afterLoadMemory - initialMemory;
            
            // التحقق من أن الزيادة في الذاكرة معقولة (أقل من 1GB)
            expect(memoryIncrease).to.be.lessThan(1024 * 1024 * 1024);
            
            cy.log(`زيادة الذاكرة: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
          });
      });
    });
  });

  afterEach(() => {
    // تسجيل إحصائيات الاختبار
    cy.then(() => {
      console.log('📊 إحصائيات الاختبار:');
      console.log(`- طلبات الشبكة: ${networkRequests.length}`);
      console.log(`- أحداث تحميل النماذج: ${modelLoadEvents.length}`);
      
      const modelLoads = modelLoadEvents.filter(e => e.type === 'load-complete');
      console.log(`- نماذج محملة: ${modelLoads.length}`);
    });
  });
});
