"use client";

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, FileText, CheckCircle } from 'lucide-react';
import ProcessingModeSelector from '@/components/ai/ProcessingModeSelector';
import SimpleProcessingWarning from '@/components/ai/SimpleProcessingWarning';
import { detectDeviceCapabilities } from '@/utils/device-capabilities';

interface DocumentUploadWithAIProps {
  onDocumentsProcessed: (results: any[]) => void;
}

export default function DocumentUploadWithAI({ onDocumentsProcessed }: DocumentUploadWithAIProps) {
  const [step, setStep] = useState<'upload' | 'processing-mode' | 'processing' | 'complete'>('upload');
  const [documents, setDocuments] = useState<File[]>([]);
  const [deviceCapabilities, setDeviceCapabilities] = useState<any>(null);
  const [processingMode, setProcessingMode] = useState<'local' | 'cloud' | 'hybrid'>('local');

  const handleFileUpload = async (files: FileList) => {
    const fileArray = Array.from(files);
    setDocuments(fileArray);

    // تحليل قدرات الجهاز
    const capabilities = await detectDeviceCapabilities();
    setDeviceCapabilities(capabilities);

    // تحديد الخطوة التالية حسب قدرات الجهاز
    if (capabilities.isLowEnd) {
      // جهاز ضعيف - تحذير بسيط ومعالجة سحابية
      setProcessingMode('cloud');
      setStep('processing-mode');
    } else if (capabilities.ram >= 6144) {
      // جهاز قوي - معالجة محلية مباشرة
      setProcessingMode('local');
      setStep('processing-mode');
    } else {
      // جهاز متوسط - عرض خيارات
      setStep('processing-mode');
    }
  };

  const handleModeSelect = (mode: 'local' | 'cloud' | 'hybrid') => {
    setProcessingMode(mode);
    setStep('processing');
    // بدء المعالجة الفعلية
    processDocuments(mode);
  };

  const processDocuments = async (mode: 'local' | 'cloud' | 'hybrid') => {
    try {
      // هنا يتم استدعاء API المعالجة حسب الوضع المختار
      const results = await processDocumentsWithMode(documents, mode);
      onDocumentsProcessed(results);
      setStep('complete');
    } catch (error) {
      console.error('خطأ في معالجة الوثائق:', error);
    }
  };

  const processDocumentsWithMode = async (docs: File[], mode: string) => {
    // محاكاة معالجة الوثائق
    return docs.map(doc => ({
      name: doc.name,
      extractedData: {
        // بيانات مستخرجة وهمية
        companyName: "شركة تجارية",
        registrationNumber: "*********",
        issueDate: "2024-01-01"
      }
    }));
  };

  if (step === 'upload') {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            رفع وثائق التسجيل
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <Upload className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-lg font-medium mb-2">اسحب الملفات هنا أو انقر للاختيار</p>
              <p className="text-sm text-gray-500 mb-4">
                الوثائق المطلوبة: السجل التجاري، الهوية الشخصية، شهادة الضريبة
              </p>
              <input
                type="file"
                multiple
                accept="image/*,.pdf"
                onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                className="hidden"
                id="document-upload"
              />
              <Button asChild>
                <label htmlFor="document-upload" className="cursor-pointer">
                  اختيار الملفات
                </label>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (step === 'processing-mode') {
    const documentSizes = documents.map(doc => doc.size / 1024 / 1024); // MB

    // للأجهزة الضعيفة أو القوية - تحذير بسيط
    if (deviceCapabilities?.isLowEnd || deviceCapabilities?.ram >= 6144) {
      return (
        <SimpleProcessingWarning
          isLowEndDevice={deviceCapabilities.isLowEnd}
          onProceed={() => handleModeSelect(processingMode)}
          onCancel={() => setStep('upload')}
        />
      );
    }

    // للأجهزة المتوسطة - واجهة الاختيار الكاملة
    return (
      <ProcessingModeSelector
        documentCount={documents.length}
        documentSizes={documentSizes}
        onModeSelect={handleModeSelect}
        onCancel={() => setStep('upload')}
      />
    );
  }

  if (step === 'processing') {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>جاري معالجة الوثائق...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {documents.map((doc, index) => (
              <div key={index} className="flex items-center gap-3 p-3 border rounded">
                <FileText className="h-5 w-5" />
                <span className="flex-1">{doc.name}</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (step === 'complete') {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-600">
            <CheckCircle className="h-5 w-5" />
            تم رفع الوثائق بنجاح
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-gray-600">
            تم استخراج البيانات من وثائقك بنجاح. سيتم مراجعة طلبك خلال 24 ساعة.
          </p>
        </CardContent>
      </Card>
    );
  }

  return null;
}
