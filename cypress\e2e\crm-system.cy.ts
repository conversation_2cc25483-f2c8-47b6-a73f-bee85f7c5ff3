/// <reference types="cypress" />

/**
 * اختبارات نظام CRM
 * تختبر جميع وظائف إدارة علاقات العملاء
 */

describe('👥 نظام CRM - إدارة علاقات العملاء', () => {
  beforeEach(() => {
    // إعداد البيانات الوهمية
    cy.mockLogin('merchant')
    cy.mockFirebaseAuth()
    cy.mockCRMCustomers()
    
    // زيارة صفحة CRM
    cy.visitWithLocale('/merchant/crm')
    cy.waitForLoadingToFinish()
  })

  afterEach(() => {
    cy.mockLogout()
  })

  describe('📋 لوحة تحكم CRM الرئيسية', () => {
    it('يجب أن تعرض لوحة تحكم CRM بشكل صحيح', () => {
      // التحقق من وجود العناصر الأساسية
      cy.get('[data-testid="crm-dashboard"]').should('be.visible')
      cy.shouldContainArabicText('إدارة علاقات العملاء')
      cy.shouldContainArabicText('العملاء')
      cy.shouldContainArabicText('التفاعلات')
      cy.shouldContainArabicText('التقسيمات')
      
      // التحقق من وجود الإحصائيات
      cy.get('[data-testid="total-customers"]').should('be.visible')
      cy.get('[data-testid="active-customers"]').should('be.visible')
      cy.get('[data-testid="customer-satisfaction"]').should('be.visible')
      cy.get('[data-testid="retention-rate"]').should('be.visible')
    })

    it('يجب أن تعرض الرسوم البيانية للتحليلات', () => {
      // التحقق من وجود الرسوم البيانية
      cy.get('[data-testid="customer-growth-chart"]').should('be.visible')
      cy.get('[data-testid="customer-segments-chart"]').should('be.visible')
      cy.get('[data-testid="interaction-trends-chart"]').should('be.visible')
      cy.get('[data-testid="revenue-by-customer-chart"]').should('be.visible')
    })

    it('يجب أن تعرض العملاء الأحدث', () => {
      // التحقق من قائمة العملاء الأحدث
      cy.get('[data-testid="recent-customers"]').should('be.visible')
      cy.shouldContainArabicText('العملاء الجدد')
      
      // التحقق من وجود بيانات العملاء
      cy.get('[data-testid="customer-card"]').should('have.length.at.least', 1)
      cy.get('[data-testid="customer-name"]').should('be.visible')
      cy.get('[data-testid="customer-email"]').should('be.visible')
      cy.get('[data-testid="customer-orders"]').should('be.visible')
    })
  })

  describe('👤 إدارة ملفات العملاء', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة العملاء
      cy.get('[data-testid="customers-tab"]').click()
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض قائمة العملاء بشكل صحيح', () => {
      // التحقق من وجود جدول العملاء
      cy.get('[data-testid="customers-table"]').should('be.visible')
      
      // التحقق من أعمدة الجدول
      cy.shouldContainArabicText('الاسم')
      cy.shouldContainArabicText('البريد الإلكتروني')
      cy.shouldContainArabicText('الهاتف')
      cy.shouldContainArabicText('إجمالي الطلبات')
      cy.shouldContainArabicText('إجمالي الإنفاق')
      cy.shouldContainArabicText('آخر طلب')
      cy.shouldContainArabicText('التقسيم')
    })

    it('يجب أن يبحث في العملاء بشكل صحيح', () => {
      // البحث بالاسم
      cy.get('[data-testid="customer-search"]').type('أحمد')
      cy.get('[data-testid="search-btn"]').click()
      
      // التحقق من نتائج البحث
      cy.get('[data-testid="customer-row"]').should('contain.text', 'أحمد')
      
      // مسح البحث
      cy.get('[data-testid="clear-search"]').click()
      cy.get('[data-testid="customer-row"]').should('have.length.at.least', 1)
    })

    it('يجب أن يفلتر العملاء حسب التقسيم', () => {
      // فلترة حسب التقسيم
      cy.get('[data-testid="segment-filter"]').select('high_value')
      
      // التحقق من الفلترة
      cy.get('[data-testid="customer-row"]').each(($row) => {
        cy.wrap($row).find('[data-testid="customer-segment"]')
          .should('contain.text', 'عملاء عاليو القيمة')
      })
    })

    it('يجب أن يعرض تفاصيل العميل', () => {
      // النقر على عميل
      cy.get('[data-testid="customer-row"]').first().click()
      
      // التحقق من فتح صفحة تفاصيل العميل
      cy.get('[data-testid="customer-profile"]').should('be.visible')
      cy.shouldContainArabicText('ملف العميل')
      
      // التحقق من وجود المعلومات الأساسية
      cy.get('[data-testid="customer-info"]').should('be.visible')
      cy.get('[data-testid="customer-stats"]').should('be.visible')
      cy.get('[data-testid="customer-orders-history"]').should('be.visible')
      cy.get('[data-testid="customer-interactions"]').should('be.visible')
    })

    it('يجب أن يضيف ملاحظة للعميل', () => {
      // فتح ملف العميل
      cy.get('[data-testid="customer-row"]').first().click()
      
      // إضافة ملاحظة
      cy.get('[data-testid="add-note-btn"]').click()
      cy.get('[data-testid="note-content"]').type('عميل مهم - يحتاج متابعة خاصة')
      cy.get('[data-testid="save-note"]').click()
      
      // التحقق من إضافة الملاحظة
      cy.get('[data-testid="customer-notes"]').should('contain.text', 'عميل مهم')
    })

    it('يجب أن يضيف علامة للعميل', () => {
      // فتح ملف العميل
      cy.get('[data-testid="customer-row"]').first().click()
      
      // إضافة علامة
      cy.get('[data-testid="add-tag-btn"]').click()
      cy.get('[data-testid="tag-input"]').type('vip')
      cy.get('[data-testid="add-tag"]').click()
      
      // التحقق من إضافة العلامة
      cy.get('[data-testid="customer-tags"]').should('contain.text', 'vip')
    })
  })

  describe('💬 إدارة التفاعلات', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة التفاعلات
      cy.get('[data-testid="interactions-tab"]').click()
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض قائمة التفاعلات', () => {
      // التحقق من وجود جدول التفاعلات
      cy.get('[data-testid="interactions-table"]').should('be.visible')
      
      // التحقق من أعمدة الجدول
      cy.shouldContainArabicText('العميل')
      cy.shouldContainArabicText('نوع التفاعل')
      cy.shouldContainArabicText('الموضوع')
      cy.shouldContainArabicText('التاريخ')
      cy.shouldContainArabicText('الحالة')
    })

    it('يجب أن يضيف تفاعل جديد', () => {
      // إضافة تفاعل جديد
      cy.get('[data-testid="add-interaction"]').click()
      
      // ملء نموذج التفاعل
      cy.get('[data-testid="customer-select"]').select('customer-1')
      cy.get('[data-testid="interaction-type"]').select('phone_call')
      cy.fillForm({
        'interaction-subject': 'استفسار عن المنتج',
        'interaction-description': 'العميل يسأل عن توفر المنتج وأسعار الشحن'
      })
      
      // حفظ التفاعل
      cy.get('[data-testid="save-interaction"]').click()
      
      // التحقق من إضافة التفاعل
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة التفاعل بنجاح')
    })

    it('يجب أن يحدث حالة التفاعل', () => {
      // تحديث حالة التفاعل
      cy.get('[data-testid="interaction-row"]').first().within(() => {
        cy.get('[data-testid="interaction-status"]').select('resolved')
      })
      
      // التحقق من تحديث الحالة
      cy.get('[data-testid="interaction-row"]').first()
        .find('[data-testid="status-badge"]')
        .should('contain.text', 'محلول')
    })

    it('يجب أن يفلتر التفاعلات حسب النوع', () => {
      // فلترة حسب نوع التفاعل
      cy.get('[data-testid="interaction-type-filter"]').select('email')
      
      // التحقق من الفلترة
      cy.get('[data-testid="interaction-row"]').each(($row) => {
        cy.wrap($row).find('[data-testid="interaction-type-cell"]')
          .should('contain.text', 'بريد إلكتروني')
      })
    })
  })

  describe('📊 تقسيم العملاء', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة التقسيمات
      cy.get('[data-testid="segments-tab"]').click()
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض التقسيمات الافتراضية', () => {
      // التحقق من وجود التقسيمات الافتراضية
      cy.get('[data-testid="segments-list"]').should('be.visible')
      cy.shouldContainArabicText('عملاء عاليو القيمة')
      cy.shouldContainArabicText('عملاء جدد')
      cy.shouldContainArabicText('عملاء غير نشطين')
      cy.shouldContainArabicText('عملاء مخلصون')
    })

    it('يجب أن ينشئ تقسيم جديد', () => {
      // إنشاء تقسيم جديد
      cy.get('[data-testid="create-segment"]').click()
      
      // ملء نموذج التقسيم
      cy.fillForm({
        'segment-name': 'عملاء الإلكترونيات',
        'segment-description': 'العملاء الذين يشترون منتجات إلكترونية'
      })
      
      // إضافة معايير التقسيم
      cy.get('[data-testid="add-criteria"]').click()
      cy.get('[data-testid="criteria-field"]').select('preferredCategories')
      cy.get('[data-testid="criteria-operator"]').select('contains')
      cy.get('[data-testid="criteria-value"]').type('electronics')
      
      // حفظ التقسيم
      cy.get('[data-testid="save-segment"]').click()
      
      // التحقق من إنشاء التقسيم
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إنشاء التقسيم بنجاح')
    })

    it('يجب أن يعرض عملاء التقسيم', () => {
      // عرض عملاء تقسيم معين
      cy.get('[data-testid="segment-item"]').first().within(() => {
        cy.get('[data-testid="view-customers"]').click()
      })
      
      // التحقق من عرض العملاء
      cy.get('[data-testid="segment-customers"]').should('be.visible')
      cy.get('[data-testid="customer-count"]').should('be.visible')
      cy.get('[data-testid="customers-list"]').should('be.visible')
    })
  })

  describe('📈 تحليلات العملاء', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة التحليلات
      cy.get('[data-testid="analytics-tab"]').click()
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض تحليلات شاملة للعملاء', () => {
      // التحقق من وجود المقاييس الأساسية
      cy.get('[data-testid="customer-lifetime-value"]').should('be.visible')
      cy.get('[data-testid="customer-acquisition-cost"]').should('be.visible')
      cy.get('[data-testid="churn-rate"]').should('be.visible')
      cy.get('[data-testid="repeat-purchase-rate"]').should('be.visible')
      
      // التحقق من الرسوم البيانية
      cy.get('[data-testid="customer-behavior-chart"]').should('be.visible')
      cy.get('[data-testid="purchase-patterns-chart"]').should('be.visible')
      cy.get('[data-testid="customer-journey-chart"]').should('be.visible')
    })

    it('يجب أن يفلتر التحليلات حسب الفترة الزمنية', () => {
      // تغيير الفترة الزمنية
      cy.get('[data-testid="date-range-filter"]').select('last_30_days')
      
      // التحقق من تحديث البيانات
      cy.get('[data-testid="analytics-loading"]').should('be.visible')
      cy.get('[data-testid="analytics-loading"]').should('not.exist')
      cy.get('[data-testid="customer-lifetime-value"]').should('be.visible')
    })

    it('يجب أن يصدر تقرير التحليلات', () => {
      // تصدير التقرير
      cy.get('[data-testid="export-analytics"]').click()
      
      // اختيار نوع التصدير
      cy.get('[data-testid="export-format"]').select('excel')
      cy.get('[data-testid="confirm-export"]').click()
      
      // التحقق من بدء التصدير
      cy.get('[data-testid="export-status"]').should('contain.text', 'جاري التصدير')
    })
  })

  describe('📧 التواصل مع العملاء', () => {
    it('يجب أن يرسل رسالة بريد إلكتروني لعميل', () => {
      // فتح ملف العميل
      cy.get('[data-testid="customers-tab"]').click()
      cy.get('[data-testid="customer-row"]').first().click()
      
      // إرسال بريد إلكتروني
      cy.get('[data-testid="send-email"]').click()
      
      // ملء نموذج البريد الإلكتروني
      cy.fillForm({
        'email-subject': 'عرض خاص لك',
        'email-content': 'نحن سعداء لتقديم عرض خاص لك كعميل مميز'
      })
      
      // إرسال البريد
      cy.get('[data-testid="send-email-btn"]').click()
      
      // التحقق من الإرسال
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إرسال البريد الإلكتروني بنجاح')
    })

    it('يجب أن يرسل حملة تسويقية لتقسيم', () => {
      // الانتقال إلى التقسيمات
      cy.get('[data-testid="segments-tab"]').click()
      
      // إرسال حملة لتقسيم
      cy.get('[data-testid="segment-item"]').first().within(() => {
        cy.get('[data-testid="send-campaign"]').click()
      })
      
      // ملء نموذج الحملة
      cy.fillForm({
        'campaign-name': 'حملة العروض الشتوية',
        'campaign-subject': 'عروض شتوية مميزة',
        'campaign-content': 'اكتشف مجموعتنا الجديدة من المنتجات الشتوية'
      })
      
      // إرسال الحملة
      cy.get('[data-testid="send-campaign-btn"]').click()
      
      // التحقق من الإرسال
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إرسال الحملة بنجاح')
    })
  })

  describe('🔍 البحث المتقدم', () => {
    it('يجب أن يبحث بمعايير متعددة', () => {
      // فتح البحث المتقدم
      cy.get('[data-testid="advanced-search"]').click()
      
      // إضافة معايير البحث
      cy.get('[data-testid="search-criteria-1"]').within(() => {
        cy.get('[data-testid="field"]').select('totalSpent')
        cy.get('[data-testid="operator"]').select('greater_than')
        cy.get('[data-testid="value"]').type('1000')
      })
      
      // إضافة معيار آخر
      cy.get('[data-testid="add-criteria"]').click()
      cy.get('[data-testid="search-criteria-2"]').within(() => {
        cy.get('[data-testid="field"]').select('totalOrders')
        cy.get('[data-testid="operator"]').select('greater_than')
        cy.get('[data-testid="value"]').type('5')
      })
      
      // تنفيذ البحث
      cy.get('[data-testid="execute-search"]').click()
      
      // التحقق من النتائج
      cy.get('[data-testid="search-results"]').should('be.visible')
      cy.get('[data-testid="results-count"]').should('contain.text', 'نتيجة')
    })
  })
})
