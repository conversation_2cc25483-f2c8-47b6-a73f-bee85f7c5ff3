"use client";

import { useEffect } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';

export default function NavigationLogger() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    console.log('🧭 [NavigationLogger] تغيير في التنقل:', {
      pathname,
      searchParams: searchParams.toString(),
      timestamp: new Date().toISOString()
    });
  }, [pathname, searchParams]);

  useEffect(() => {
    // تسجيل أحداث التنقل
    const originalPush = router.push;
    const originalReplace = router.replace;
    const originalBack = router.back;
    const originalForward = router.forward;

    // @ts-ignore
    router.push = (...args) => {
      console.log('🧭 [NavigationLogger] router.push called:', args);
      return originalPush.apply(router, args);
    };

    // @ts-ignore
    router.replace = (...args) => {
      console.log('🧭 [NavigationLogger] router.replace called:', args);
      return originalReplace.apply(router, args);
    };

    // @ts-ignore
    router.back = (...args) => {
      console.log('🧭 [NavigationLogger] router.back called:', args);
      return originalBack.apply(router, args);
    };

    // @ts-ignore
    router.forward = (...args) => {
      console.log('🧭 [NavigationLogger] router.forward called:', args);
      return originalForward.apply(router, args);
    };

    return () => {
      // استعادة الوظائف الأصلية
      router.push = originalPush;
      router.replace = originalReplace;
      router.back = originalBack;
      router.forward = originalForward;
    };
  }, [router]);

  // لا نعرض أي شيء - هذا مكون للتسجيل فقط
  return null;
}
