// src/services/representativeAIApprovalService.ts
import { collection, query, where, getDocs, doc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { RepresentativeDocument } from '@/types/representative';

// أنواع البيانات للموافقة الذكية للمندوبين
export interface RepresentativeDocumentAnalysis {
  documentType: 'driving_license' | 'vehicle_inspection' | 'national_id';
  extractedData: {
    ownerName: string;
    documentNumber: string;
    issueDate: Date;
    expiryDate: Date;
    issuingAuthority: string;
    vehicleInfo?: {
      plateNumber: string;
      model: string;
      year: number;
    };
    licenseClass?: string;
  };
  confidence: number; // 0-100%
  isValid: boolean;
  issues: string[];
  ocrText: string;
}

export interface RepresentativeVerificationResult {
  nameMatch: boolean;
  nameMatchConfidence: number;
  documentsConsistent: boolean;
  documentsValid: boolean;
  duplicateCheck: boolean;
  duplicateDetails?: {
    existingRepresentativeId: string;
    duplicateType: 'name' | 'license' | 'vehicle' | 'national_id';
  };
  riskScore: number; // 0-100
  autoApprovalEligible: boolean;
  reasons: string[];
}

export interface RepresentativeAutoApprovalDecision {
  decision: 'approve' | 'reject' | 'manual_review';
  confidence: number;
  reasons: string[];
  extractedData: Record<string, any>;
  riskFactors: string[];
  recommendations: string[];
}

// قواعد الموافقة التلقائية للمندوبين
const REPRESENTATIVE_AUTO_APPROVAL_RULES = {
  nameMatchThreshold: 85, // تطابق الاسم بنسبة 85%+
  documentValidityThreshold: 90, // صحة المستند 90%+
  maxRiskScore: 30, // نقاط المخاطر أقل من 30
  minConfidenceScore: 80, // ثقة عامة 80%+
  requiredDocuments: ['driving_license', 'vehicle_inspection'],
  maxDocumentAge: 365, // أقصى عمر للمستند بالأيام
  minLicenseValidityDays: 90 // أقل مدة صلاحية مطلوبة للرخصة
};

export class RepresentativeAIApprovalService {
  /**
   * تحليل مستندات المندوب باستخدام الذكاء الاصطناعي
   */
  static async analyzeRepresentativeDocument(
    documentUrl: string, 
    documentType: string
  ): Promise<RepresentativeDocumentAnalysis> {
    try {
      // استدعاء API للذكاء الاصطناعي
      const response = await fetch('/api/ai/analyze-representative-documents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ documentUrl, documentType })
      });

      if (!response.ok) {
        throw new Error('فشل في تحليل مستند المندوب');
      }

      const analysis = await response.json();
      
      // التحقق من صحة البيانات المستخرجة
      return this.validateRepresentativeExtractedData(analysis);
    } catch (error) {
      console.error('خطأ في تحليل مستند المندوب:', error);
      return {
        documentType: documentType as any,
        extractedData: {
          ownerName: '',
          documentNumber: '',
          issueDate: new Date(),
          expiryDate: new Date(),
          issuingAuthority: ''
        },
        confidence: 0,
        isValid: false,
        issues: ['فشل في تحليل المستند'],
        ocrText: ''
      };
    }
  }

  /**
   * التحقق من صحة البيانات المستخرجة للمندوب
   */
  private static validateRepresentativeExtractedData(analysis: any): RepresentativeDocumentAnalysis {
    const issues: string[] = [];
    
    // التحقق من وجود البيانات الأساسية
    if (!analysis.extractedData.ownerName) {
      issues.push('لم يتم العثور على اسم المالك');
    }
    
    if (!analysis.extractedData.documentNumber) {
      issues.push('لم يتم العثور على رقم المستند');
    }
    
    // التحقق من تاريخ الانتهاء
    const expiryDate = new Date(analysis.extractedData.expiryDate);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (expiryDate < today) {
      issues.push('المستند منتهي الصلاحية');
    } else if (daysUntilExpiry < REPRESENTATIVE_AUTO_APPROVAL_RULES.minLicenseValidityDays) {
      issues.push(`المستند ينتهي خلال ${daysUntilExpiry} يوم`);
    }
    
    // التحقق من عمر المستند
    const issueDate = new Date(analysis.extractedData.issueDate);
    const documentAge = Math.ceil((today.getTime() - issueDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (documentAge > REPRESENTATIVE_AUTO_APPROVAL_RULES.maxDocumentAge * 5) { // 5 سنوات
      issues.push('المستند قديم جداً');
    }
    
    return {
      ...analysis,
      issues,
      isValid: issues.length === 0 && analysis.confidence > 70
    };
  }

  /**
   * التحقق من بيانات المندوب والتطابق
   */
  static async verifyRepresentativeData(
    representativeData: RepresentativeDocument,
    documentAnalyses: RepresentativeDocumentAnalysis[]
  ): Promise<RepresentativeVerificationResult> {
    const result: RepresentativeVerificationResult = {
      nameMatch: false,
      nameMatchConfidence: 0,
      documentsConsistent: false,
      documentsValid: false,
      duplicateCheck: true,
      riskScore: 0,
      autoApprovalEligible: false,
      reasons: []
    };

    try {
      // 1. التحقق من تطابق الأسماء
      const nameVerification = this.verifyRepresentativeNameMatch(representativeData, documentAnalyses);
      result.nameMatch = nameVerification.match;
      result.nameMatchConfidence = nameVerification.confidence;

      // 2. التحقق من صحة المستندات
      result.documentsValid = documentAnalyses.every(doc => doc.isValid);

      // 3. التحقق من تناسق المستندات
      result.documentsConsistent = this.verifyRepresentativeDocumentConsistency(documentAnalyses);

      // 4. فحص التكرار
      const duplicateCheck = await this.checkRepresentativeDuplicates(representativeData, documentAnalyses);
      result.duplicateCheck = !duplicateCheck.hasDuplicates;
      result.duplicateDetails = duplicateCheck.details;

      // 5. حساب نقاط المخاطر
      result.riskScore = this.calculateRepresentativeRiskScore(representativeData, documentAnalyses, duplicateCheck);

      // 6. تحديد الأسباب
      result.reasons = this.generateRepresentativeReasons(result, documentAnalyses);

      // 7. تحديد الأهلية للموافقة التلقائية
      result.autoApprovalEligible = this.isRepresentativeEligibleForAutoApproval(result, documentAnalyses);

      return result;
    } catch (error) {
      console.error('خطأ في التحقق من بيانات المندوب:', error);
      result.reasons.push('خطأ في عملية التحقق');
      result.riskScore = 100;
      return result;
    }
  }

  /**
   * التحقق من تطابق أسماء المندوب
   */
  private static verifyRepresentativeNameMatch(
    representativeData: RepresentativeDocument, 
    documentAnalyses: RepresentativeDocumentAnalysis[]
  ): { match: boolean; confidence: number } {
    const representativeName = representativeData.displayName?.toLowerCase().trim() || '';
    let bestMatch = 0;

    for (const analysis of documentAnalyses) {
      const docName = analysis.extractedData.ownerName?.toLowerCase().trim() || '';
      const similarity = this.calculateStringSimilarity(representativeName, docName);
      bestMatch = Math.max(bestMatch, similarity);
    }

    return {
      match: bestMatch >= REPRESENTATIVE_AUTO_APPROVAL_RULES.nameMatchThreshold,
      confidence: bestMatch
    };
  }

  /**
   * حساب تشابه النصوص (نفس الخوارزمية المستخدمة للتجار)
   */
  private static calculateStringSimilarity(str1: string, str2: string): number {
    // خوارزمية Levenshtein Distance مبسطة
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    const maxLength = Math.max(str1.length, str2.length);
    return maxLength === 0 ? 100 : ((maxLength - matrix[str2.length][str1.length]) / maxLength) * 100;
  }

  /**
   * التحقق من تناسق مستندات المندوب
   */
  private static verifyRepresentativeDocumentConsistency(documentAnalyses: RepresentativeDocumentAnalysis[]): boolean {
    if (documentAnalyses.length < 2) return true;

    const firstDoc = documentAnalyses[0];
    return documentAnalyses.every(doc => 
      doc.extractedData.ownerName === firstDoc.extractedData.ownerName
    );
  }

  /**
   * فحص التكرار للمندوبين
   */
  private static async checkRepresentativeDuplicates(
    representativeData: RepresentativeDocument,
    documentAnalyses: RepresentativeDocumentAnalysis[]
  ): Promise<{ hasDuplicates: boolean; details?: any }> {
    try {
      // فحص تكرار البريد الإلكتروني
      const emailQuery = query(
        collection(db, 'representatives'),
        where('email', '==', representativeData.email)
      );
      const emailDocs = await getDocs(emailQuery);
      
      if (emailDocs.size > 1) {
        return {
          hasDuplicates: true,
          details: { duplicateType: 'email', existingRepresentativeId: emailDocs.docs[0].id }
        };
      }

      // فحص تكرار رقم الهوية
      const nationalIdQuery = query(
        collection(db, 'representatives'),
        where('nationalId', '==', representativeData.nationalId)
      );
      const nationalIdDocs = await getDocs(nationalIdQuery);
      
      if (nationalIdDocs.size > 0) {
        return {
          hasDuplicates: true,
          details: { duplicateType: 'national_id', existingRepresentativeId: nationalIdDocs.docs[0].id }
        };
      }

      // فحص تكرار رخصة القيادة
      const licenseAnalysis = documentAnalyses.find(doc => doc.documentType === 'driving_license');
      if (licenseAnalysis?.extractedData.documentNumber) {
        const licenseQuery = query(
          collection(db, 'representatives'),
          where('drivingLicense.number', '==', licenseAnalysis.extractedData.documentNumber)
        );
        const licenseDocs = await getDocs(licenseQuery);
        
        if (licenseDocs.size > 0) {
          return {
            hasDuplicates: true,
            details: { duplicateType: 'license', existingRepresentativeId: licenseDocs.docs[0].id }
          };
        }
      }

      return { hasDuplicates: false };
    } catch (error) {
      console.error('خطأ في فحص تكرار المندوب:', error);
      return { hasDuplicates: true }; // في حالة الخطأ، نفترض وجود تكرار للأمان
    }
  }

  /**
   * حساب نقاط المخاطر للمندوب
   */
  private static calculateRepresentativeRiskScore(
    representativeData: RepresentativeDocument,
    documentAnalyses: RepresentativeDocumentAnalysis[],
    duplicateCheck: any
  ): number {
    let riskScore = 0;

    // مخاطر المستندات
    documentAnalyses.forEach(analysis => {
      if (analysis.confidence < 80) riskScore += 25;
      if (!analysis.isValid) riskScore += 35;
      if (analysis.issues.length > 0) riskScore += analysis.issues.length * 8;

      // مخاطر خاصة بالمندوبين
      if (analysis.documentType === 'driving_license') {
        const expiryDate = new Date(analysis.extractedData.expiryDate);
        const today = new Date();
        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        if (daysUntilExpiry < 30) riskScore += 20; // رخصة تنتهي قريباً
        if (daysUntilExpiry < 7) riskScore += 30; // رخصة تنتهي خلال أسبوع
      }
    });

    // مخاطر التكرار
    if (duplicateCheck.hasDuplicates) riskScore += 60;

    // مخاطر البيانات الناقصة
    if (!representativeData.displayName) riskScore += 15;
    if (!representativeData.email) riskScore += 20;
    if (!representativeData.phoneNumber) riskScore += 15;
    if (!representativeData.nationalId) riskScore += 25;

    // مخاطر المركبة
    if (!representativeData.vehicle?.plateNumber) riskScore += 10;
    if (!representativeData.vehicle?.model) riskScore += 5;

    return Math.min(riskScore, 100);
  }

  /**
   * توليد أسباب القرار للمندوب
   */
  private static generateRepresentativeReasons(
    result: RepresentativeVerificationResult,
    documentAnalyses: RepresentativeDocumentAnalysis[]
  ): string[] {
    const reasons: string[] = [];

    if (!result.nameMatch) {
      reasons.push(`عدم تطابق الاسم (${result.nameMatchConfidence.toFixed(1)}%)`);
    }

    if (!result.documentsConsistent) {
      reasons.push('عدم تناسق البيانات بين المستندات');
    }

    if (!result.documentsValid) {
      reasons.push('مشاكل في صحة المستندات');
    }

    if (!result.duplicateCheck) {
      reasons.push('وجود حساب مكرر في النظام');
    }

    documentAnalyses.forEach((analysis, index) => {
      if (!analysis.isValid) {
        const docTypeArabic = this.getDocumentTypeArabic(analysis.documentType);
        reasons.push(`مشكلة في ${docTypeArabic}: ${analysis.issues.join(', ')}`);
      }
    });

    if (result.riskScore > 50) {
      reasons.push(`نقاط مخاطر عالية (${result.riskScore}/100)`);
    }

    return reasons;
  }

  /**
   * ترجمة نوع المستند للعربية
   */
  private static getDocumentTypeArabic(documentType: string): string {
    const translations = {
      'driving_license': 'رخصة القيادة',
      'vehicle_inspection': 'شهادة الفحص الدوري',
      'national_id': 'الهوية الوطنية'
    };
    return translations[documentType as keyof typeof translations] || documentType;
  }

  /**
   * تحديد الأهلية للموافقة التلقائية للمندوب
   */
  private static isRepresentativeEligibleForAutoApproval(
    result: RepresentativeVerificationResult,
    documentAnalyses: RepresentativeDocumentAnalysis[]
  ): boolean {
    return (
      result.nameMatch &&
      result.nameMatchConfidence >= REPRESENTATIVE_AUTO_APPROVAL_RULES.nameMatchThreshold &&
      result.documentsConsistent &&
      result.documentsValid &&
      result.duplicateCheck &&
      result.riskScore <= REPRESENTATIVE_AUTO_APPROVAL_RULES.maxRiskScore &&
      documentAnalyses.every(doc =>
        doc.isValid && doc.confidence >= REPRESENTATIVE_AUTO_APPROVAL_RULES.documentValidityThreshold
      ) &&
      documentAnalyses.some(doc => doc.documentType === 'driving_license') // يجب وجود رخصة قيادة
    );
  }

  /**
   * اتخاذ قرار الموافقة التلقائية للمندوب
   */
  static async makeRepresentativeAutoApprovalDecision(
    representativeUid: string,
    representativeData: RepresentativeDocument
  ): Promise<RepresentativeAutoApprovalDecision> {
    try {
      // تحليل المستندات
      const documentAnalyses: RepresentativeDocumentAnalysis[] = [];

      // تحليل رخصة القيادة
      if (representativeData.drivingLicense?.imageURL) {
        const analysis = await this.analyzeRepresentativeDocument(
          representativeData.drivingLicense.imageURL,
          'driving_license'
        );
        documentAnalyses.push(analysis);
      }

      // تحليل شهادة الفحص الدوري
      if (representativeData.vehicleInspection?.imageURL) {
        const analysis = await this.analyzeRepresentativeDocument(
          representativeData.vehicleInspection.imageURL,
          'vehicle_inspection'
        );
        documentAnalyses.push(analysis);
      }

      // التحقق من البيانات
      const verification = await this.verifyRepresentativeData(representativeData, documentAnalyses);

      // اتخاذ القرار
      let decision: 'approve' | 'reject' | 'manual_review' = 'manual_review';
      let confidence = 0;
      const reasons: string[] = [];
      const riskFactors: string[] = [];
      const recommendations: string[] = [];

      if (verification.autoApprovalEligible) {
        decision = 'approve';
        confidence = Math.min(verification.nameMatchConfidence,
          Math.min(...documentAnalyses.map(d => d.confidence)));
        reasons.push('جميع المعايير مستوفاة للموافقة التلقائية');
        reasons.push(`تطابق الاسم: ${verification.nameMatchConfidence.toFixed(1)}%`);
        reasons.push('جميع المستندات صحيحة وسارية');
        reasons.push('لا توجد حسابات مكررة');
      } else if (verification.riskScore > 75) {
        decision = 'reject';
        confidence = 85;
        reasons.push('نقاط مخاطر عالية جداً');
        reasons.push(...verification.reasons);
      } else {
        decision = 'manual_review';
        confidence = 65;
        reasons.push('يتطلب مراجعة يدوية');
        reasons.push(...verification.reasons);
      }

      // تحديد عوامل المخاطر
      if (verification.riskScore > 40) {
        riskFactors.push(`نقاط مخاطر: ${verification.riskScore}/100`);
      }
      if (!verification.duplicateCheck) {
        riskFactors.push('حساب مكرر محتمل');
      }
      if (!verification.documentsValid) {
        riskFactors.push('مشاكل في صحة المستندات');
      }

      // التوصيات
      if (decision === 'manual_review') {
        recommendations.push('مراجعة المستندات يدوياً');
        recommendations.push('التحقق من هوية المندوب');
        if (verification.nameMatchConfidence < 90) {
          recommendations.push('التأكد من تطابق الأسماء');
        }
        if (verification.riskScore > 50) {
          recommendations.push('فحص إضافي للمخاطر');
        }
      } else if (decision === 'reject') {
        recommendations.push('رفض الطلب مع توضيح الأسباب');
        recommendations.push('إشعار المتقدم بالمتطلبات');
      }

      return {
        decision,
        confidence,
        reasons,
        extractedData: documentAnalyses.reduce((acc, doc) => ({
          ...acc,
          [doc.documentType]: doc.extractedData
        }), {}),
        riskFactors,
        recommendations
      };

    } catch (error) {
      console.error('خطأ في اتخاذ قرار موافقة المندوب:', error);
      return {
        decision: 'manual_review',
        confidence: 0,
        reasons: ['خطأ في النظام'],
        extractedData: {},
        riskFactors: ['خطأ تقني'],
        recommendations: ['مراجعة يدوية مطلوبة']
      };
    }
  }

  /**
   * تطبيق قرار الموافقة التلقائية للمندوب
   */
  static async applyRepresentativeAutoApprovalDecision(
    representativeUid: string,
    decision: RepresentativeAutoApprovalDecision
  ): Promise<boolean> {
    try {
      const representativeDocRef = doc(db, 'representatives', representativeUid);

      const updateData: any = {
        aiAnalysis: {
          decision: decision.decision,
          confidence: decision.confidence,
          reasons: decision.reasons,
          extractedData: decision.extractedData,
          riskFactors: decision.riskFactors,
          processedAt: serverTimestamp(),
          version: '1.0'
        },
        updatedAt: serverTimestamp()
      };

      if (decision.decision === 'approve') {
        updateData.approvalStatus = 'approved';
        updateData.isActive = true;
        updateData.approvalDate = serverTimestamp();
        updateData.approvalNotes = `موافقة تلقائية بالذكاء الاصطناعي (ثقة: ${decision.confidence}%)`;
        updateData.reviewedBy = 'ai-system';
      } else if (decision.decision === 'reject') {
        updateData.approvalStatus = 'rejected';
        updateData.isActive = false;
        updateData.approvalDate = serverTimestamp();
        updateData.approvalNotes = `رفض تلقائي: ${decision.reasons.join(', ')}`;
        updateData.reviewedBy = 'ai-system';
      }
      // في حالة manual_review، نترك الحالة pending

      await updateDoc(representativeDocRef, updateData);
      return true;
    } catch (error) {
      console.error('خطأ في تطبيق قرار موافقة المندوب:', error);
      return false;
    }
  }
}
