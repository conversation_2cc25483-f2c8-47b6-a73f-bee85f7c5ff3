{"version": "2.0.0", "description": "تكوين النماذج المحلية المتقدمة للذكاء الاصطناعي - خصوصية 100%", "privacy": {"mode": "strict", "localProcessingOnly": true, "noExternalRequests": true, "dataEncryption": true, "auditLogging": true}, "models": {"essential": {"tesseract_arabic": {"name": "Tesseract OCR Arabic Enhanced", "path": "./models/ocr/tesseract-ara.traineddata", "compressedPath": "./models/ocr/tesseract-ara.traineddata.br", "originalSize": "55MB", "compressedSize": "16MB", "type": "ocr", "language": ["ara", "eng"], "priority": "essential", "loadOnStartup": true, "capabilities": ["text_extraction", "arabic_text", "english_text"], "accuracy": "92%", "processingTime": "2-4s"}, "document_classifier": {"name": "Document Type Classifier", "path": "./models/classification/document-classifier.onnx", "compressedPath": "./models/classification/document-classifier.onnx.br", "originalSize": "22MB", "compressedSize": "7MB", "type": "classification", "language": ["ara", "eng"], "priority": "essential", "loadOnStartup": true, "capabilities": ["document_classification", "commercial_registration", "freelance_document", "driving_license"], "accuracy": "94%", "processingTime": "0.5-1s"}, "format_validator": {"name": "Document Format Validator", "path": "./models/validation/format-validator.onnx", "compressedPath": "./models/validation/format-validator.onnx.br", "originalSize": "8MB", "compressedSize": "2MB", "type": "validation", "language": ["ara", "eng"], "priority": "essential", "loadOnStartup": true, "capabilities": ["format_validation", "structure_check", "required_fields"], "accuracy": "96%", "processingTime": "0.2-0.5s"}}, "advanced": {"arabic_ner": {"name": "Arabic Named Entity Recognition", "path": "./models/nlp/arabic-ner.onnx", "compressedPath": "./models/nlp/arabic-ner.onnx.br", "originalSize": "25MB", "compressedSize": "8MB", "type": "nlp", "language": ["ara"], "priority": "advanced", "loadOnDemand": true, "capabilities": ["entity_extraction", "person_names", "organizations", "locations", "dates"], "accuracy": "89%", "processingTime": "1-2s"}, "fraud_detector": {"name": "Document Fraud <PERSON>ector", "path": "./models/classification/fraud-detector.onnx", "compressedPath": "./models/classification/fraud-detector.onnx.br", "originalSize": "28MB", "compressedSize": "9MB", "type": "fraud_detection", "language": ["ara", "eng"], "priority": "advanced", "loadOnDemand": true, "capabilities": ["fraud_detection", "authenticity_check", "pattern_analysis"], "accuracy": "91%", "processingTime": "1-3s"}, "authenticity_verifier": {"name": "Document Authenticity Verifier", "path": "./models/validation/authenticity-verifier.onnx", "compressedPath": "./models/validation/authenticity-verifier.onnx.br", "originalSize": "20MB", "compressedSize": "6MB", "type": "verification", "language": ["ara", "eng"], "priority": "advanced", "loadOnDemand": true, "capabilities": ["authenticity_verification", "signature_check", "seal_validation"], "accuracy": "88%", "processingTime": "2-4s"}, "paddleocr_arabic": {"name": "PaddleOCR Arabic Advanced", "path": "./models/ocr/paddleocr-ara.onnx", "compressedPath": "./models/ocr/paddleocr-ara.onnx.br", "originalSize": "12MB", "compressedSize": "4MB", "type": "ocr", "language": ["ara"], "priority": "advanced", "loadOnDemand": true, "capabilities": ["advanced_ocr", "handwritten_text", "complex_layouts"], "accuracy": "94%", "processingTime": "3-5s"}}, "specialized": {"arabic_sentiment": {"name": "Arabic Sentiment Analysis", "path": "./models/nlp/arabic-sentiment.onnx", "compressedPath": "./models/nlp/arabic-sentiment.onnx.br", "originalSize": "15MB", "compressedSize": "5MB", "type": "sentiment", "language": ["ara"], "priority": "specialized", "loadOnDemand": true, "capabilities": ["sentiment_analysis", "emotion_detection"], "accuracy": "87%", "processingTime": "0.5-1s"}, "quality_assessor": {"name": "Document Quality Assessor", "path": "./models/classification/quality-assessor.onnx", "compressedPath": "./models/classification/quality-assessor.onnx.br", "originalSize": "12MB", "compressedSize": "4MB", "type": "quality_assessment", "language": ["ara", "eng"], "priority": "specialized", "loadOnDemand": true, "capabilities": ["quality_assessment", "image_clarity", "text_readability"], "accuracy": "90%", "processingTime": "1-2s"}, "consistency_checker": {"name": "Data Consistency Checker", "path": "./models/validation/consistency-checker.onnx", "compressedPath": "./models/validation/consistency-checker.onnx.br", "originalSize": "15MB", "compressedSize": "5MB", "type": "consistency", "language": ["ara", "eng"], "priority": "specialized", "loadOnDemand": true, "capabilities": ["consistency_check", "cross_validation", "data_matching"], "accuracy": "93%", "processingTime": "1-2s"}, "text_similarity": {"name": "Text Similarity Calculator", "path": "./models/nlp/text-similarity.onnx", "compressedPath": "./models/nlp/text-similarity.onnx.br", "originalSize": "18MB", "compressedSize": "6MB", "type": "similarity", "language": ["ara", "eng"], "priority": "specialized", "loadOnDemand": true, "capabilities": ["text_similarity", "semantic_matching", "duplicate_detection"], "accuracy": "91%", "processingTime": "0.5-1s"}, "easyocr_arabic": {"name": "EasyOCR Arabic Lightweight", "path": "./models/ocr/easyocr-ara.pth", "compressedPath": "./models/ocr/easyocr-ara.pth.br", "originalSize": "8MB", "compressedSize": "3MB", "type": "ocr", "language": ["ara"], "priority": "specialized", "loadOnDemand": true, "capabilities": ["fast_ocr", "lightweight_processing"], "accuracy": "85%", "processingTime": "1-2s"}}}, "engines": {"onnx_runtime": {"enabled": true, "path": "./engines/onnx-runtime/", "version": "1.17.0", "capabilities": ["cpu", "webgl", "wasm"], "memoryLimit": "512MB", "concurrentModels": 3}, "tensorflow_js": {"enabled": true, "path": "./engines/tensorflow-js/", "version": "4.15.0", "capabilities": ["cpu", "webgl"], "memoryLimit": "256MB", "concurrentModels": 2}, "tesseract_js": {"enabled": true, "path": "./engines/tesseract-js/", "version": "5.1.1", "capabilities": ["ocr", "arabic", "english"], "memoryLimit": "128MB", "concurrentModels": 1}}, "performance": {"maxConcurrentAnalysis": 3, "analysisTimeout": 30000, "cacheEnabled": true, "cacheTTL": 3600000, "memoryThreshold": 0.85, "autoCleanup": true}, "download": {"maxConcurrentDownloads": 3, "retryAttempts": 5, "retryDelay": 2000, "compressionEnabled": true, "progressTracking": true, "cdnUrls": ["https://cdn.mikhla.com/ai-models/", "https://backup-cdn.mikhla.com/ai-models/", "https://github.com/mikhla/ai-models/releases/latest/"]}, "security": {"encryptionEnabled": true, "encryptionAlgorithm": "AES-256-GCM", "auditLogging": true, "networkMonitoring": true, "memoryClearing": true, "dataRetention": "session_only"}, "compliance": {"gdpr": true, "saudiDataProtection": true, "ccpa": true, "localProcessingOnly": true, "noExternalTransfer": true, "dataMinimization": true}}