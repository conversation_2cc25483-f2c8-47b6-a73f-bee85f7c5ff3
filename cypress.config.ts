import { defineConfig } from 'cypress'

export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:9002',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: true,
    screenshotOnRunFailure: true,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    setupNodeEvents(on, config) {
      // implement node event listeners here
    },
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.ts',
  },
  component: {
    devServer: {
      framework: 'next',
      bundler: 'webpack',
    },
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/component.ts',
  },
  env: {
    // متغيرات البيئة للاختبار - استخدام قيم تجريبية آمنة
    NEXT_PUBLIC_FIREBASE_API_KEY: 'test-api-key-for-cypress',
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: 'mikhla-test.firebaseapp.com',
    NEXT_PUBLIC_FIREBASE_PROJECT_ID: 'mikhla-test',
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: '123456789',
    NEXT_PUBLIC_FIREBASE_APP_ID: '1:123456789:web:test',
    // متغيرات إضافية للاختبار
    CYPRESS_TEST_MODE: 'true',
    NODE_ENV: 'test',
    // إعدادات اختبارات التحليلات
    ANALYTICS_TEST_MODE: 'true',
    MOCK_ANALYTICS_DATA: 'true',
    PERFORMANCE_TEST_THRESHOLD: '3000', // 3 ثوان
    LARGE_DATASET_SIZE: '1000',
    MEMORY_THRESHOLD: '52428800', // 50 MB
  },
  retries: {
    runMode: 2,
    openMode: 0,
  },
  experimentalStudio: true,
})
