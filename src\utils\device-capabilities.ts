/**
 * 🔍 نظام اكتشاف قدرات الجهاز
 * يحدد ما إذا كان الجهاز قادر على تشغيل نماذج AI محلياً
 */

export interface DeviceCapabilities {
  canRunLocalAI: boolean;
  recommendedMode: 'local' | 'cloud' | 'hybrid';
  ram: number;
  cores: number;
  isLowEnd: boolean;
  batteryLevel?: number;
  connectionSpeed: 'slow' | 'medium' | 'fast';
}

/**
 * اكتشاف قدرات الجهاز
 */
export async function detectDeviceCapabilities(): Promise<DeviceCapabilities> {
  const capabilities: DeviceCapabilities = {
    canRunLocalAI: true,
    recommendedMode: 'local',
    ram: 0,
    cores: 0,
    isLowEnd: false,
    connectionSpeed: 'medium'
  };

  try {
    // اكتشاف عدد المعالجات
    capabilities.cores = navigator.hardwareConcurrency || 2;

    // تقدير الذاكرة (تقريبي)
    if ('deviceMemory' in navigator) {
      capabilities.ram = (navigator as any).deviceMemory * 1024; // MB
    } else {
      // تقدير بناءً على User Agent
      capabilities.ram = estimateRAMFromUserAgent();
    }

    // اكتشاف مستوى البطارية
    if ('getBattery' in navigator) {
      const battery = await (navigator as any).getBattery();
      capabilities.batteryLevel = Math.round(battery.level * 100);
    }

    // اكتشاف سرعة الاتصال
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      capabilities.connectionSpeed = getConnectionSpeed(connection);
    }

    // تحديد ما إذا كان الجهاز ضعيف
    capabilities.isLowEnd = isLowEndDevice(capabilities);

    // تحديد الوضع المُوصى به
    capabilities.recommendedMode = getRecommendedMode(capabilities);
    capabilities.canRunLocalAI = capabilities.recommendedMode !== 'cloud';

  } catch (error) {
    console.warn('خطأ في اكتشاف قدرات الجهاز:', error);
    // افتراض جهاز متوسط القدرات
    capabilities.ram = 4096;
    capabilities.cores = 4;
    capabilities.recommendedMode = 'hybrid';
  }

  return capabilities;
}

/**
 * تقدير الذاكرة من User Agent
 */
function estimateRAMFromUserAgent(): number {
  const userAgent = navigator.userAgent.toLowerCase();
  
  // هواتف ذكية
  if (/mobile|android|iphone|ipad/.test(userAgent)) {
    if (/iphone.*os 1[5-9]|android.*1[1-9]/.test(userAgent)) {
      return 6144; // 6GB للهواتف الحديثة
    } else if (/iphone.*os 1[2-4]|android.*[8-10]/.test(userAgent)) {
      return 4096; // 4GB للهواتف المتوسطة
    } else {
      return 2048; // 2GB للهواتف القديمة
    }
  }
  
  // أجهزة كمبيوتر
  return 8192; // 8GB افتراضي للكمبيوتر
}

/**
 * تحديد سرعة الاتصال
 */
function getConnectionSpeed(connection: any): 'slow' | 'medium' | 'fast' {
  if (!connection) return 'medium';
  
  const effectiveType = connection.effectiveType;
  const downlink = connection.downlink;
  
  if (effectiveType === '4g' && downlink > 10) return 'fast';
  if (effectiveType === '4g' || (effectiveType === '3g' && downlink > 1)) return 'medium';
  return 'slow';
}

/**
 * تحديد ما إذا كان الجهاز ضعيف
 */
function isLowEndDevice(capabilities: DeviceCapabilities): boolean {
  return (
    capabilities.ram < 3072 || // أقل من 3GB
    capabilities.cores < 4 ||   // أقل من 4 معالجات
    capabilities.batteryLevel !== undefined && capabilities.batteryLevel < 20 // بطارية منخفضة
  );
}

/**
 * تحديد الوضع المُوصى به
 */
function getRecommendedMode(capabilities: DeviceCapabilities): 'local' | 'cloud' | 'hybrid' {
  // أجهزة قوية - معالجة محلية
  if (capabilities.ram >= 6144 && capabilities.cores >= 6) {
    return 'local';
  }
  
  // أجهزة ضعيفة - معالجة سحابية
  if (capabilities.isLowEnd || capabilities.connectionSpeed === 'slow') {
    return 'cloud';
  }
  
  // أجهزة متوسطة - نظام هجين
  return 'hybrid';
}

/**
 * اختبار أداء الجهاز
 */
export async function benchmarkDevice(): Promise<number> {
  return new Promise((resolve) => {
    const start = performance.now();
    
    // اختبار بسيط للأداء
    let result = 0;
    for (let i = 0; i < 1000000; i++) {
      result += Math.sqrt(i);
    }
    
    const end = performance.now();
    const score = Math.round(1000 / (end - start)); // نقاط الأداء
    
    resolve(score);
  });
}
