/// <reference types="cypress" />

/**
 * اختبارات نظام التكامل ERP/POS
 * تختبر جميع وظائف التكامل مع أنظمة ERP و POS
 */

describe('🔌 نظام التكامل ERP/POS', () => {
  beforeEach(() => {
    // إعداد البيانات الوهمية
    cy.mockLogin('merchant')
    cy.mockFirebaseAuth()
    cy.mockERPIntegration()
    
    // زيارة صفحة التكاملات
    cy.visitWithLocale('/merchant/integrations')
    cy.waitForLoadingToFinish()
  })

  afterEach(() => {
    cy.mockLogout()
  })

  describe('📋 صفحة التكاملات الرئيسية', () => {
    it('يجب أن تعرض صفحة التكاملات بشكل صحيح', () => {
      // التحقق من وجود العناصر الأساسية
      cy.get('[data-testid="integrations-page"]').should('be.visible')
      cy.shouldContainArabicText('التكاملات')
      cy.shouldContainArabicText('أنظمة ERP')
      cy.shouldContainArabicText('أنظمة POS')
      
      // التحقق من وجود أزرار الإجراءات
      cy.get('[data-testid="add-erp-integration"]').should('be.visible')
      cy.get('[data-testid="add-pos-integration"]').should('be.visible')
    })

    it('يجب أن تعرض قائمة التكاملات الحالية', () => {
      // التحقق من وجود جدول التكاملات
      cy.get('[data-testid="integrations-table"]').should('be.visible')
      
      // التحقق من وجود أعمدة الجدول
      cy.shouldContainArabicText('نوع النظام')
      cy.shouldContainArabicText('اسم النظام')
      cy.shouldContainArabicText('الحالة')
      cy.shouldContainArabicText('آخر مزامنة')
      cy.shouldContainArabicText('الإجراءات')
    })
  })

  describe('🔧 إضافة تكامل ERP جديد', () => {
    it('يجب أن يفتح نموذج إضافة تكامل ERP', () => {
      cy.get('[data-testid="add-erp-integration"]').click()
      
      // التحقق من فتح النموذج
      cy.get('[data-testid="erp-integration-modal"]').should('be.visible')
      cy.shouldContainArabicText('إضافة تكامل ERP جديد')
      
      // التحقق من وجود الحقول المطلوبة
      cy.get('[data-testid="system-type"]').should('be.visible')
      cy.get('[data-testid="system-name"]').should('be.visible')
      cy.get('[data-testid="api-url"]').should('be.visible')
      cy.get('[data-testid="api-key"]').should('be.visible')
    })

    it('يجب أن ينشئ تكامل ERP جديد بنجاح', () => {
      cy.get('[data-testid="add-erp-integration"]').click()
      
      // ملء النموذج
      cy.fillForm({
        'system-type': 'sap',
        'system-name': 'SAP Production System',
        'api-url': 'https://sap-api.company.com',
        'api-key': 'test-api-key-12345',
        'username': 'sap-user',
        'database': 'PROD_DB'
      })
      
      // تفعيل إعدادات المزامنة
      cy.get('[data-testid="sync-products"]').check()
      cy.get('[data-testid="sync-inventory"]').check()
      cy.get('[data-testid="sync-orders"]').check()
      cy.get('[data-testid="sync-customers"]').check()
      
      // حفظ التكامل
      cy.get('[data-testid="save-integration"]').click()
      
      // التحقق من نجاح الإنشاء
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إنشاء التكامل بنجاح')
    })

    it('يجب أن يختبر الاتصال مع نظام ERP', () => {
      cy.get('[data-testid="add-erp-integration"]').click()
      
      // ملء بيانات الاتصال
      cy.fillForm({
        'system-type': 'sap',
        'api-url': 'https://test-sap.example.com',
        'api-key': 'test-key'
      })
      
      // اختبار الاتصال
      cy.get('[data-testid="test-connection"]').click()
      
      // انتظار نتيجة الاختبار
      cy.get('[data-testid="connection-status"]', { timeout: 15000 })
        .should('be.visible')
        .and('contain.text', 'نجح الاتصال')
    })
  })

  describe('🔧 إضافة تكامل POS جديد', () => {
    it('يجب أن يفتح نموذج إضافة تكامل POS', () => {
      cy.get('[data-testid="add-pos-integration"]').click()
      
      // التحقق من فتح النموذج
      cy.get('[data-testid="pos-integration-modal"]').should('be.visible')
      cy.shouldContainArabicText('إضافة تكامل POS جديد')
      
      // التحقق من أنواع أنظمة POS المتاحة
      cy.get('[data-testid="pos-system-type"]').click()
      cy.shouldContainArabicText('Square')
      cy.shouldContainArabicText('Shopify POS')
      cy.shouldContainArabicText('Lightspeed')
      cy.shouldContainArabicText('Toast')
      cy.shouldContainArabicText('Clover')
    })

    it('يجب أن ينشئ تكامل POS جديد بنجاح', () => {
      cy.get('[data-testid="add-pos-integration"]').click()
      
      // ملء النموذج
      cy.get('[data-testid="pos-system-type"]').select('square')
      cy.fillForm({
        'system-name': 'Square Main Terminal',
        'api-url': 'https://connect.squareup.com',
        'api-key': 'square-api-key-12345',
        'location-id': 'location-123'
      })
      
      // تفعيل إعدادات المزامنة
      cy.get('[data-testid="sync-sales"]').check()
      cy.get('[data-testid="sync-payments"]').check()
      cy.get('[data-testid="sync-inventory"]').check()
      
      // حفظ التكامل
      cy.get('[data-testid="save-pos-integration"]').click()
      
      // التحقق من نجاح الإنشاء
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إنشاء تكامل POS بنجاح')
    })
  })

  describe('🔄 عمليات المزامنة', () => {
    it('يجب أن يبدأ مزامنة يدوية للمنتجات', () => {
      // البحث عن تكامل موجود
      cy.get('[data-testid="integration-row"]').first().within(() => {
        cy.get('[data-testid="sync-products-btn"]').click()
      })
      
      // التحقق من بدء المزامنة
      cy.get('[data-testid="sync-status"]').should('contain.text', 'جاري المزامنة')
      
      // انتظار انتهاء المزامنة
      cy.get('[data-testid="sync-status"]', { timeout: 30000 })
        .should('contain.text', 'تمت المزامنة بنجاح')
    })

    it('يجب أن يعرض سجل المزامنة', () => {
      // فتح سجل المزامنة
      cy.get('[data-testid="integration-row"]').first().within(() => {
        cy.get('[data-testid="view-sync-log"]').click()
      })
      
      // التحقق من فتح نافذة السجل
      cy.get('[data-testid="sync-log-modal"]').should('be.visible')
      cy.shouldContainArabicText('سجل المزامنة')
      
      // التحقق من وجود بيانات السجل
      cy.get('[data-testid="sync-log-entries"]').should('be.visible')
      cy.get('[data-testid="log-entry"]').should('have.length.at.least', 1)
    })

    it('يجب أن يعرض إحصائيات المزامنة', () => {
      // فتح إحصائيات التكامل
      cy.get('[data-testid="integration-row"]').first().within(() => {
        cy.get('[data-testid="view-stats"]').click()
      })
      
      // التحقق من فتح نافذة الإحصائيات
      cy.get('[data-testid="integration-stats-modal"]').should('be.visible')
      cy.shouldContainArabicText('إحصائيات التكامل')
      
      // التحقق من وجود المقاييس
      cy.get('[data-testid="total-synced-products"]').should('be.visible')
      cy.get('[data-testid="total-synced-orders"]').should('be.visible')
      cy.get('[data-testid="last-sync-time"]').should('be.visible')
      cy.get('[data-testid="sync-success-rate"]').should('be.visible')
    })
  })

  describe('⚙️ إدارة التكاملات', () => {
    it('يجب أن يحرر تكامل موجود', () => {
      // فتح نموذج التحرير
      cy.get('[data-testid="integration-row"]').first().within(() => {
        cy.get('[data-testid="edit-integration"]').click()
      })
      
      // التحقق من فتح نموذج التحرير
      cy.get('[data-testid="edit-integration-modal"]').should('be.visible')
      cy.shouldContainArabicText('تحرير التكامل')
      
      // تعديل اسم النظام
      cy.get('[data-testid="system-name"]').clear().type('SAP Updated System')
      
      // حفظ التغييرات
      cy.get('[data-testid="save-changes"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث التكامل بنجاح')
    })

    it('يجب أن يوقف/يفعل تكامل', () => {
      // إيقاف التكامل
      cy.get('[data-testid="integration-row"]').first().within(() => {
        cy.get('[data-testid="toggle-integration"]').click()
      })
      
      // تأكيد الإيقاف
      cy.get('[data-testid="confirm-toggle"]').click()
      
      // التحقق من تغيير الحالة
      cy.get('[data-testid="integration-status"]').should('contain.text', 'معطل')
      
      // إعادة تفعيل التكامل
      cy.get('[data-testid="integration-row"]').first().within(() => {
        cy.get('[data-testid="toggle-integration"]').click()
      })
      
      // تأكيد التفعيل
      cy.get('[data-testid="confirm-toggle"]').click()
      
      // التحقق من تغيير الحالة
      cy.get('[data-testid="integration-status"]').should('contain.text', 'نشط')
    })

    it('يجب أن يحذف تكامل', () => {
      // حذف التكامل
      cy.get('[data-testid="integration-row"]').first().within(() => {
        cy.get('[data-testid="delete-integration"]').click()
      })
      
      // تأكيد الحذف
      cy.get('[data-testid="confirm-delete"]').should('be.visible')
      cy.shouldContainArabicText('هل أنت متأكد من حذف هذا التكامل؟')
      cy.get('[data-testid="confirm-delete-btn"]').click()
      
      // التحقق من نجاح الحذف
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم حذف التكامل بنجاح')
    })
  })

  describe('📊 تقارير التكامل', () => {
    it('يجب أن يعرض تقرير شامل للتكاملات', () => {
      // الانتقال إلى صفحة التقارير
      cy.get('[data-testid="integration-reports"]').click()
      
      // التحقق من عرض التقرير
      cy.get('[data-testid="integration-report"]').should('be.visible')
      cy.shouldContainArabicText('تقرير التكاملات')
      
      // التحقق من وجود الرسوم البيانية
      cy.get('[data-testid="sync-performance-chart"]').should('be.visible')
      cy.get('[data-testid="error-rate-chart"]').should('be.visible')
      cy.get('[data-testid="data-volume-chart"]').should('be.visible')
    })

    it('يجب أن يصدر تقرير التكاملات', () => {
      cy.get('[data-testid="integration-reports"]').click()
      
      // تصدير التقرير
      cy.get('[data-testid="export-report"]').click()
      
      // اختيار نوع التصدير
      cy.get('[data-testid="export-format"]').select('pdf')
      cy.get('[data-testid="confirm-export"]').click()
      
      // التحقق من بدء التصدير
      cy.get('[data-testid="export-status"]').should('contain.text', 'جاري التصدير')
    })
  })

  describe('🚨 معالجة الأخطاء', () => {
    it('يجب أن يعرض رسائل خطأ واضحة عند فشل الاتصال', () => {
      cy.get('[data-testid="add-erp-integration"]').click()
      
      // ملء بيانات خاطئة
      cy.fillForm({
        'api-url': 'invalid-url',
        'api-key': 'invalid-key'
      })
      
      // اختبار الاتصال
      cy.get('[data-testid="test-connection"]').click()
      
      // التحقق من رسالة الخطأ
      cy.get('[data-testid="connection-error"]', { timeout: 15000 })
        .should('be.visible')
        .and('contain.text', 'فشل في الاتصال')
    })

    it('يجب أن يعرض تفاصيل الأخطاء في سجل المزامنة', () => {
      // فتح سجل المزامنة
      cy.get('[data-testid="integration-row"]').first().within(() => {
        cy.get('[data-testid="view-sync-log"]').click()
      })
      
      // البحث عن أخطاء في السجل
      cy.get('[data-testid="filter-by-error"]').click()
      
      // التحقق من عرض الأخطاء
      cy.get('[data-testid="error-log-entry"]').should('be.visible')
      cy.get('[data-testid="error-details"]').should('contain.text', 'تفاصيل الخطأ')
    })
  })
})
