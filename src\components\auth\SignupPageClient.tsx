// src/components/auth/SignupPageClient.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useLocale } from '@/hooks/use-locale';
import { Loader2, <PERSON><PERSON>he<PERSON>, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { Locale } from '@/lib/i18n';
import AuthErrorHandler from './AuthErrorHandler';
import CacheCleaner from './CacheCleaner';
import { determineUserRedirectPath, executeRedirectWithFallback, clearAuthCache } from '@/utils/authRedirect';

interface SignupPageClientProps {
  locale: Locale;
  children: React.ReactNode;
}

export default function SignupPageClient({ locale, children }: SignupPageClientProps) {
  const { user, loading, initialLoadingCompleted } = useAuth();
  const router = useRouter();
  const { t } = useLocale();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [redirectError, setRedirectError] = useState<string | null>(null);
  const [allowRedirect, setAllowRedirect] = useState(false);
  const [isNewSignup, setIsNewSignup] = useState(false);

  // إعادة توجيه للمستخدمين المسجلين مع معالجة خاصة للمستخدمين الجدد
  useEffect(() => {
    // تجنب hydration errors بالتأكد من أننا في العميل
    if (typeof window === 'undefined') return;

    // تجنب التوجيه إذا كان المستخدم في صفحة auth-success
    if (window.location.pathname.includes('auth-success')) {
      console.log('🚫 User is in auth-success page, skipping signup redirect');
      return;
    }

    if (user && initialLoadingCompleted) {
      console.log('🚀 User detected in signup page');

      // فحص إذا كان مستخدم جديد - مع معالجة آمنة للـ storage
      let justSignedUp = false;
      let newUserType = 'customer';

      try {
        justSignedUp = !!(localStorage.getItem('justSignedUp') || sessionStorage.getItem('justSignedUp'));
        newUserType = localStorage.getItem('newUserType') || sessionStorage.getItem('newUserType') || 'customer';
      } catch (error) {
        console.warn('Storage access error:', error);
      }

      if (justSignedUp) {
        console.log('✅ New user detected, but should be handled by auth-success page');
        // لا نقوم بالتوجيه هنا، دع auth-success يتولى الأمر
        return;
      } else {
        console.log('🔄 Existing user, redirecting to dashboard');
        // مستخدم موجود، توجيه للوحة التحكم
        router.push(`/${locale}/dashboard`);
      }
    }
  }, [user, initialLoadingCompleted, locale, router]);

  // وظيفة لمسح التخزين المؤقت وإعادة تحميل الصفحة
  const clearCacheAndReload = () => {
    clearAuthCache();

    if (typeof window !== 'undefined') {
      // مسح جميع البيانات المحلية
      localStorage.clear();
      sessionStorage.clear();

      // مسح cookies إن أمكن
      document.cookie.split(";").forEach((c) => {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });

      // إعادة تحميل الصفحة
      window.location.reload();
    }
  };

  // مسح التخزين المؤقت عند تحميل صفحة التسجيل
  useEffect(() => {
    // مسح أي بيانات مؤقتة قد تسبب مشاكل
    if (typeof window !== 'undefined') {
      // مسح بيانات Firebase المؤقتة
      const firebaseKeys = Object.keys(localStorage).filter(key =>
        key.startsWith('firebase:') ||
        key.includes('authUser') ||
        key.includes('firebase')
      );

      firebaseKeys.forEach(key => {
        localStorage.removeItem(key);
      });

      // مسح session storage
      sessionStorage.clear();

      if (process.env.NODE_ENV === 'development') {
        console.log('🧹 Cleared Firebase cache for fresh signup');
      }
    }
  }, []);

  // التحقق من المصدر لتحديد ما إذا كان المستخدم جاء من عملية تسجيل
  useEffect(() => {
    if (initialLoadingCompleted && user) {
      // التحقق من عدة مصادر لتحديد ما إذا كان هذا تسجيل جديد
      const justSignedUpFlag = sessionStorage.getItem('justSignedUp');
      const urlParams = new URLSearchParams(window.location.search);
      const fromSignup = urlParams.get('from') === 'signup';
      const referrer = document.referrer;
      const isFromSignupPage = referrer.includes('/signup') || referrer.includes('/user-type-selection');

      // تحديد ما إذا كان هذا مستخدم جديد بناءً على عدة عوامل
      const isFromSignupForm = justSignedUpFlag === 'true' || fromSignup || isFromSignupPage;

      console.log('SignupPageClient: User detected, checking source', {
        user: !!user,
        justSignedUpFlag,
        fromSignup,
        isFromSignupPage,
        referrer,
        isFromSignupForm
      });

      if (isFromSignupForm) {
        // هذا تسجيل جديد
        console.log('SignupPageClient: New signup detected');
        setIsNewSignup(true);
        // مسح العلامة
        sessionStorage.removeItem('justSignedUp');
      } else {
        // مستخدم موجود يحاول الوصول لصفحة التسجيل
        console.log('SignupPageClient: Existing user detected');
        setIsNewSignup(false);
      }

      // السماح بالتوجيه في جميع الحالات
      setAllowRedirect(true);
    }
  }, [initialLoadingCompleted, user]);

  useEffect(() => {
    if (initialLoadingCompleted && user && allowRedirect && !isRedirecting) {
      setIsRedirecting(true);

      // إعادة التوجيه الفوري والمبسط
      const redirectUser = async () => {
        try {
          console.log('🔄 SignupPageClient: Quick redirect for user:', user.uid);

          // تحديد مسار التوجيه بسرعة
          let redirectPath = `/${locale}/dashboard`; // افتراضي

          // محاولة سريعة لتحديد نوع المستخدم
          try {
            const result = await Promise.race([
              determineUserRedirectPath(user, locale),
              new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 1000))
            ]);

            if (result.success && result.redirectPath) {
              redirectPath = result.redirectPath;
            }
          } catch (timeoutError) {
            console.warn('⚠️ Timeout determining user path, using default dashboard');
          }

          console.log('✅ SignupPageClient: Redirecting to:', redirectPath);

          // إعادة التوجيه الفوري
          window.location.href = redirectPath;

        } catch (error) {
          console.error("❌ Error during redirect:", error);
          // fallback فوري
          window.location.href = `/${locale}/dashboard`;
        }
      };

      // تنفيذ فوري
      redirectUser();
    }
  }, [user, initialLoadingCompleted, allowRedirect, isRedirecting, router, locale, t, isNewSignup]);

  // Show loading while checking auth state
  if (loading || !initialLoadingCompleted) {
    return (
      <div className="flex min-h-[calc(100vh-8rem)] items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-muted-foreground">{t('loading')}</p>
        </div>
      </div>
    );
  }

  // إذا كان المستخدم مسجل دخول، إعادة توجيه فوري بدون عرض أي محتوى
  if (user && initialLoadingCompleted) {
    // تشغيل إعادة التوجيه إذا لم تكن قد بدأت بعد
    if (!isRedirecting && !allowRedirect) {
      setAllowRedirect(true);
    }

    // إضافة fallback سريع - إعادة توجيه مباشرة بعد 2 ثانية
    useEffect(() => {
      const quickFallback = setTimeout(() => {
        console.log('🚨 Quick fallback redirect triggered');
        window.location.href = `/${locale}/dashboard`;
      }, 2000);

      return () => clearTimeout(quickFallback);
    }, [locale]);

    // عرض شاشة تحميل بسيطة فقط
    return (
      <div className="flex min-h-[calc(100vh-8rem)] items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-muted-foreground">{t('loading')}</p>

          {/* زر للتوجيه اليدوي */}
          <div className="mt-6">
            <Button
              onClick={() => window.location.href = `/${locale}/dashboard`}
              className="bg-primary hover:bg-primary/90"
            >
              الانتقال للوحة التحكم
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show simple loading screen for all logged-in users during redirect
  // عرض شاشة تحميل بسيطة لجميع المستخدمين المسجلين أثناء إعادة التوجيه
  if (user && isRedirecting && allowRedirect) {
    return (
      <div className="flex min-h-[calc(100vh-8rem)] items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-muted-foreground">{t('loading')}</p>

          {/* زر لمسح التخزين المؤقت في حالة التعليق - يظهر بعد 5 ثوان */}
          <div className="mt-8 opacity-0 animate-fade-in" style={{animationDelay: '5s'}}>
            <p className="text-sm text-muted-foreground mb-4">
              إذا استمر التحميل لفترة طويلة، جرب مسح التخزين المؤقت:
            </p>
            <Button
              onClick={clearCacheAndReload}
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              مسح التخزين المؤقت وإعادة التحميل
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show error handler if there's a redirect error
  if (redirectError) {
    return (
      <AuthErrorHandler
        locale={locale}
        error={redirectError}
        onRetry={() => {
          setRedirectError(null);
          setIsRedirecting(true);
          // Retry redirect logic
          window.location.reload();
        }}
      />
    );
  }

  // User is not logged in, show signup form
  if (!user) {
    return (
      <>
        <CacheCleaner clearOnMount={true} clearFirebaseOnly={true} />
        {children}
      </>
    );
  }

  // Fallback (shouldn't reach here)
  return null;
}
