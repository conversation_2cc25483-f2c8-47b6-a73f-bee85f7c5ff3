// src/components/auth/QuickRedirect.tsx
"use client";

import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { useLocale } from '@/hooks/use-locale';
import type { Locale } from '@/lib/i18n';

interface QuickRedirectProps {
  locale: Locale;
  targetPage?: 'dashboard' | 'merchant' | 'representative';
}

export default function QuickRedirect({ locale, targetPage = 'dashboard' }: QuickRedirectProps) {
  const { user, initialLoadingCompleted } = useAuth();
  const router = useRouter();
  const { t } = useLocale();

  useEffect(() => {
    if (user && initialLoadingCompleted) {
      console.log('🚀 QuickRedirect: Redirecting user immediately');
      
      // تحديد مسار التوجيه
      let redirectPath = `/${locale}/dashboard`;
      
      // فحص نوع المستخدم من sessionStorage أولاً
      const userType = sessionStorage.getItem('newUserType');
      
      if (userType === 'merchant' || targetPage === 'merchant') {
        redirectPath = `/${locale}/merchant/pending-approval`;
      } else if (userType === 'representative' || targetPage === 'representative') {
        redirectPath = `/${locale}/representative/signup`;
      }
      
      // مسح البيانات المؤقتة
      sessionStorage.removeItem('justSignedUp');
      sessionStorage.removeItem('newUserType');
      sessionStorage.removeItem('signupTimestamp');
      
      // إعادة توجيه فورية
      console.log('🔄 Redirecting to:', redirectPath);
      window.location.replace(redirectPath);
    }
  }, [user, initialLoadingCompleted, locale, targetPage]);

  // إذا لم يكن هناك مستخدم، إعادة توجيه لصفحة التسجيل
  useEffect(() => {
    if (!user && initialLoadingCompleted) {
      console.log('🔄 No user found, redirecting to signup');
      router.replace(`/${locale}/user-type-selection`);
    }
  }, [user, initialLoadingCompleted, locale, router]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
      <div className="flex flex-col items-center justify-center space-y-4 text-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="text-muted-foreground">{t('loading')}</p>
        <p className="text-sm text-muted-foreground">جاري توجيهك للصفحة المناسبة...</p>
      </div>
    </div>
  );
}
