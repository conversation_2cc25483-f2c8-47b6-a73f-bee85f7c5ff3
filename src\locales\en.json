{"appName": "<PERSON><PERSON><PERSON>", "tagline": "Connecting local stores with customers through seamless e-commerce.", "home": "Home", "pricing": "Subscription Plans", "login": "<PERSON><PERSON>", "signup": "Sign Up", "profile": "Profile", "logout": "Logout", "language": "Language", "english": "English", "arabic": "Arabic", "homeHeroTitle": "<PERSON><PERSON><PERSON>: Your City's Treasures, Delivered.", "homeHeroSubtitle": "Explore a vibrant marketplace of unique goods from local stores and artisans. Fresh produce, handmade crafts, local fashion, and more – support your neighbors and discover quality.", "getStarted": "Get Started", "getStartedFree": "Get Started Free", "browseStores": "Browse Stores", "copyright": "© {{year}} <PERSON><PERSON><PERSON>. All rights reserved.", "merchants": "Merchants", "customers": "Customers", "merchantPlansTitle": "Plans for Merchants", "customerPlansTitle": "Plans for Customers", "plan_basic_name": "Basic Plan", "plan_premium_name": "Premium Plan", "plan_business_name": "Business Plan", "plan_customer_basic_name": "Basic Customer", "plan_customer_premium_name": "Premium Customer", "SAR": "SAR", "monthly": "monthly", "month": "month", "free": "Free", "pricePerPeriod": "{{price}} {{currency}} / {{period}}", "commission": "{{value}}% commission on sales", "basicStoreManagement": "Basic store management", "addManageProducts": "Add and manage products", "emailSupport": "Email support", "simpleDashboard": "Simple dashboard", "basicStats": "Basic statistics", "basicOrderManagement": "Basic order management", "standardStorePage": "Standard store page", "productImages3": "Up to 3 images per product", "basicInventory": "Basic inventory management", "simpleMonthlyReports": "Simple monthly reports", "basicPaymentIntegration": "Basic payment integration", "basicRatingSystem": "Basic rating system", "basicEmailNotifications": "Basic email notifications", "basicDataExport": "Basic data export", "manualDataBackup": "Manual data backup", "unlimitedProducts": "Unlimited products", "prioritySupport247": "24/7 priority support", "advancedDashboard": "Advanced dashboard", "fullStoreCustomization": "Full store page customization", "advancedSalesAnalytics": "Advanced sales analytics", "advancedInventorySystem": "Advanced inventory system", "socialMediaIntegration": "Social media integration", "customerLoyaltySystem": "Customer loyalty system", "detailedWeeklyReports": "Detailed weekly reports", "advancedMarketingTools": "Advanced marketing tools", "multipleShippingOptions": "Multiple shipping options", "couponsDiscountsSystem": "Coupons and discounts system", "multiFormatReportExport": "Export reports in multiple formats", "autoDailyBackup": "Automatic daily backup", "vipSupport": "VIP support with dedicated account manager", "smartAnalyticsPredictions": "Smart analytics with future predictions", "erpPosIntegration": "Integration with ERP & POS systems", "crmSystem": "CRM system", "fullSystemCustomization": "Full system customization", "processMarketingAutomation": "Process and marketing automation", "realtimeAdvancedAnalytics": "Real-time advanced analytical reports", "multiBranchManagement": "Multi-branch management", "advancedPerformanceMonitoring": "Advanced performance monitoring system", "advancedFraudProtection": "Advanced fraud protection", "accountingIntegration": "Integration with accounting systems", "autoHourlyBackup": "Automatic hourly backup", "businessConsultingServices": "Business consulting services", "customTeamTraining": "Custom team training", "freeAccountCreation": "Free account creation", "browseProducts": "Browse products", "addToCartWishlist": "Add to cart and wishlist", "basicSearch": "Basic search", "basicOrderTracking": "Basic order tracking", "ePayment": "E-payment", "ratingsReviews": "Ratings and reviews", "saveAddresses": "Save addresses", "orderHistory": "Order history", "socialSharing": "Social sharing", "profileManagement": "Profile management", "passwordRecovery": "Password recovery", "basicAlerts": "Basic alerts", "unlimitedFreeShipping": "Unlimited free shipping", "discount20all": "20% discount on all purchases", "priorityOrders": "Priority Orders", "exclusiveOffers": "Exclusive offers", "doubleRewardPoints": "Double reward points", "freeOrderCancellation": "Free order cancellation", "earlyAccessSales": "Early access to sales", "vipCustomerService": "VIP customer service", "advancedOrderTracking": "Advanced order tracking", "instantNotifications": "Instant notifications", "monthlyVouchers": "Monthly purchase vouchers", "cashbackPurchases": "Cashback on purchases", "premiumClubMembership": "Premium Club membership", "loginToAccount": "Login to your account", "emailAddress": "Email Address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "needAccount": "Need an account?", "createAnAccount": "Create an account", "username": "Username", "confirmPassword": "Confirm Password", "changePassword": "Change Password", "changePasswordDescription": "Update your password. Your new password must be at least 8 characters long.", "currentPassword": "Current Password", "newPassword": "New Password", "enterCurrentPassword": "Enter your current password", "enterNewPassword": "Enter your new password", "confirmNewPassword": "Confirm your new password", "updatePassword": "Update Password", "passwordChangeSuccessTitle": "Password Changed", "passwordChangeSuccessMessage": "Your password has been updated successfully.", "wrongCurrentPassword": "Current password is incorrect", "weakPassword": "New password is too weak. It must be at least 8 characters long.", "recentLoginRequired": "Please sign in again before changing your password", "passwordChangeFailed": "Password change failed. Please try again.", "alreadyHaveAccount": "Already have an account?", "myProfile": "My Profile", "customizeProfile": "Customize Profile", "uploadImage": "Upload Image", "generateAvatar": "Generate Avatar", "saveChanges": "Save Changes", "selectPlan": "Select Plan", "popular": "Popular", "passwordsDoNotMatch": "Passwords do not match.", "errorTitle": "Error", "signupSuccessTitle": "Signup Successful", "signupSuccessMessage": "Welcome! Your account has been created. You are being redirected...", "signupFailed": "Signup failed. Please try again.", "loginSuccessTitle": "Login Successful", "loginSuccessMessage": "Welcome back!", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "user": "User", "alreadyLoggedIn": "You are already logged in", "redirectingToYourDashboard": "You are being redirected to your dashboard...", "authenticationError": "Authentication Error", "authenticationErrorMessage": "An error occurred while verifying your identity. Please try again.", "securityRecommendation": "For your security, we recommend logging out and signing in again.", "secureLogout": "Se<PERSON>", "securityLogoutMessage": "You have been logged out for security reasons.", "retry": "Retry", "loggingOut": "Logging out...", "notLoggedIn": "Not logged in", "profileUpdateSuccessTitle": "Profile Updated Successfully", "profileUpdateSuccessMessage": "Your profile has been updated successfully", "profileUpdateFailed": "Failed to update profile.", "logoutSuccessTitle": "Logged Out", "logoutSuccessMessage": "You have been successfully logged out.", "logoutFailed": "<PERSON><PERSON><PERSON> failed. Please try again.", "invalidCredentials": "Invalid email or password. Please try again.", "accountDisabled": "This account has been disabled. Please contact support.", "tooManyRequests": "Too many failed attempts. Please try again later.", "networkError": "Network connection error. Please check your internet connection.", "emailAlreadyInUse": "This email is already in use. Please try a different email or log in.", "usernameAlreadyInUse": "This username is already taken. Please choose a different username.", "checkingEmail": "Checking email availability...", "passwordTooShort": "Password should be at least 6 characters.", "passwordLengthHint": "Password must be at least 6 characters long.", "loadingProfile": "Loading...", "loadingTerms": "Loading terms and conditions...", "userType": "User Type", "customer": "Customer", "merchant": "Merchant", "termsAndConditions": "Terms and Conditions", "iAgreeToThe": "I agree to the", "commercialRegistration": "Commercial Registration", "otherLicenses": "Other Licenses (Optional)", "freelanceDocument": "Freelance Work Document", "errorUserTypeRequired": "Please select a user type.", "errorTermsRequired": "You must accept the Terms and Conditions to continue.", "errorCommercialRegistrationRequired": "Please upload your Commercial Registration certificate.", "errorFreelanceDocumentRequired": "Please upload your freelance work document from the freelance platform.", "fileFormatsAllowed": "Allowed formats: PDF, JPG, PNG.", "termsAndConditionsTitle": "Terms and Conditions", "termsAndConditionsForCustomers": "Terms and Conditions for Customers", "termsAndConditionsForMerchants": "Terms and Conditions for Merchants", "termsAndConditionsForRepresentatives": "Terms and Conditions for Representatives", "customerTermsText": "Welcome to Mikhla!\n\nBy registering and using the Mikhla platform as a customer, you agree to be bound by the following terms and conditions:\n\n1.  **Account and Personal Responsibility**: You are fully responsible for maintaining the confidentiality of your account information (username and password) and for all activities that occur under your account. All information provided by you must be accurate and up-to-date. Notify us immediately of any unauthorized use of your account.\n2.  **Legal Eligibility**: You must be legally eligible to make purchases under the laws applicable in the Kingdom of Saudi Arabia.\n3.  **Purchases and Payments**: When making a purchase, you agree to provide correct, complete, and accurate payment information. All sales are considered final unless otherwise stated by the store you are transacting with in its return and exchange policy.\n4.  **Role of Mikhla Platform**: You acknowledge and agree that the Mikhla platform acts as an intermediary between you (the Customer) and the Merchants. Mikhla does not directly hold or escrow funds; payments are processed through approved payment gateways according to established mechanisms.\n5.  **Platform Usage**: You undertake to use the Mikhla platform legally, ethically, and for its intended purposes only. Any use intended for fraud, harm to others, dissemination of illegal or offensive content, or disruption of platform services is strictly prohibited.\n6.  **Privacy and Data Protection**: Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your personal information. Please review it carefully to understand our practices.\n7.  **Product and Store Reviews**: We encourage you to provide honest, constructive, and helpful reviews for the community. Mikhla reserves the right to remove any content deemed inappropriate, offensive, misleading, infringing on intellectual property rights, or violating these terms.\n8.  **Communication and Notifications**: By using the platform, you consent to receive emails or in-app notifications related to your account, orders, promotions, or platform updates. You can manage your communication preferences through your account settings where applicable.\n9.  **Intellectual Property**: All content displayed on the platform (excluding user and store content) is the property of Mikhla or licensed to it and is protected by copyright and trademark laws. You may not use any of these materials without prior written permission.\n10. **Modification of Terms and Conditions**: Mikhla reserves the right to modify these terms and conditions at any time. You will be notified of any material changes via email or through a prominent notice on the platform. Your continued use of the platform after the posting of modifications constitutes your acceptance of the new terms.\n11. **Governing Law and Dispute Resolution**: These terms and conditions shall be governed by and construed in accordance with the laws of the Kingdom of Saudi Arabia. Any disputes arising from these terms will be resolved amicably, and if not possible, through competent courts in the Kingdom of Saudi Arabia.\n12. **Governing Language**: Both Arabic and English are the approved languages for these terms. In the event of any conflict between the Arabic version and the English version, the Arabic version shall prevail unless local law requires otherwise.\n\nThank you for your trust and use of the Mikhla platform. We wish you an enjoyable and safe shopping experience!", "merchantTermsText": "Welcome to the Mikhla Merchant Platform!\n\nThis agreement ('Terms', 'Agreement') sets forth the legally binding rules and regulations for your use of the Mikhla platform ('Platform', 'Service') as a merchant ('You', 'Merchant'). By registering and using the Platform, you acknowledge that you have read, understood, and agree to be bound by all of these Terms. If you do not agree with any part of these Terms, you must not use the Platform.\n\n1.  **Merchant Account Registration and Verification**:\n    *   1.1. **Accurate Information**: You must provide complete, accurate, and current business information during registration and thereafter, including legal business name, address, contact details (phone number and email), bank account details for payouts, and tax identification numbers. You commit to promptly updating this information should any changes occur.\n    *   1.2. **Required Documentation**: You agree to submit all necessary documentation for identity and business verification promptly upon request, such as a valid copy of the Commercial Registration, relevant business licenses (e.g., municipal licenses, safety certificates, etc.), national ID of the owner or authorized signatory, and any other documents Mi<PERSON><PERSON> may require for verification or compliance purposes. Mikhla reserves the right to suspend or terminate your account for failure to provide these documents or if they are found to be invalid, expired, or insufficient.\n    *   1.3. **Account Confidentiality and Security**: You are solely responsible for maintaining the confidentiality of your account credentials (username and password) and for all activities that occur under your account. <PERSON><PERSON><PERSON> must be notified immediately of any unauthorized use of your account or any other security breach. Mikhla is not liable for any loss or damage arising from your failure to comply with this requirement.\n    *   1.4. **Merchant Eligibility**: You must be a legally registered business entity in the Kingdom of Saudi Arabia or an individual authorized to conduct business according to applicable regulations.\n\n2.  **Store Management and Product Listings**:\n    *   2.1. **Product Description and Quality**: All product descriptions, images, prices, availability information, and specifications (such as size, color, material, country of origin, expiry date if applicable) must be accurate, complete, and not misleading. You are solely responsible for your store's content and product listings, ensuring that products offered are of acceptable commercial quality, conform to the descriptions provided, are free from defects, and safe for their intended use.\n    *   2.2. **Prohibited Products and Legal Compliance**: It is strictly forbidden to list or sell any products that are illegal under the laws of the Kingdom of Saudi Arabia (including products contrary to Islamic Sharia), counterfeit or fraudulent items, or products that infringe on the intellectual property rights of third parties (including copyrights, trademarks, and patents), or violate Mikhla's published policies, public morals, or consumer protection regulations. Mikhla reserves the right to remove any offending listings and/or suspend or terminate your account without prior notice, with the possibility of imposing fines or taking legal action.\n    *   2.3. **Pricing and Taxes**: You determine your product prices. All prices displayed to customers must be in Saudi Riyals (SAR) and inclusive of Value Added Tax (VAT) and any other applicable taxes, unless clearly stated otherwise and permitted by regulation. You are responsible for determining, collecting, and remitting all applicable taxes to the relevant authorities and issuing tax-compliant invoices.\n    *   2.4. **Inventory and Availability Management**: You must maintain accurate and up-to-date inventory levels for all listed products to ensure availability accuracy for buyers and avoid selling out-of-stock items. Unavailable products must be promptly removed or clearly marked as such.\n    *   2.5. **Intellectual Property Rights for Content**: You represent and warrant that you have all necessary rights, licenses, and permissions to use and display all content you upload to your store (including text, images, videos, logos, and trademarks).\n\n3.  **Order Fulfillment and Shipping**:\n    *   3.1. **Timely Order Processing**: You agree to process and fulfill customer orders received through the platform within a period not exceeding 2-3 business days, unless a different timeframe is clearly communicated to the customer before purchase (e.g., for products requiring special preparation, custom-made items, or pre-orders). This includes order confirmation, product preparation, and handover to the approved shipping carrier or the one chosen by the customer if this feature is available.\n    *   3.2. **Secure Shipping and Delivery**: You are responsible for securely and appropriately packaging products to prevent damage during transit and for selecting a reliable shipping method. You must provide accurate and correct tracking information to customers where possible and in a timely manner. Shipping policies and associated costs (if any) must be clear to customers before they complete their purchase.\n    *   3.3. **Shipping Responsibility and Insurance**: Unless explicitly agreed otherwise with the customer or Mikhla, you bear the responsibility for the product until it is successfully delivered to the customer. Obtaining adequate shipping insurance to cover loss or damage, especially for high-value shipments, is strongly recommended, and the platform may require proof of insurance for certain product categories.\n    *   3.4. **Communication Regarding Delays**: In the event of any anticipated delay in order processing or shipping beyond the specified period, you must immediately inform the concerned customer, providing the reason for the delay and an updated, realistic delivery date.\n    *   3.5. **Delivery and Verification**: You are responsible for ensuring products are delivered to the correct address provided by the customer and verifying the recipient's identity if necessary, according to the shipping company's policies.\n\n4.  **Fees, Commissions, and Payments**:\n    *   4.1. **Fee Structure**: You agree to pay the applicable subscription fees for your chosen merchant plan (if any) in addition to any agreed-upon commissions on sales made through the platform. The fee and commission structure will be clarified to you upon registration or when changing plans. Mikhla reserves the right to modify the fee structure with at least 30 days prior notice to you.\n    *   4.2. **Payment Processing and Role of Mikhla Platform**: Mikhla (or its payment processing partner) will process customer payments. You acknowledge and agree that the Mikhla platform acts as an intermediary platform facilitating transactions between you (the Merchant) and the Customers. Mikhla does not directly hold or escrow funds; payments are processed via approved payment gateways according to established mechanisms. The currency for all transactions is Saudi Riyal.\n    *   4.3. **Merchant Payouts**: Net sales (after deducting commissions, payment processing fees, any other applicable charges, refunds, or chargebacks, or fines) will be transferred to your registered bank account according to the payment schedule set by Mikhla (e.g., weekly, bi-weekly, monthly), and after any minimum transfer threshold is met (if applicable).\n    *   4.4. **Disputes, Refunds, and Chargebacks**: Mikhla reserves the right to withhold payments or make deductions from your future payouts to cover customer refunds (in accordance with your return policy or consumer protection system requirements), confirmed fraud cases, or other violations of these terms. Mikhla will work with you in good faith to resolve any payment disputes, but Mikhla's final decision will be binding in disputes related to the platform.\n    *   4.5. **Financial Reports**: Mikhla will provide you with access to financial reports detailing sales, commissions, and payouts.\n\n5.  **Returns, Exchanges, and Customer Service**:\n    *   5.1. **Return and Exchange Policy**: You must define and display a clear, detailed, and fair return and exchange policy for your customers, compliant with the consumer protection system in the Kingdom of Saudi Arabia and any Mikhla platform requirements. This policy must include, at a minimum, a 7-day return period for unused products in their original packaging and condition, unless the nature of the product dictates otherwise (e.g., perishable goods, personal items, software, or custom-made products). You must clearly state any exceptions to this rule in your own policy. The policy should clarify return conditions, the return process, and who bears shipping costs in different return scenarios.\n    *   5.2. **High-Quality Customer Service**: You are responsible for providing prompt, effective, and courteous customer service, responding to customer inquiries and complaints related to your products or orders in a timely manner (e.g., within a maximum of 24-48 business hours).\n    *   5.3. **Handling Returns and Warranties**: You must process return, exchange, and defect repair (warranty) requests according to your stated policy and applicable regulations. You commit to providing clear information about the warranties offered on your products.\n\n6.  **Data, Privacy, and Intellectual Property**:\n    *   6.1. **Customer Data**: You may access customer data (such as name, shipping address, contact information) solely for the purpose of fulfilling orders, related communication, providing customer service, and complying with legal requirements. All customer data must be handled with strict confidentiality and in accordance with Mikhla's privacy policy and all applicable data protection laws in the Kingdom of Saudi Arabia (such as the Personal Data Protection Law - PDPL). Using customer data for any marketing purposes not authorized by the customer or the platform is prohibited.\n    *   6.2. **Merchant's Intellectual Property**: You retain all intellectual property rights to your content (such as brand logo, original product images, descriptions). However, you grant Mikhla a worldwide, non-exclusive, royalty-free, sublicensable license to use, display, copy, modify (for formatting and optimal display on the platform), and distribute your content for the purpose of operating, developing, and promoting the platform and related services, including its use in platform marketing campaigns.\n    *   6.3. **Mikhla's Intellectual Property**: All intellectual property rights related to the platform itself (such as software, design, interfaces, the 'Mikhla' trademark, and Mikhla-owned materials) are the exclusive property of Mikhla or its licensors. You may not use any of these materials without prior written permission from Mikhla.\n    *   6.4. **Non-Infringement**: You warrant that your content and your use of the platform do not infringe the intellectual property rights or any other rights of any third party. You bear full responsibility for any claims arising from the infringement of these rights.\n\n7.  **Legal Obligations and Taxes**:\n    *   7.1. **Compliance with Laws and Regulations**: You are fully responsible for complying with all local and national laws, regulations, and systems related to your business activity, including (but not limited to) the e-commerce system, consumer protection system, product safety, commercial licensing requirements, labor laws, and the system for combating commercial fraud and deception.\n    *   7.2. **Taxes and Invoices**: You are solely responsible for determining, collecting, submitting, issuing correct tax invoices, and paying all applicable taxes on your sales (including Value Added Tax) to the competent tax authorities. You must be registered with the Zakat, Tax and Customs Authority (ZATCA) if you meet the registration criteria, and provide Mikhla with your tax registration number.\n    *   7.3. **Commercial Records**: You commit to maintaining accurate and up-to-date commercial records for all transactions conducted via the platform according to regulatory requirements.\n\n8.  **Ratings and Reviews**: The merchant acknowledges that customers have the right to rate products and services provided. The merchant must not manipulate ratings or attempt to influence them unfairly. Mikhla reserves the right to remove ratings that violate its policies.\n\n9.  **Termination and Suspension**:\n    *   9.1. **By Merchant**: You may request termination of your account according to the procedures specified by Mikhla, subject to settling any outstanding obligations (such as pending orders, due fees, or pending refunds).\n    *   9.2. **By Mikhla**: Mikhla reserves the right to suspend or terminate your account and access to the platform, temporarily or permanently, at its sole discretion and for any reason, including (but not limited to) violation of these terms, receiving multiple and documented customer complaints, suspicion of fraudulent or illegal activity, non-payment of due fees, damaging the platform's reputation, or failure to comply with quality and service standards set by the platform. Mikhla will make a reasonable effort to provide prior notice of suspension or termination unless the situation requires immediate action.\n    *   9.3. **Effects of Termination**: Upon termination, your access to your merchant account will be disabled, and your store and products will be removed from the platform. Any outstanding financial obligations to either party remain. Mikhla may retain some of your account data for legal, accounting, or internal analytical purposes, in accordance with its privacy policy and applicable laws.\n\n10. **Limitation of Liability and Disclaimer**:\n    *   10.1. **Use of Platform at Your Own Risk**: The platform and services are provided 'as is' and 'as available' without any warranties of any kind, whether express or implied, including but not limited to, implied warranties of merchantability, fitness for a particular purpose, or non-infringement. Mikhla does not warrant that the platform will be error-free, uninterrupted, secure, or permanently available.\n    *   10.2. **Mikhla as an Intermediary Platform**: You acknowledge that Mikhla is a technology platform that facilitates transactions between merchants and customers. Mikhla is not a direct party in any sale or purchase transaction conducted between you and the customer (unless explicitly stated otherwise), and does not assume any responsibility for the quality of products, or their safety, or legality, or accuracy of listings, or the ability of customers to pay, or your ability to complete the sale. Any disputes between you and the customer must be resolved directly between yourselves, with Mikhla's potential intervention as a mediator at its discretion.\n    *   10.3. **Limitation of Financial Liability**: To the maximum extent permitted by law, Mikhla, its officials, employees, or agents will not be liable for any direct, indirect, incidental, special, consequential, or punitive damages (including loss of profits, data, reputation, or business opportunities) arising from or related to your use or inability to use the platform or any products or services obtained through it, even if Mikhla has been advised of the possibility of such damages. In all cases, Mikhla's total liability to you concerning any claim arising from this agreement shall not exceed the total amount of commissions you actually paid to Mikhla during the three months preceding the claim that led to the liability (if any).\n\n11. **Governing Law and Dispute Resolution**: These terms and conditions shall be governed by and construed in accordance with the laws of the Kingdom of Saudi Arabia. Any disputes arising from this agreement will be resolved amicably first, and if that fails, through binding arbitration in the Kingdom of Saudi Arabia in accordance with the rules of the Saudi Center for Commercial Arbitration (SCCA).\n\n12. **Amendments to Terms**: Mikhla reserves the right to amend these terms at any time at its sole discretion. You will be notified of material changes via your registered email or through a prominent notice on the platform at least 30 days before their effective date. Your continued use of the platform after the effective date of these amendments constitutes your binding acceptance of the new terms. If you do not agree to the amended terms, you must stop using the platform and close your account.\n\n13. **General Provisions**:\n    *   13.1. **Entire Agreement**: These terms, in addition to any other policies or guidelines published on the platform and forming an integral part thereof (such as the Privacy Policy, Prohibited Products Policy), constitute the entire and final agreement between you and Mikhla regarding its subject matter, and supersede all prior or contemporaneous agreements or understandings, whether oral or written.\n    *   13.2. **Severability**: If any part of these terms is deemed invalid or unenforceable by a competent court or arbitration panel, that part will be modified to the minimum extent necessary to make it valid and enforceable, and the remaining parts of the terms will remain in full force and effect.\n    *   13.3. **No Waiver**: Mikhla's failure to exercise or enforce any right or provision of these terms does not constitute a waiver of such right or provision. Any waiver must be in writing and signed by an authorized representative of Mikhla.\n    *   1.3.4. **Force Majeure**: Mikhla will not be liable for any failure or delay in performance due to circumstances beyond its reasonable control, including natural disasters, wars, terrorism, riots, labor strikes, epidemics, governmental decisions, or failure of internet or telecommunications infrastructure.\n    *   13.5. **Notices**: Any notices or other communications required or permitted under this agreement can be provided electronically via email to your registered address or through notices on the platform or merchant dashboard. A notice is considered delivered when sent.\n    *   13.6. **Relationship of Parties**: The relationship between Mikhla and the Merchant is that of an independent contractor. Nothing in this Agreement shall be construed as creating a partnership, joint venture, agency, or employment relationship between the parties.\n    *   13.7. **Governing Language**: Both Arabic and English are the approved languages for these terms. In the event of any conflict between the Arabic version and the English version, the Arabic version shall prevail unless local law requires otherwise.\n\nThank you for using the Mikhla merchant platform!", "generalTermsText": "Please select a user type (Customer or Merchant) to view the specific terms and conditions.", "orSignupManually": "or signup manually", "popularCategoriesTitle": "Explore Popular Categories", "popularCategoriesSubtitle": "Dive into a wide array of authentic local products and discover new favorites waiting around every corner. From everyday essentials to unique handcrafted items, <PERSON><PERSON><PERSON> connects you with the heart of your community.", "categoryGroceries": "Groceries & Foodstuff", "categoryGroceriesDesc": "Order daily groceries, farm-fresh fruits and vegetables, artisanal bread, and delicious local delicacies. Get everything you need for your kitchen delivered straight to your doorstep.", "categoryHandicrafts": "Handicrafts & Gifts", "categoryHandicraftsDesc": "Explore a unique collection of handmade gifts, traditional crafts, and artistic creations that bear the unique touch of creative local artisans. Find the perfect, one-of-a-kind present.", "categoryFashion": "Local Fashion & Apparel", "categoryFashionDesc": "Discover the latest local fashion designs, traditional attire, and contemporary clothing that reflects your region's culture. Support promising local designers and unique boutiques.", "categoryHealthBeauty": "Health & Beauty", "categoryHealthBeautyDesc": "Pamper yourself with natural and organic health and beauty products. Find locally sourced skincare, soaps, essential oils, and wellness items, carefully selected for your well-being.", "merchantCtaTitle": "Are you a local merchant?", "merchantCtaSubtitle": "Are you a shop owner, artisan, or local producer? Join <PERSON> today to expand your reach, showcase your unique products to a wider community, and grow your business easily and effectively through our dedicated platform.", "listYourStore": "List Your Store", "merchantBenefitsTitle": "Key Benefits for Merchants:", "merchantBenefit1": "Reach more customers in your city.", "merchantBenefit2": "Easy-to-use store management tools.", "merchantBenefit3": "Secure payment processing.", "merchantBenefit4": "Marketing and promotional opportunities.", "yourCurrentLocation": "Your Current Location", "latitude": "Latitude", "longitude": "Longitude", "fetchingLocation": "Fetching your location...", "locationPermissionDenied": "Location permission denied. Please enable location services in your browser/system to see nearby stores.", "locationUnavailable": "Location information is unavailable.", "locationRequestTimeout": "The request to get user location timed out.", "locationErrorUnknown": "An unknown error occurred while trying to get your location.", "geolocationNotSupported": "Geolocation is not supported by your browser.", "locationPermissionPrompt": "Please allow location access to find nearby stores.", "discoverNearbyTitle": "Discover Stores Near You", "discoverNearbySubtitle": "Allow location access to find local gems and support businesses in your neighborhood. We'll show you stores closest to you first!", "loadingMap": "Loading Map...", "yourLocation": "You are here", "errorFileUploadFailed": "Upload failed for: {{file}}. Please try again or contact support.", "profileAvatarUploadSuccess": "Avatar Uploaded Successfully", "profileAvatarUploadFailed": "Cloudinary avatar upload failed. Please try again.", "profileAvatarGenerateSuccess": "Avatar generated.", "profileOnlyMerchantsCanUploadAvatar": "Only merchants can upload or generate an avatar.", "addNewProduct": "Add New Product", "addNewProductSubtitle": "Fill in the details below to add a new product to your store.", "productName": "Product Name", "productDescription": "Product Description", "productPrice": "Price", "productCategory": "Product Category", "productStockQuantity": "Stock Quantity", "productImages": "Product Images", "uploadFiles": "Upload files", "switchToEnglish": "Switch to English", "switchToArabic": "Switch to Arabic", "orDragAndDrop": "or drag and drop", "imageTypesAndMaxSize": "Images (PNG, JPG, GIF) up to {{size}}MB each. Max {{count}} allowed.", "productIsActive": "Product is active (visible for sale)", "saveProduct": "Save Product", "savingProduct": "Saving Product...", "productAddedSuccessTitle": "Product Added Successfully", "productAddedSuccessMessage": "Product '{{productName}}' has been added to your store.", "errorAddingProductFailed": "Failed to add product. Please try again.", "errorProductNameRequired": "Product name is required.", "errorPriceInvalid": "Price must be a positive number.", "errorCategoryRequired": "Product category is required.", "errorStockInvalid": "Stock quantity must be a non-negative integer.", "errorMinOneImage": "At least one image is required for the product.", "productImagePreview": "Product image preview", "removeImage": "Remove image", "errorMaxImagesReached": "You have reached the maximum number of allowed images ({{count}}).", "errorImageSizeExceeded": "Image size exceeds the allowed limit ({{size}}MB).", "errorFileTypeInvalid": "Invalid file type. Please upload an image file.", "errorImageUploadFailedSummary": "Failed to upload one or more images.", "errorPartialImageUpload": "Some images failed to upload. Product not saved.", "currencySAR": "SAR", "productCategoryPlaceholder": "e.g., Electronics, Apparel, Books", "locationPermissionLoginRequired": "Please log in to enable location features and discover nearby stores.", "merchantDashboard": "Merchant Dashboard", "welcomeMerchant": "Welcome, {{name}}!", "storeNameLabel": "Store Name:", "manageYourStore": "Manage your store and products from here.", "addNewProductCta": "Add New Product", "errorFetchingStoreData": "Error fetching your store data. Please try again.", "storeNotActive": "Your store is not yet active. Please complete all required information or contact support.", "loadingStoreData": "Loading store data...", "loadingProducts": "Loading products...", "myProducts": "My Products", "errorFetchingProducts": "Error fetching your products. Please try again.", "noProductsFoundTitle": "No Products Found", "noProductsFoundDesc": "You haven't added any products yet. Start by adding your first product.", "addProductNow": "Add Product Now", "productList": "Product List", "manageYourProductsDesc": "Here you can view, edit, or delete your products.", "image": "Image", "productNameTable": "Name", "editProduct": "Edit Product", "deleteProduct": "Delete Product", "noImage": "No Image", "mustBeLoggedInAsMerchant": "You must be logged in as a merchant to access this page.", "notAllowedToAccessMerchantPage": "You are not authorized to access this merchant page.", "loadingMerchantSection": "Loading merchant section...", "accessDenied": "Access Denied", "mustBeLoggedInToAccessMerchant": "You must be logged in as a merchant to access this section.", "mustBeMerchantToAccess": "You must be a merchant to access this page.", "errorStoreDataNotFound": "Store data not found. Please try again or contact support.", "errorFetchingPlan": "Error fetching subscription plan data.", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteProductMessage": "Are you sure you want to delete the product \"{{productName}}\"? This action cannot be undone.", "deletingProduct": "Deleting product...", "productDeletedSuccessTitle": "Product Deleted", "productDeletedSuccessMessage": "Product \"{{productName}}\" has been successfully deleted.", "errorDeletingProduct": "Failed to delete product \"{{productName}}\".", "errorDeletingProductImages": "Failed to delete one or more images for product \"{{productName}}\". The product document was still deleted.", "unauthorizedMerchantAction": "Unauthorized: You are not allowed to perform this action.", "storeNotFoundOrNoAccess": "Store not found or you do not have permission to access it.", "customerAvatarInfo": "Customer avatar is automatically generated based on your name.", "uploadingAvatarTitle": "Uploading Avatar...", "uploadingAvatarDesc": "Please wait while your new avatar is being uploaded to Cloudinary...", "profileAvatarUploadSuccessDesc": "Your new avatar has been set using Cloudinary.", "uploadingFilesTitle": "Uploading Files to Cloudinary...", "uploadingFilesDesc": "Your documents are being uploaded. This may take a moment...", "fileUploadSuccessTitle": "Files Uploaded to Cloudinary", "fileUploadSuccessDesc": "Your documents have been successfully uploaded and linked to your store.", "crUploadSuccessDescCloudinary": "Commercial registration uploaded successfully to Cloudinary.", "otherLicensesUploadSuccessDescCloudinary": "Other licenses uploaded successfully to Cloudinary.", "freelanceDocumentUploadSuccessDescCloudinary": "Freelance document uploaded successfully to Cloudinary.", "uploadingCRDescCloudinary": "Uploading commercial registration to Cloudinary...", "uploadingOtherLicensesDescCloudinary": "Uploading other licenses to Cloudinary...", "uploadingFreelanceDocumentDescCloudinary": "Uploading freelance document to Cloudinary...", "signupInProgressTitle": "Signup in Progress", "signupInProgressMessage": "Creating your account, please wait...", "notSet": "Not Set", "editProfile": "Edit Profile", "errorInvalidEmailFormat": "Please enter a valid email address format.", "loginRedirecting": "Redirecting you to your dashboard...", "loadingDashboard": "Loading dashboard...", "customerDashboardTitle": "Customer Dashboard", "customerDashboardSubtitle": "Manage your orders, profile, and more.", "welcomeUser": "Welcome, {{name}}!", "quickActions": "Quick Actions", "quickActionsSubtitle": "Navigate easily through your account.", "browseProductsDashboard": "Browse Products", "myOrders": "My Orders", "manageProfile": "Manage Profile", "accountOverview": "Account Overview", "accountOverviewSubtitle": "A summary of your recent activity.", "accountOverviewPlaceholder": "Your recent orders and activities will appear here soon.", "recommendationsTitle": "Recommendations", "recommendationsSubtitle": "Products you might like.", "recommendationsPlaceholder": "We'll have some recommendations for you shortly!", "goToHomepage": "Go to Homepage", "errorFetchingUserData": "An error occurred while fetching your user data. Please try again.", "redirectingTitle": "Redirecting", "defaultStoreName": "My Store", "cloudinaryNotConfiguredError": "Cloudinary is not configured correctly. Please check environment variables.", "storeNameNotLoadedError": "Store name could not be loaded. Please try again or contact support.", "savingProductTitle": "Saving Product", "savingProductDesc": "Please wait while your product is being saved...", "uploadingProductImagesTitle": "Uploading Product Images", "uploadingProductImagesDesc": "Please wait while product images are uploaded to Cloudinary...", "imageUploadProgressTitle": "Image Upload Progress", "imageUploadProgressDesc": "Uploaded {{current}} of {{total}} images: {{fileName}}", "errorIndividualImageUploadFailed": "Failed to upload image '{{fileName}}': {{message}}", "browseAllProductsTitle": "Browse All Products", "noProductsAvailableTitle": "No Products Available Yet", "noProductsAvailableDesc": "It looks like there are no products listed at the moment. Check back soon!", "errorFetchingProductsGeneral": "An error occurred while fetching products. Please try again later.", "byStore": "By: {{storeName}}", "unknownStore": "Unknown Store", "viewDetails": "View Details", "addToCart": "Add to Cart", "noImageAvailable": "No Image", "signingIn": "Signing in...", "profileSettings": "Profile Settings", "manageYourAccountSettings": "Manage your personal account settings", "subscription": "Subscription", "orders": "orders", "subscriptionInfo": "Subscription Information", "subscriptionNotFound": "Subscription information not found", "subscriptionError": "Subscription Error", "subscriptionErrorMessage": "An error occurred while loading subscription information. Please try again.", "contactSupportForHelp": "Contact support for assistance", "userDocumentNotFound": "User data not found", "accountSetupComplete": "Account setup completed successfully", "customerAccountCreated": "Customer account created and free plan activated automatically", "failedToCreateUserDocument": "Failed to create user data", "retryLoading": "Retry", "contactSupport": "Contact Support", "planFeatures": "Plan Features", "andMoreFeatures": "and {{count}} more features", "viewAllPlans": "View All Plans", "upgradeForMoreFeatures": "Upgrade for more features", "upgradePlan": "Upgrade Plan", "customerUpgradeBenefits": "Get free shipping and exclusive discounts", "merchantUpgradeBenefits": "Get advanced features and comprehensive analytics", "currentPlan": "Current Plan", "availableFeatures": "Available Features", "activePlan": "Active Plan", "defaultPlanInfo": "Default Plan Information", "defaultCustomerPlanMessage": "You are registered as a customer with the basic free plan", "defaultMerchantPlanMessage": "You are registered as a merchant with the basic free plan", "features": "Features", "featuresPageTitle": "Platform Features", "featuresPageSubtitle": "Discover all available features for each type of user", "customerFeaturesTitle": "Customer Features", "merchantFeaturesTitle": "Merchant Features", "allFeatures": "All Features", "featureIncluded": "Included", "featureNotIncluded": "Not Included", "totalFeatures": "Total Features", "loginRequired": "<PERSON><PERSON> Required", "loginToViewSubscription": "Please log in to view subscription information", "loadingSubscriptionInfo": "Loading subscription information...", "subscriptionCheckout": "Subscription Checkout", "completeSubscription": "Complete your subscription to the selected plan", "includedFeatures": "Included Features", "activatePlan": "Activate Plan", "paymentDetails": "Payment Details", "freeActivationMessage": "The free plan will be activated immediately without any charges", "activating": "Activating...", "activateFreePlan": "Activate Free Plan", "subscriptionFor": "Subscription for", "loadingPaymentForm": "Loading payment form...", "confirmPlanActivation": "Confirm Plan Activation", "youWillGet": "You will get:", "freeActivationNote": "The free plan requires no fees and can be cancelled at any time", "activateNow": "Activate Now", "planActivatedSuccessfully": "Plan Activated Successfully", "planActivatedMessage": "{{planName}} plan has been activated successfully", "redirectingToSuccess": "Redirecting to success page...", "recentOrders": "Recent Orders", "recentOrdersDescription": "View your latest orders", "noOrdersYet": "No orders yet", "startShoppingMessage": "Start shopping and enjoy the Mikhla experience", "orderNumber": "Order Number", "date": "Date", "items": "Items", "total": "Total", "viewAllOrders": "View All Orders", "pending": "Pending", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "merchantAccount": "Merchant Account", "customerAccount": "Customer Account", "pleaseWait": "Please wait...", "securePayment": "Secure Payment", "premiumSupport": "Premium Support", "allOrders": "All Orders", "ordersFound": "Found {{count}} orders", "noOrdersDescription": "You haven't received any orders yet. Orders will appear here when customers start ordering from your store.", "backToDashboard": "Back to Dashboard", "representatives": "Representatives", "representativeDashboard": "Representative Dash<PERSON>", "welcomeRepresentative": "Welcome, {{representativeName}}!", "representativeAccount": "Representative Account", "becomeRepresentative": "Become a Representative", "representativeSignup": "Representative <PERSON><PERSON>", "representativeLogin": "Representative <PERSON><PERSON>", "representativeProfile": "Representative Profile", "representativeOrders": "Delivery Orders", "representativeEarnings": "Earnings", "representativeStats": "Representative <PERSON><PERSON>", "representativeSettings": "Representative <PERSON><PERSON><PERSON>", "plan_representative_basic_name": "Basic Plan", "plan_representative_premium_name": "Premium Plan", "unlimitedDeliveries": "Unlimited Deliveries", "basicSupport": "Basic Support", "advancedStats": "Advanced Statistics", "detailedReports": "Detailed Reports", "advancedGpsTracking": "Advanced GPS Tracking", "advancedNotifications": "Advanced Notifications", "dailyPayouts": "Daily Payouts", "weeklyPayouts": "Weekly Payouts", "commission5Percent": "5% Commission", "commission10Percent": "10% Commission", "bonusIncentives": "Bonus Incentives", "flexibleSchedule": "Flexible Schedule", "basicTraining": "Basic Training", "premiumTraining": "Premium Training", "standardPriority": "Standard Priority", "basicEarningsReports": "Basic Earnings Reports", "mobileApp": "Mobile App", "gpsTracking": "GPS Tracking", "customerRatings": "Customer Ratings", "basicNotifications": "Basic Notifications", "upgradeToPremium": "Upgrade to Premium", "orderManagementSubtitle": "Track and manage all your store orders", "reportsSubtitle": "Comprehensive reports and analytics for your store performance", "reportsComingSoonTitle": "Reports and Analytics Under Development", "reportsComingSoonDescription": "We're working on an advanced reporting system that will provide you with comprehensive insights about your store performance:", "reportFeature1": "Daily and monthly sales reports", "reportFeature2": "Best-selling products performance analysis", "reportFeature3": "Customer statistics and purchasing behavior", "reportFeature4": "Profit and revenue reports", "reportFeature5": "Interactive charts and graphs", "reportFeature6": "Export reports in various formats", "newMerchantWelcome": "Welcome as a new merchant! You can start by setting up your store and adding your first products.", "personalInformation": "Personal Information", "identityInformation": "Identity Information", "drivingLicense": "Driving License", "vehicleInformation": "Vehicle Information", "vehicleInspection": "Vehicle Inspection", "documentsUpload": "Documents Upload", "fullName": "Full Name", "phoneNumber": "Phone Number", "nationalId": "National ID", "nationalIdType": "ID Type", "nationalIdCard": "National ID", "residentId": "Resident ID", "licenseNumber": "License Number", "issueDate": "Issue Date", "expiryDate": "Expiry Date", "licenseImage": "License Image", "vehicleType": "Vehicle Type", "vehicleModel": "Vehicle Model", "vehicleYear": "Manufacturing Year", "plateNumber": "Plate Number", "vehicleColor": "Vehicle Color", "vehicleImage": "Vehicle Image", "car": "Car", "motorcycle": "Motorcycle", "bicycle": "Bicycle", "certificateNumber": "Certificate Number", "inspectionImage": "Inspection Certificate Image", "totalDeliveries": "Total Deliveries", "completedDeliveries": "Completed Deliveries", "cancelledDeliveries": "Cancelled Deliveries", "averageRating": "Average Rating", "totalEarnings": "Total Earnings", "monthlyEarnings": "Monthly Earnings", "weeklyEarnings": "Weekly Earnings", "dailyEarnings": "Daily Earnings", "averageDeliveryTime": "Average Delivery Time", "successRate": "Success Rate", "deliveryStatus": "Delivery Status", "assigned": "Assigned", "picked_up": "Picked Up", "in_transit": "In Transit", "pickupLocation": "Pickup Location", "deliveryLocation": "Delivery Location", "deliveryFee": "Delivery Fee", "representativeEarning": "Representative <PERSON><PERSON><PERSON>", "workingHours": "Working Hours", "workingDays": "Working Days", "maxDeliveryRadius": "Max Delivery Radius", "vehicleCapacity": "Vehicle Capacity", "autoAcceptOrders": "Auto Accept Orders", "notificationSettings": "Notification Settings", "newOrders": "New Orders", "orderUpdates": "Order Updates", "earnings": "Earnings", "promotions": "Promotions", "enterYourPersonalInformation": "Enter your personal information", "enterFullName": "Enter full name", "enterPhoneNumber": "Enter phone number", "enterEmail": "Enter email address", "enterYourIdentityInformation": "Enter your identity information", "enterNationalId": "Enter national ID number", "uploadYourDrivingLicense": "Upload your driving license", "enterLicenseNumber": "Enter license number", "enterVehicleDetails": "Enter vehicle details", "enterVehicleModel": "Enter vehicle model", "enterPlateNumber": "Enter plate number", "enterVehicleColor": "Enter vehicle color", "enterCertificateNumber": "Enter certificate number", "reviewAndAcceptTerms": "Review and accept terms and conditions", "representativeTermsTitle": "Representative Terms and Conditions", "representativeTermsIntro": "Welcome to <PERSON><PERSON><PERSON>'s delivery representative team", "eligibilityRequirements": "Eligibility Requirements", "vehicleRequirements": "Vehicle Requirements", "deliveryResponsibilities": "Delivery Responsibilities", "commissionStructure": "Commission Structure", "conductStandards": "Conduct Standards", "terminationConditions": "Termination Conditions", "joinOurDeliveryTeam": "Join our delivery team", "step": "Step", "of": "of", "submitApplication": "Submit Application", "pleaseCompleteAllRequiredFields": "Please complete all required fields", "representativeSignupSuccess": "Registration application submitted successfully! It will be reviewed soon.", "representativeSignupError": "An error occurred during registration. Please try again.", "advancedSearch": "Advanced Search", "searchProductsAndStores": "Search products and stores...", "searchResultsFor": "Search results for", "searchError": "An error occurred while searching", "startSearching": "Start Searching", "enterSearchTermToFindProducts": "Enter search term to find products and stores", "noSearchResults": "No search results", "tryDifferentSearchTerms": "Try different search terms or adjust filters", "clearFiltersAndTryAgain": "Clear filters and try again", "noProductsFound": "No products found", "tryAdjustingFilters": "Try adjusting filters for better results", "relevance": "Relevance", "priceLowToHigh": "Price: Low to High", "priceHighToLow": "Price: High to Low", "highestRated": "Highest Rated", "newest": "Newest", "mostPopular": "Most Popular", "browseByCategories": "Browse by Categories", "discoverProductsInDifferentCategories": "Discover products in different categories", "allCategories": "All Categories", "productsFound": "products found", "productsInCategory": "products in category", "totalProducts": "Total Products", "noProductsInThisCategory": "No products in this category", "trySelectingDifferentCategory": "Try selecting a different category or browse all categories", "checkBackLaterForNewProducts": "Check back later for new products", "browseAllCategories": "Browse All Categories", "food": "Food & Beverages", "groceries": "Groceries & Food Items", "fashion": "Fashion & Clothing", "electronics": "Electronics", "homeAndGarden": "Home & Garden", "beautyAndHealth": "Beauty & Health", "sportsAndFitness": "Sports & Fitness", "automotive": "Automotive", "booksAndMedia": "Books & Media", "artsAndCrafts": "Arts & Crafts", "handicrafts": "Handicrafts & Traditional Crafts", "toys": "Toys & Entertainment", "pets": "Pets", "babyKids": "Baby & Kids", "jewelry": "Jewelry & Accessories", "services": "Local Services", "plants": "Plants & Gardening", "appliances": "Home Appliances", "waterSports": "Water Sports & Beach", "perfumes": "Perfumes & Incense", "tools": "Tools & Equipment", "featuredStores": "Featured Stores", "discoverTopRatedStores": "Discover top-rated stores in your area", "discoverLocalStores": "Discover local stores in your area", "viewAllStores": "View All Stores", "noFeaturedStores": "No featured stores currently", "checkBackLaterForFeaturedStores": "Check back later to see featured stores", "basicInformation": "Basic Information", "enterBasicInformation": "Enter your basic information", "passwordSetup": "Password Setup", "createSecurePassword": "Create a secure password", "accountType": "Account Type", "selectAccountType": "Select your account type", "uploadRequiredDocuments": "Upload required documents", "joinOurCommunity": "Join our community", "createAccount": "Create Account", "enterUsername": "Enter username", "enterPassword": "Enter password", "enterConfirmPassword": "Confirm password", "confirmYourPassword": "Confirm your password", "optionalFileFormatsAllowed": "Optional formats allowed: PDF, JPG, PNG", "fileSelected": "File selected", "errorUsernameRequired": "Username is required", "errorEmailRequired": "Email address is required", "chooseYourAccountType": "Choose your account type", "customerDescription": "Shop from local stores and enjoy a unique shopping experience", "merchantDescription": "Create your online store and start selling your products", "representativeDescription": "Join as a delivery representative and earn extra income", "termsAndConditionsIntro": "Please read and review the following terms and conditions carefully", "userRights": "User Rights", "userResponsibilities": "User Responsibilities", "privacyPolicy": "Privacy Policy", "serviceTerms": "Service Terms", "paymentTerms": "Payment Terms", "readyToStart": "Ready to Start", "proceedToCreateAccount": "Proceed to create your account", "customerAccountSummary": "You'll be able to browse products and shop from local stores", "merchantAccountSummary": "You'll be able to create your store and sell products to customers", "representativeAccountSummary": "You'll be able to join our delivery team and earn additional income", "clickNextToProceed": "Click next to proceed to the detailed registration form", "completeYourRegistration": "Complete your registration", "startRegistration": "Start Registration", "finalStep": "Final Step", "reviewYourInformation": "Review your information", "uploaded": "Uploaded", "notUploaded": "Not uploaded", "clickCreateToFinish": "Click create account to finish registration", "loadingRepresentativeSection": "Loading representative section...", "mustBeLoggedInToAccessRepresentative": "You must be logged in to access the representative section", "loadingRepresentativeData": "Loading representative data...", "noRepresentativeDataFound": "No representative data found", "pleaseCompleteRegistration": "Please complete the registration process", "completeRegistration": "Complete Registration", "checkYourApplicationStatus": "Check your application status", "representativeName": "Representative Name", "reapply": "Reapply", "goToDashboard": "Go to Dashboard", "helpCenter": "Help Center", "suspended": "Suspended", "backToHome": "Back to Home", "subscriptionSuccessful": "Subscription Successful", "subscriptionProcessedSuccessfully": "Your subscription has been processed successfully", "freePlanActivated": "Free Plan Activated", "paidPlanActivated": "Paid Plan Activated", "welcomeToNewPlan": "Welcome to {{planName}}", "paymentId": "Payment ID", "viewProfile": "View Profile", "whatYouGet": "What You Get", "nextSteps": "Next Steps", "completeProfile": "Complete your profile", "exploreDashboard": "Explore the dashboard", "startUsingFeatures": "Start using features", "receiveInvoiceEmail": "You will receive an invoice via email", "thankYou": "Thank You", "subscriptionThankYouMessage": "Thank you for choosing <PERSON><PERSON><PERSON>. We look forward to serving you with our best.", "subscriptionCancelled": "Subscription Cancelled", "subscriptionCancelledMessage": "The subscription process has been cancelled. No amount has been charged to your account.", "noChargesApplied": "No charges have been applied to your account", "tryAgain": "Try Again", "needHelp": "Need Help?", "subscriptionHelpMessage": "If you encountered a problem with the subscription process, you can:", "checkPaymentMethod": "Check your payment method", "tryDifferentPaymentMethod": "Try a different payment method", "checkInternetConnection": "Check your internet connection", "representativeDashboardSubtitle": "Manage your delivery orders and earnings from here", "reviews": {"noReviews": "No reviews yet", "beFirstToReview": "Be the first to review", "helpful": "Helpful", "report": "Report", "confirmDelete": "Are you sure you want to delete this review?", "viewImages": "View Images", "loginToReview": "Login to add a review", "loginToReviewDescription": "You must be logged in to add a review or rating", "alreadyReviewed": "You've already reviewed this", "alreadyReviewedDescription": "You can only add one review per product or store", "cannotReview": "Cannot add review", "cannotReviewDescription": "You must purchase the product first to add a review", "addStoreReview": "Add Store Review", "addProductReview": "Add Product Review", "ratingRequired": "Rating is required", "comment": "Comment", "commentRequired": "Comment is required", "commentTooShort": "Comment is too short (minimum 10 characters)", "commentPlaceholder": "Share your experience with this product or store...", "images": "Images", "optional": "Optional", "uploadImages": "Upload Images", "uploadingImages": "Uploading images...", "imagesUploaded": "Images uploaded successfully", "imageUploadFailed": "Failed to upload images", "maxImagesExceeded": "Maximum number of images exceeded (5 images)", "maxImages": "You can upload {{count}} more images", "minCharacters": "Minimum {{count}} characters", "submitting": "Submitting...", "ratingLabels": {"1": "Very Poor", "2": "Poor", "3": "Average", "4": "Good", "5": "Excellent"}, "reportReview": "Report Review", "reportReviewDescription": "Help us maintain review quality by reporting inappropriate content", "reportReason": "Report Reason", "additionalDetails": "Additional Details", "reportDescriptionPlaceholder": "Explain why you're reporting this review...", "reportWarningTitle": "Important Warning", "reportWarningDescription": "False reporting may result in account suspension. Please ensure your report is accurate.", "submittingReport": "Submitting report...", "submitReport": "Submit Report", "reportReasons": {"spam": "Spam", "spamDescription": "Unwanted promotional or repetitive content", "inappropriate": "Inappropriate Content", "inappropriateDescription": "Offensive or inappropriate content that violates guidelines", "fake": "Fake Review", "fakeDescription": "Fake or fraudulent review from a fake account", "offensive": "Offensive Content", "offensiveDescription": "Offensive, inflammatory, or discriminatory language", "other": "Other", "otherDescription": "Another reason not listed above"}, "ratingsAndReviews": "Ratings & Reviews", "noRatings": "No ratings yet", "noRatingsDescription": "Be the first to rate this product or store", "totalReviews": "{{count}} reviews", "verifiedReviews": "{{count}} verified reviews", "recentReviews": "Recent Reviews", "ratingDistribution": "Rating Distribution", "verifiedPercentage": "Verified", "positivePercentage": "Positive", "qualityScore": "Quality Score", "qualityScoreDescription": "Comprehensive indicator of product or store quality", "excellent": "Excellent", "veryGood": "Very Good", "good": "Good", "average": "Average", "poor": "Poor", "reviewImages": "Review Images", "imageNavigationHint": "Use arrows or click thumbnails to navigate", "shareYourExperience": "Share Your Experience", "helpOthersWithYourReview": "Help others with your product review", "helpOthersWithYourStoreReview": "Help others with your store review", "customerReviews": "Customer Reviews", "reviewGuidelines": "Review Guidelines", "helpfulReviewTips": "Tips for helpful reviews", "tip1": "Be honest and detailed about your experience", "tip2": "Mention both positives and negatives", "tip3": "Add photos if possible", "tip4": "Be respectful in your comments", "helpfulStoreReviewTips": "Tips for store reviews", "storeTip1": "Rate service quality and products", "storeTip2": "Mention delivery speed and handling", "storeTip3": "Rate store cleanliness and organization", "storeTip4": "Share your customer service experience", "communityGuidelines": "Community Guidelines", "guidelinesDescription": "We are committed to maintaining a respectful and helpful community. Please follow community guidelines when writing reviews.", "viewAllReviews": "View All Reviews", "positiveReviews": "Positive Reviews", "stars": "stars"}, "minutes": "minutes", "currentStatus": "Current Status", "workingStatus": "Working Status", "commissionRate": "Commission Rate", "recentActivity": "Recent Activity", "recentActivitySubtitle": "Latest orders and activities", "noRecentActivity": "No recent activity", "startAcceptingOrders": "Start accepting orders to see activity here", "representativeApprovals": "Representative <PERSON><PERSON><PERSON><PERSON>", "reviewAndApproveRepresentatives": "Review and approve representative applications", "loadingRepresentatives": "Loading representatives...", "noPendingRepresentatives": "No pending applications", "allRepresentativesReviewed": "All representative applications have been reviewed", "submittedOn": "Submitted on", "showDetails": "Show Details", "hideDetails": "Hide Details", "viewLicense": "View License", "viewCertificate": "View Certificate", "reviewNotes": "Review Notes", "addReviewNotes": "Add review notes (optional)", "approve": "Approve", "reject": "Reject", "representativeApproved": "Representative approved successfully", "representativeRejected": "Representative rejected", "errorFetchingRepresentatives": "Error fetching representatives data", "errorUpdatingRepresentative": "Error updating representative status", "representativePlansTitle": "Representative Plans", "joinDeliveryTeamSubtitle": "Join our delivery team and earn flexible income", "representativeBenefitsTitle": "Benefits of working as a representative:", "flexibleWorkingHours": "Flexible working hours", "competitiveCommissions": "Competitive commissions", "weeklyPayments": "Weekly payments", "comprehensiveSupport": "Comprehensive ongoing support", "joinDeliveryTeam": "Join Delivery Team", "pleaseReadTermsCarefully": "Please read the terms and conditions carefully", "acceptTerms": "Accept Terms", "showing": "Showing", "continueWithGoogle": "Continue with Google", "googleSignInFailed": "Google sign-in failed. Please try again.", "googleSignInCancelled": "Google sign-in was cancelled.", "googleSignInPopupBlocked": "Sign-in popup was blocked. Please allow popups and try again.", "orContinueWith": "Or continue with", "orSigninManually": "Or sign in manually", "orCreateManually": "Or create account manually", "selectUserType": "Select Account Type", "selectUserTypeSubtitle": "To start creating your account, first, tell us about the type of account you want", "welcomeToMikhla": "Welcome to Mikhla", "continueAsCustomer": "Continue as Customer", "continueAsMerchant": "Continue as Merchant", "continueAsRepresentative": "Continue as Representative", "chooseAccountType": "Choose your account type", "accountTypeRequired": "Account type selection is required", "userTypeSelectionTitle": "Welcome to Mikhla", "userTypeSelectionSubtitle": "Let's start by creating your account. First, tell us about the type of account you want", "connectionTimeout": "Connection timeout", "retryingConnection": "Retrying connection...", "googleSignInCustomersOnly": "Google Sign-In is available for customers only.", "merchantGoogleSignupNotice": "Google Sign-Up is not available for merchants", "representativeGoogleSignupNotice": "Google Sign-Up is not available for representatives", "merchantManualSignupRequired": "Merchant registration requires filling out a detailed form and uploading required documents. Please use manual registration.", "representativeManualSignupRequired": "Representative registration requires filling out a detailed form and uploading required documents. Please use manual registration.", "signupManually": "Sign Up Manually", "redirectingPleaseWait": "Redirecting, please wait...", "authenticationInProgress": "Authentication in progress...", "processingRequest": "Processing your request...", "signupSuccessful": "Registration successful!", "redirectingToYourAccount": "Redirecting you to your account...", "errorLoadingTerms": "An error occurred while loading terms and conditions", "signupErrorTitle": "Account Creation Failed", "signupErrorMessage": "An error occurred while creating your account. Please try again later.", "merchantApproval": "Merchant Approval", "pendingApproval": "Pending Approval", "approvalPending": "Your application is under review", "approvalApproved": "Your application has been approved!", "approvalRejected": "Your application has been rejected", "approvalPendingDesc": "We are reviewing your merchant application. You will be notified of the result soon.", "approvalApprovedDesc": "Congratulations! Your application has been approved and your store is now active. You can now access the dashboard.", "approvalRejectedDesc": "Your application has been rejected. Please review the requirements and try again.", "submissionDate": "Submission Date", "reviewDate": "Review Date", "applicationInfo": "Application Information", "merchantName": "Merchant Name", "viewFile": "View File", "submitNewApplication": "Submit New Application", "interactiveMap": "Interactive Map", "findNearbyStores": "Find nearby stores", "detectingLocation": "Detecting your location", "detectingLocationDesc": "We are detecting your location to show nearby stores", "enableLocation": "Enable Location", "locationDetected": "Location Detected", "locationError": "Location Error", "searchStores": "Search stores...", "filters": "Filters", "clearFilters": "Clear Filters", "storesMap": "Stores Map", "storesFound": "Found {{count}} stores", "locationRequired": "Location Required", "enableLocationToSeeMap": "Enable location to see the map", "nearbyStores": "Nearby Stores", "noStoresFound": "No stores found", "tryDifferentFilters": "Try different filters", "open": "Open", "closed": "Closed", "km": "km", "today": "Today", "website": "Website", "visitStore": "Visit Store", "directions": "Directions", "distanceCalculator": "Distance Calculator", "distanceTo": "Distance to", "walking": "Walking", "cycling": "Cycling", "driving": "Driving", "getDirections": "Get Directions", "note": "Note", "estimatedTimesNote": "Estimated times are approximate and may vary based on road conditions and weather", "errorLoadingStores": "Error loading stores", "checkout": "Checkout", "completeYourOrder": "Complete Your Order", "customerInformation": "Customer Information", "shippingAddress": "Shipping Address", "streetAddress": "Street Address", "city": "City", "postalCode": "Postal Code", "deliveryNotes": "Delivery Notes", "deliveryNotesPlaceholder": "Special delivery instructions (optional)", "paymentMethod": "Payment Method", "cashOnDelivery": "Cash on Delivery", "creditCard": "Credit Card", "onlinePayment": "Online Payment", "orderNotes": "Order Notes", "orderNotesPlaceholder": "Additional notes for your order (optional)", "orderSummary": "Order Summary", "subtotal": "Subtotal", "shippingFee": "Shipping Fee", "tax": "Tax", "discount": "Discount", "grandTotal": "Grand Total", "placeOrder": "Place Order", "processingOrder": "Processing Order", "orderPlaced": "Order Placed", "orderConfirmation": "Order Confirmation", "thankYouForOrder": "Thank you for your order!", "estimatedDelivery": "Estimated Delivery", "trackOrder": "Track Order", "continueShopping": "Continue Shopping", "orderDetails": "Order Details", "billingAddress": "Billing Address", "sameAsShipping": "Same as shipping address", "differentBillingAddress": "Use different billing address", "products": "Products", "allProducts": "All Products", "productDetails": "Product Details", "lowStock": "Low Stock", "cart": "<PERSON><PERSON>", "searchPlaceholder": "Search for products and stores...", "searching": "Searching...", "recentSearches": "Recent Searches", "clearAll": "Clear All", "trendingSearches": "Trending Searches", "noSuggestions": "No suggestions", "searchResults": "Search Results", "noStoresAvailable": "No stores available currently", "errorFetchingStores": "Error fetching stores", "noProductsInStore": "No products in store", "storeHasNoProducts": "This store currently has no products", "errorFetchingStore": "Error fetching store data", "allStores": "All Stores", "quantity": "Quantity", "freeDeliveryNotice": "Add {{amount}} SAR more for free delivery", "secureCheckoutNotice": "Your payment information is secure and encrypted", "orderSuccessTitle": "Order Placed Successfully", "orderSuccessMessage": "Your order has been placed successfully and will be processed soon", "viewMyOrders": "View My Orders", "orderCreationFailed": "Failed to create order. Please try again", "stores": "Stores", "map": "Map", "storeDetails": "Store Details", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "reviewsComingSoon": "Reviews Coming Soon", "reviewsFeatureWillBeAvailable": "The reviews feature will be available soon", "sortBy": "Sort By", "categories": "Categories", "priceRange": "Price Range", "rating": "Rating", "distance": "Distance", "storeStatus": "Store Status", "openNow": "Open Now", "closedNow": "Closed Now", "anyTime": "Any Time", "within1km": "Within 1 km", "within5km": "Within 5 km", "within10km": "Within 10 km", "within50km": "Within 50 km", "anyDistance": "Any Distance", "4starsAndUp": "4 stars and up", "3starsAndUp": "3 stars and up", "2starsAndUp": "2 stars and up", "1starAndUp": "1 star and up", "anyRating": "Any Rating", "applyFilters": "Apply Filters", "resetFilters": "Reset Filters", "filtersApplied": "Filters Applied", "showingResults": "Showing {{start}}-{{end}} of {{total}} results", "loadMore": "Load More", "endOfResults": "End of results", "noResultsFound": "No results found", "adjustFiltersForBetterResults": "Try adjusting your filters for better results", "sortByRelevance": "Relevance", "sortByPriceLow": "Price: Low to High", "sortByPriceHigh": "Price: High to Low", "sortByRating": "Rating", "sortByDistance": "Distance", "sortByNewest": "Newest", "fourStarsAndUp": "4 stars and up", "threeStarsAndUp": "3 stars and up", "twoStarsAndUp": "2 stars and up", "oneStarAndUp": "1 star and up", "oneKm": "1 km", "fiveKm": "5 km", "tenKm": "10 km", "twentyFiveKm": "25 km", "fiftyKm": "50 km", "common": {"loading": "Loading...", "loadMore": "Load More", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "close": "Close", "search": "Search", "filter": "Filter", "sort": "Sort", "price": "Price", "category": "Category", "store": "Store", "product": "Product", "stock": "Stock", "available": "Available", "outOfStock": "Out of Stock", "verified": "Verified", "active": "Active", "inactive": "Inactive", "status": "Status", "phone": "Phone", "email": "Email", "address": "Address", "businessHours": "Business Hours", "storeInfo": "Store Information", "productInfo": "Product Information", "storeStats": "Store Statistics", "totalOrders": "Total Orders", "totalSales": "Total Sales", "sar": "SAR", "productNotFound": "Product Not Found", "productNotFoundDescription": "The requested product was not found", "storeNotFound": "Store Not Found", "storeNotFoundDescription": "The requested store was not found", "backToProducts": "Back to Products", "backToStores": "Back to Stores", "backToProduct": "Back to Product", "backToStore": "Back to Store"}, "admin": {"reviewReports": "Review Reports", "reviewReportsDescription": "Review and handle reports submitted about reviews", "noReportsFound": "No pending reports", "noReportsFoundDescription": "All reports have been reviewed or there are no new reports", "pendingReports": "Pending Reports", "reporter": "Reporter", "reason": "Reason", "reviewContent": "Review Content", "reportDate": "Report Date", "actions": "Actions", "review": "Review", "by": "by", "reviewNotFound": "Review not found", "reviewReport": "Review Report", "reviewReportDescription": "Review the report and take appropriate action", "reportDetails": "Report Details", "description": "Description", "reportedReview": "Reported Review", "moderationNotes": "Moderation Notes", "moderationNotesPlaceholder": "Add notes about the moderation decision...", "moderationWarning": "Important Warning", "moderationWarningDescription": "Moderation decisions affect user experience. Make sure to make the right decision.", "processing": "Processing...", "approveReview": "Approve Review", "rejectReview": "Reject Review", "reportApproved": "Review approved and report closed", "reportRejected": "Review rejected and hidden", "errorFetchingReports": "Error fetching reports", "errorResolvingReport": "Error processing report"}, "popularProducts": "Popular Products", "discoverBestSellingProducts": "Discover the most popular and best-selling products", "viewAllProducts": "View All Products", "noPopularProducts": "No popular products currently", "checkBackLaterForPopularProducts": "Check back later to see popular products", "searchFilters": "Search Filters", "filterByCategory": "Filter by Category", "viewStore": "View Store", "contactAvailable": "Contact Available", "websiteAvailable": "Website Available", "addReview": "Add Review", "writeReview": "Write Review", "rateProduct": "Rate Product", "addToWishlist": "Add to Wishlist", "removeFromCart": "Remove from Cart", "updateQuantity": "Update Quantity", "notifications": "Notifications", "markAsRead": "<PERSON> <PERSON>", "faq": "FAQ", "reportIssue": "Report Issue", "representative.dashboard.welcome": "Welcome, {{name}}!", "representative.dashboard.subtitle": "Manage delivery orders and earnings from here", "representative.dashboard.completed": "Completed", "representative.dashboard.onTimeRate": "On-time delivery rate", "representative.dashboard.avgDeliveryTime": "Average delivery time", "representative.dashboard.loading": "Loading...", "representative.dashboard.representative": "Representative", "representative.dashboard.active": "Active", "representative.dashboard.inactive": "Inactive", "representative.dashboard.available": "Available", "representative.dashboard.unavailable": "Unavailable", "representative.dashboard.totalDeliveries": "Total Deliveries", "representative.dashboard.monthlyEarnings": "Monthly Earnings", "representative.dashboard.totalEarnings": "Total Earnings", "representative.dashboard.averageRating": "Average Rating", "representative.dashboard.reviews": "Reviews", "representative.dashboard.minutes": "Minutes", "representative.dashboard.mustBeLoggedIn": "You must be logged in as a representative to access this section", "representative.nav.dashboard": "Dashboard", "representative.nav.orders": "Orders", "representative.nav.earnings": "Earnings", "representative.nav.profile": "Profile", "auth.login": "<PERSON><PERSON>", "unknownStatus": "Unknown Status", "unknownStatusDesc": "An error occurred while determining your application status", "previous": "Previous", "next": "Next", "warning": "Warning", "closedToday": "Closed Today", "hoursNotAvailable": "Hours Not Available", "following": "Following", "reviews.noReviews": "No reviews", "reviews.beFirstToReview": "Be the first to review", "reviews.helpful": "Helpful", "reviews.report": "Report", "reviews.confirmDelete": "Confirm Delete", "reviews.viewImages": "View Images", "reviews.loginToReview": "Login to Review", "reviews.loginToReviewDescription": "You must be logged in to add a review", "reviews.alreadyReviewed": "Already Reviewed", "reviews.alreadyReviewedDescription": "You have already reviewed this product", "reviews.cannotReview": "Cannot Review", "reviews.cannotReviewDescription": "You cannot review this item", "reviews.addStoreReview": "Add Store Review", "reviews.addProductReview": "Add Product Review", "reviews.ratingRequired": "Rating is required", "reviews.comment": "Comment", "reviews.commentRequired": "Comment is required", "reviews.commentTooShort": "Comment is too short", "reviews.commentPlaceholder": "Share your experience...", "reviews.images": "Images", "reviews.addImages": "Add Images", "reviews.maxImages": "Maximum {{count}} images", "reviews.submitReview": "Submit Review", "reviews.editReview": "Edit Review", "reviews.deleteReview": "Delete Review", "reviews.reviewSubmitted": "Review Submitted", "reviews.reviewUpdated": "Review Updated", "reviews.reviewDeleted": "Review Deleted", "reviews.loadingReviews": "Loading Reviews...", "reviews.noReviewsYet": "No reviews yet", "reviews.writeFirstReview": "Write the first review", "map.loading": "Loading Map...", "map.error": "Map Error", "map.noLocation": "No Location", "map.enableLocation": "Enable Location", "map.locationDenied": "Location Access Denied", "map.locationUnavailable": "Location Unavailable", "map.findStores": "Find Stores", "map.nearbyStores": "Nearby Stores", "map.storeDetails": "Store Details", "map.getDirections": "Get Directions", "map.distance": "Distance", "map.estimatedTime": "Estimated Time", "stores.featured": "Featured Stores", "stores.popular": "Popular Stores", "stores.newest": "Newest Stores", "stores.topRated": "Top Rated Stores", "stores.openNow": "Open Now", "stores.closedNow": "Closed Now", "stores.deliveryAvailable": "Delivery Available", "stores.pickupOnly": "Pickup Only", "stores.freeDelivery": "Free Delivery", "stores.fastDelivery": "Fast Delivery", "stores.viewMenu": "View Menu", "stores.orderOnline": "Order Online", "stores.callStore": "Call Store", "stores.storeInfo": "Store Information", "stores.workingHours": "Working Hours", "stores.contactInfo": "Contact Information", "stores.socialMedia": "Social Media", "products.featured": "Featured Products", "products.bestsellers": "Bestsellers", "products.newArrivals": "New Arrivals", "products.onSale": "On Sale", "products.recommended": "Recommended", "products.relatedProducts": "Related Products", "products.productDetails": "Product Details", "products.specifications": "Specifications", "products.ingredients": "Ingredients", "products.nutritionFacts": "Nutrition Facts", "products.allergenInfo": "Allergen Information", "products.storageInstructions": "Storage Instructions", "products.usageInstructions": "Usage Instructions", "products.warranty": "Warranty", "products.returnPolicy": "Return Policy", "orders.orderHistory": "Order History", "orders.currentOrders": "Current Orders", "orders.pastOrders": "Past Orders", "orders.orderDetails": "Order Details", "orders.orderSummary": "Order Summary", "orders.orderItems": "Order Items", "orders.orderTotal": "Order Total", "orders.orderStatus": "Order Status", "orders.trackOrder": "Track Order", "orders.cancelOrder": "Cancel Order", "orders.reorder": "Reorder", "orders.orderConfirmation": "Order Confirmation", "orders.estimatedDelivery": "Estimated Delivery", "orders.deliveryAddress": "Delivery Address", "orders.paymentMethod": "Payment Method", "orders.orderNotes": "Order Notes", "payment.paymentMethods": "Payment Methods", "payment.creditCard": "Credit Card", "payment.debitCard": "Debit Card", "payment.cashOnDelivery": "Cash on Delivery", "payment.digitalWallet": "Digital Wallet", "payment.bankTransfer": "Bank Transfer", "payment.paymentProcessing": "Processing Payment...", "payment.paymentSuccess": "Payment Successful", "payment.paymentFailed": "Payment Failed", "payment.paymentCancelled": "Payment Cancelled", "payment.refundProcessing": "Processing Refund...", "payment.refundCompleted": "Refund Completed", "payment.billingAddress": "Billing Address", "payment.securePayment": "Secure Payment", "delivery.deliveryOptions": "Delivery Options", "delivery.standardDelivery": "Standard Delivery", "delivery.expressDelivery": "Express Delivery", "delivery.sameDay": "Same Day Delivery", "delivery.nextDay": "Next Day Delivery", "delivery.scheduled": "Scheduled Delivery", "delivery.pickup": "Pickup", "delivery.deliveryFee": "Delivery Fee", "delivery.freeDelivery": "Free Delivery", "delivery.deliveryTime": "Delivery Time", "delivery.deliveryAddress": "Delivery Address", "delivery.deliveryInstructions": "Delivery Instructions", "delivery.contactlessDelivery": "Contactless Delivery", "delivery.trackDelivery": "Track Delivery", "delivery.deliveryStatus": "Delivery Status", "delivery.outForDelivery": "Out for Delivery", "delivery.delivered": "Delivered", "delivery.deliveryConfirmation": "Delivery Confirmation", "follow": "Follow", "share": "Share", "aboutStore": "About Store", "noDescriptionAvailable": "No description available", "trending": "Trending", "location": "Location", "errorFetchingProduct": "Error fetching product", "inStock": "In Stock", "emptyCart": "Cart is empty", "emptyCartDescription": "You haven't added any products to your cart yet", "startShopping": "Start Shopping", "loadingCart": "Loading cart...", "proceedToCheckout": "Proceed to Checkout", "secureCheckout": "Secure and protected payment", "freeDeliveryThreshold": "Free delivery for orders over {{amount}}", "representative.dashboard": "Representative Dash<PERSON>", "representative.orders": "Orders", "representative.earnings": "Earnings", "representative.profile": "Profile", "representative.acceptOrder": "Accept Order", "representative.rejectOrder": "Reject Order", "representative.completeOrder": "Complete Order", "representative.totalEarnings": "Total Earnings", "representative.todayEarnings": "Today's Earnings", "representative.pendingOrders": "Pending Orders", "representative.completedOrders": "Completed Orders", "submitReview": "Submit Review", "reviewSubmitted": "Review Submitted", "thankYouForReview": "Thank you for your review", "orderStatus": "Order Status", "orderTotal": "Order Total", "orderDate": "Order Date", "payment": "Payment", "paymentStatus": "Payment Status", "paymentSuccessful": "Payment Successful", "paymentFailed": "Payment Failed", "shipping": "Shipping", "shippingMethod": "Shipping Method", "shippingCost": "Shipping Cost", "freeShipping": "Free Shipping", "settings": "Settings", "accountSettings": "Account <PERSON><PERSON>", "privacySettings": "Privacy Settings", "error": "Error", "errorOccurred": "An error occurred", "somethingWentWrong": "Something went wrong", "navigation": "Navigation", "menu": "<PERSON><PERSON>", "reports": "Reports", "analytics": "Analytics", "integrations.title": "Integration Settings", "integrations.description": "Connect your store with external ERP and POS systems", "integrations.erp.title": "ERP Systems", "integrations.pos.title": "POS Systems", "integrations.addErp": "Add ERP Integration", "integrations.addPos": "Add POS Integration", "integrations.testConnection": "Test Connection", "integrations.sync": "Sync", "integrations.delete": "Delete", "integrations.status.connected": "Connected", "integrations.status.disconnected": "Disconnected", "integrations.status.error": "Error", "integrations.status.syncing": "Syncing", "integrations.lastSync": "Last Sync", "integrations.neverSynced": "Never", "integrations.syncInterval": "Sync Interval", "integrations.minutes": "minutes", "integrations.enabled": "Enabled", "integrations.disabled": "Disabled", "integrations.products": "Products", "integrations.inventory": "Inventory", "integrations.orders": "Orders", "integrations.customers": "Customers", "integrations.sales": "Sales", "integrations.payments": "Payments", "integrations.accounting": "Accounting", "integrations.noErpIntegrations": "No ERP Integrations", "integrations.noPosIntegrations": "No POS Integrations", "integrations.addFirstErp": "Start by adding an ERP integration to sync your data", "integrations.addFirstPos": "Start by adding a POS integration to sync your sales", "integrations.form.systemType": "System Type", "integrations.form.systemName": "System Name", "integrations.form.apiUrl": "API URL", "integrations.form.apiKey": "API Key", "integrations.form.username": "Username", "integrations.form.password": "Password", "integrations.form.database": "Database", "integrations.form.accessToken": "Access Token", "integrations.form.storeId": "Store ID", "integrations.form.locationId": "Location ID", "integrations.form.environment": "Environment", "integrations.form.sandbox": "Sandbox", "integrations.form.production": "Production", "integrations.form.syncSettings": "Sync Settings", "integrations.form.autoSync": "Auto Sync", "integrations.form.create": "Create Integration", "integrations.form.cancel": "Cancel", "integrations.errors.createFailed": "Failed to create integration", "integrations.errors.testFailed": "Failed to test connection", "integrations.errors.syncFailed": "Failed to sync data", "integrations.errors.deleteFailed": "Failed to delete integration", "integrations.errors.loadFailed": "Failed to load integration data", "integrations.confirmDelete": "Are you sure you want to delete this integration?", "aiDocumentProcessing": {"title": "AI Document Processing System", "subtitle": "Upload your documents and get intelligent automatic approval within minutes using the latest AI technologies", "upload": {"title": "Upload Documents", "dragAndDrop": "Drag files here or click to select", "dragActive": "Drop files here...", "supportedFormats": "Supported formats: JPG, PNG, WebP, HEIC, PDF", "maxSize": "Maximum: 10MB per file", "qualityTip": "For best results, ensure documents are clear and readable, with good lighting and no text cutoff.", "uploadedFiles": "Uploaded Files", "upload": "Upload", "retry": "Retry", "remove": "Remove"}, "status": {"pending": "Pending", "uploading": "Uploading...", "processing": "Processing...", "completed": "Completed", "error": "Error", "failed": "Failed", "requiresReupload": "Requires Reupload"}, "stages": {"ocr": "Extract text from document", "ner": "Extract data and information", "classification": "Classify document and make decision", "completed": "Processing completed"}, "results": {"title": "Analysis Results", "decision": {"approve": "Approved", "reject": "Rejected", "manualReview": "Requires Manual Review"}, "documentType": "Document Type", "confidence": "Overall Confidence", "riskScore": "Risk Score", "reasons": "Decision Reasons", "extractedData": "Extracted Data", "processingTime": "Processing Time"}, "quality": {"title": "Quality Report", "imageQuality": "Image Quality", "resolution": "Resolution Sufficient", "format": "Format Supported", "blur": "No Blur Detected", "yes": "Yes", "no": "No"}, "errors": {"fileTooBig": "File too large. Maximum 10MB", "unsupportedFormat": "Unsupported file type", "uploadFailed": "Failed to upload file", "processingFailed": "Failed to process document", "lowQuality": "File quality insufficient or format not supported", "unauthorized": "Unauthorized access", "notFound": "Document not found", "serverError": "Server error"}, "features": {"security": {"title": "High Security", "description": "Secure document processing with personal data protection"}, "speed": {"title": "Fast Processing", "description": "Instant results within minutes"}, "accuracy": {"title": "High Accuracy", "description": "Advanced AI technologies to ensure accurate results"}}, "tabs": {"upload": "Upload Documents", "howItWorks": "How It Works", "requirements": "Requirements", "results": "Results", "stages": "Stages", "data": "Data", "quality": "Quality"}, "history": {"title": "Document Processing History", "total": "Total Documents", "completed": "Completed", "processing": "Processing", "failed": "Failed", "averageConfidence": "Average Confidence", "averageTime": "Average Time", "filters": "Filters and Search", "search": "Search", "searchPlaceholder": "File name...", "status": "Status", "allStatuses": "All Statuses", "date": "Date", "allDates": "All Dates", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "fileName": "File", "result": "Result", "confidence": "Confidence", "time": "Time", "createdAt": "Date", "actions": "Actions", "view": "View", "download": "Download", "noDocuments": "No processed documents yet", "startFirst": "Start by uploading your first document"}}}