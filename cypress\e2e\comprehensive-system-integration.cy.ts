/// <reference types="cypress" />

/**
 * اختبارات التكامل الشامل للنظام
 * تختبر التكامل بين جميع الأنظمة: ERP/POS، CRM، الكوبونات، الولاء، ولوحات التحكم
 */

describe('🔗 التكامل الشامل للنظام', () => {
  beforeEach(() => {
    // إعداد جميع البيانات الوهمية
    cy.mockFirebaseAuth()
    cy.mockERPIntegration()
    cy.mockCRMCustomers()
    cy.mockCoupons()
    cy.mockLoyaltyProgram()
  })

  afterEach(() => {
    cy.mockLogout()
  })

  describe('🔄 سيناريو التكامل الكامل - رحلة العميل', () => {
    it('يجب أن يكمل رحلة عميل شاملة من التسجيل إلى الولاء', () => {
      // 1. تسجيل عميل جديد
      cy.visitWithLocale('/signup')
      cy.waitForLoadingToFinish()
      
      cy.fillForm({
        'first-name': 'أحمد',
        'last-name': 'محمد',
        'email': '<EMAIL>',
        'phone': '+966501234567',
        'password': 'password123',
        'confirm-password': 'password123'
      })
      
      cy.get('[data-testid="signup-btn"]').click()
      cy.get('[data-testid="success-message"]').should('be.visible')
      
      // 2. تسجيل الدخول
      cy.mockLogin('customer')
      cy.visitWithLocale('/dashboard')
      cy.waitForLoadingToFinish()
      
      // 3. تصفح المنتجات وإضافة للسلة
      cy.visitWithLocale('/products')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="product-card"]').first().click()
      cy.get('[data-testid="add-to-cart"]').click()
      cy.get('[data-testid="cart-success"]').should('be.visible')
      
      // 4. تطبيق كوبون خصم
      cy.visitWithLocale('/checkout')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="coupon-input"]').type('SAVE20')
      cy.get('[data-testid="apply-coupon"]').click()
      cy.get('[data-testid="discount-applied"]').should('be.visible')
      
      // 5. إكمال الطلب
      cy.fillForm({
        'shipping-address': 'شارع الملك فهد، الرياض',
        'payment-method': 'credit_card'
      })
      
      cy.get('[data-testid="place-order"]').click()
      cy.get('[data-testid="order-success"]').should('be.visible')
      
      // 6. كسب نقاط الولاء
      cy.get('[data-testid="loyalty-points-earned"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة نقاط الولاء')
      
      // 7. التحقق من تحديث CRM
      cy.mockLogout()
      cy.mockLogin('merchant')
      cy.visitWithLocale('/merchant/crm')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="customer-search"]').type('أحمد')
      cy.get('[data-testid="search-btn"]').click()
      cy.get('[data-testid="customer-row"]').should('contain.text', 'أحمد')
      
      // 8. التحقق من مزامنة ERP
      cy.visitWithLocale('/merchant/integrations')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="erp-sync-status"]').should('contain.text', 'مزامن')
      cy.get('[data-testid="last-sync-time"]').should('be.visible')
    })
  })

  describe('🏪 سيناريو التاجر الشامل', () => {
    it('يجب أن يدير التاجر جميع جوانب متجره', () => {
      // 1. تسجيل دخول التاجر
      cy.mockLogin('merchant')
      cy.visitWithLocale('/merchant/dashboard')
      cy.waitForLoadingToFinish()
      
      // 2. إعداد تكامل ERP
      cy.visitWithLocale('/merchant/integrations')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="add-erp-integration"]').click()
      cy.fillForm({
        'system-type': 'sap',
        'system-name': 'SAP Production',
        'api-url': 'https://sap.company.com',
        'api-key': 'test-key-123'
      })
      
      cy.get('[data-testid="test-connection"]').click()
      cy.get('[data-testid="connection-success"]').should('be.visible')
      cy.get('[data-testid="save-integration"]').click()
      
      // 3. إنشاء كوبون ترويجي
      cy.visitWithLocale('/merchant/coupons')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="create-coupon-btn"]').click()
      cy.fillForm({
        'coupon-code': 'NEWCUSTOMER',
        'coupon-value': '15',
        'usage-limit': '100'
      })
      
      cy.get('[data-testid="coupon-type"]').select('percentage')
      cy.get('[data-testid="save-coupon"]').click()
      cy.get('[data-testid="success-message"]').should('be.visible')
      
      // 4. إعداد برنامج الولاء
      cy.visitWithLocale('/merchant/loyalty')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="program-settings"]').click()
      cy.fillForm({
        'points-per-riyal': '2',
        'minimum-redemption': '100'
      })
      
      cy.get('[data-testid="save-settings"]').click()
      cy.get('[data-testid="success-message"]').should('be.visible')
      
      // 5. إضافة منتج جديد
      cy.visitWithLocale('/merchant/products')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="add-product"]').click()
      cy.fillForm({
        'product-name-ar': 'منتج تجريبي',
        'product-name-en': 'Test Product',
        'product-price': '500',
        'product-stock': '100'
      })
      
      cy.get('[data-testid="save-product"]').click()
      cy.get('[data-testid="success-message"]').should('be.visible')
      
      // 6. مراجعة التحليلات
      cy.visitWithLocale('/merchant/reports')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="sales-report"]').should('be.visible')
      cy.get('[data-testid="customer-analytics"]').should('be.visible')
      cy.get('[data-testid="product-performance"]').should('be.visible')
    })
  })

  describe('🛡️ سيناريو المدير الشامل', () => {
    it('يجب أن يدير المدير جميع جوانب النظام', () => {
      // 1. تسجيل دخول المدير
      cy.mockLogin('representative')
      cy.visitWithLocale('/admin/dashboard')
      cy.waitForLoadingToFinish()
      
      // 2. مراجعة إحصائيات النظام
      cy.get('[data-testid="total-users"]').should('be.visible')
      cy.get('[data-testid="total-merchants"]').should('be.visible')
      cy.get('[data-testid="total-orders"]').should('be.visible')
      cy.get('[data-testid="system-health"]').should('be.visible')
      
      // 3. الموافقة على طلب تاجر
      cy.visitWithLocale('/admin/merchant-approvals')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="merchant-request-row"]').first().within(() => {
        cy.get('[data-testid="approve-merchant"]').click()
      })
      
      cy.get('[data-testid="approval-notes"]').type('تم مراجعة الطلب والموافقة')
      cy.get('[data-testid="confirm-approval"]').click()
      cy.get('[data-testid="success-message"]').should('be.visible')
      
      // 4. إدارة المستخدمين
      cy.visitWithLocale('/admin/users')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="users-table"]').should('be.visible')
      cy.get('[data-testid="user-search"]').type('أحمد')
      cy.get('[data-testid="search-btn"]').click()
      cy.get('[data-testid="user-row"]').should('contain.text', 'أحمد')
      
      // 5. إضافة فئة جديدة
      cy.visitWithLocale('/admin/categories')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="add-category"]').click()
      cy.fillForm({
        'category-name-ar': 'فئة تجريبية',
        'category-name-en': 'Test Category',
        'category-description-ar': 'وصف الفئة التجريبية'
      })
      
      cy.get('[data-testid="save-category"]').click()
      cy.get('[data-testid="success-message"]').should('be.visible')
      
      // 6. تحديث إعدادات النظام
      cy.visitWithLocale('/admin/settings')
      cy.waitForLoadingToFinish()
      
      cy.fillForm({
        'site-name': 'مِخْلاة - منصة التجارة الإلكترونية',
        'contact-email': '<EMAIL>'
      })
      
      cy.get('[data-testid="save-general-settings"]').click()
      cy.get('[data-testid="success-message"]').should('be.visible')
    })
  })

  describe('🔄 اختبار التكامل بين الأنظمة', () => {
    it('يجب أن تتكامل أنظمة CRM والولاء والكوبونات', () => {
      // 1. إنشاء عميل في CRM
      cy.mockLogin('merchant')
      cy.visitWithLocale('/merchant/crm')
      cy.waitForLoadingToFinish()
      
      // 2. إضافة العميل لتقسيم VIP
      cy.get('[data-testid="customer-row"]').first().click()
      cy.get('[data-testid="add-tag-btn"]').click()
      cy.get('[data-testid="tag-input"]').type('vip')
      cy.get('[data-testid="add-tag"]').click()
      
      // 3. إنشاء كوبون خاص لعملاء VIP
      cy.visitWithLocale('/merchant/coupons')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="create-coupon-btn"]').click()
      cy.fillForm({
        'coupon-code': 'VIP20',
        'coupon-value': '20',
        'customer-segment': 'vip'
      })
      
      cy.get('[data-testid="save-coupon"]').click()
      
      // 4. إضافة نقاط ولاء للعميل VIP
      cy.visitWithLocale('/merchant/loyalty')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="program-members"]').click()
      cy.get('[data-testid="member-row"]').first().click()
      cy.get('[data-testid="add-points-btn"]').click()
      cy.fillForm({
        'points-amount': '500',
        'points-reason': 'مكافأة عضوية VIP'
      })
      
      cy.get('[data-testid="confirm-add-points"]').click()
      cy.get('[data-testid="success-message"]').should('be.visible')
    })

    it('يجب أن تتكامل أنظمة ERP/POS مع إدارة المخزون', () => {
      cy.mockLogin('merchant')
      
      // 1. إعداد مزامنة المخزون مع ERP
      cy.visitWithLocale('/merchant/integrations')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="integration-row"]').first().within(() => {
        cy.get('[data-testid="sync-inventory-btn"]').click()
      })
      
      cy.get('[data-testid="sync-status"]').should('contain.text', 'جاري المزامنة')
      
      // 2. التحقق من تحديث المخزون
      cy.visitWithLocale('/merchant/inventory')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="inventory-table"]').should('be.visible')
      cy.get('[data-testid="last-sync-time"]').should('be.visible')
      
      // 3. التحقق من تنبيهات المخزون المنخفض
      cy.get('[data-testid="low-stock-alerts"]').should('be.visible')
      cy.get('[data-testid="reorder-suggestions"]').should('be.visible')
    })

    it('يجب أن تعمل التقارير الموحدة لجميع الأنظمة', () => {
      cy.mockLogin('merchant')
      cy.visitWithLocale('/merchant/reports')
      cy.waitForLoadingToFinish()
      
      // 1. تقرير شامل يجمع بيانات من جميع الأنظمة
      cy.get('[data-testid="comprehensive-report"]').click()
      
      // 2. التحقق من وجود بيانات من جميع الأنظمة
      cy.get('[data-testid="sales-data"]').should('be.visible')
      cy.get('[data-testid="crm-data"]').should('be.visible')
      cy.get('[data-testid="loyalty-data"]').should('be.visible')
      cy.get('[data-testid="coupon-data"]').should('be.visible')
      cy.get('[data-testid="erp-data"]').should('be.visible')
      
      // 3. تصدير التقرير الشامل
      cy.get('[data-testid="export-comprehensive-report"]').click()
      cy.get('[data-testid="export-format"]').select('excel')
      cy.get('[data-testid="confirm-export"]').click()
      
      cy.get('[data-testid="export-status"]').should('contain.text', 'جاري التصدير')
    })
  })

  describe('🚨 اختبار معالجة الأخطاء والاستثناءات', () => {
    it('يجب أن يتعامل مع أخطاء التكامل بشكل صحيح', () => {
      cy.mockLogin('merchant')
      
      // 1. محاكاة خطأ في تكامل ERP
      cy.intercept('POST', '**/api/erp/sync**', {
        statusCode: 500,
        body: { error: 'ERP connection failed' }
      }).as('erpError')
      
      cy.visitWithLocale('/merchant/integrations')
      cy.waitForLoadingToFinish()
      
      cy.get('[data-testid="sync-products-btn"]').click()
      cy.wait('@erpError')
      
      // التحقق من عرض رسالة خطأ واضحة
      cy.get('[data-testid="sync-error"]').should('be.visible')
      cy.shouldContainArabicText('فشل في المزامنة مع نظام ERP')
      
      // التحقق من وجود خيارات الاستكشاف
      cy.get('[data-testid="retry-sync"]').should('be.visible')
      cy.get('[data-testid="contact-support"]').should('be.visible')
    })

    it('يجب أن يتعامل مع أخطاء النظام العامة', () => {
      // محاكاة خطأ عام في النظام
      cy.intercept('GET', '**/api/**', {
        statusCode: 503,
        body: { error: 'Service unavailable' }
      }).as('systemError')
      
      cy.mockLogin('customer')
      cy.visitWithLocale('/dashboard')
      cy.wait('@systemError')
      
      // التحقق من عرض صفحة خطأ مناسبة
      cy.get('[data-testid="system-error"]').should('be.visible')
      cy.shouldContainArabicText('النظام غير متاح حالياً')
      
      // التحقق من وجود خيارات الاستكشاف
      cy.get('[data-testid="retry-page"]').should('be.visible')
      cy.get('[data-testid="go-home"]').should('be.visible')
    })
  })

  describe('📊 اختبار الأداء والتحميل', () => {
    it('يجب أن يتعامل مع حمولة عالية من البيانات', () => {
      cy.mockLogin('merchant')
      
      // محاكاة بيانات كبيرة
      cy.window().then((win) => {
        const largeCRMData = Array.from({ length: 1000 }, (_, i) => ({
          id: `customer-${i}`,
          name: `عميل ${i}`,
          email: `customer${i}@example.com`,
          orders: Math.floor(Math.random() * 50),
          totalSpent: Math.floor(Math.random() * 10000)
        }))
        
        win.localStorage.setItem('mockLargeCRMData', JSON.stringify(largeCRMData))
      })
      
      // اختبار تحميل صفحة CRM مع بيانات كبيرة
      cy.visitWithLocale('/merchant/crm')
      cy.waitForLoadingToFinish()
      
      // التحقق من تحميل البيانات بنجاح
      cy.get('[data-testid="customers-table"]').should('be.visible')
      cy.get('[data-testid="pagination"]').should('be.visible')
      
      // اختبار البحث والفلترة مع البيانات الكبيرة
      cy.get('[data-testid="customer-search"]').type('عميل 100')
      cy.get('[data-testid="search-btn"]').click()
      cy.get('[data-testid="customer-row"]').should('contain.text', 'عميل 100')
    })
  })

  describe('🔒 اختبار الأمان والصلاحيات', () => {
    it('يجب أن يمنع الوصول غير المصرح به', () => {
      // محاولة الوصول لصفحة التاجر بدون تسجيل دخول
      cy.visitWithLocale('/merchant/dashboard')
      
      // التحقق من إعادة التوجيه لصفحة تسجيل الدخول
      cy.url().should('include', '/login')
      cy.shouldContainArabicText('يجب تسجيل الدخول أولاً')
      
      // محاولة الوصول لصفحة الإدارة بحساب عميل
      cy.mockLogin('customer')
      cy.visitWithLocale('/admin/dashboard')
      
      // التحقق من منع الوصول
      cy.get('[data-testid="access-denied"]').should('be.visible')
      cy.shouldContainArabicText('ليس لديك صلاحية للوصول لهذه الصفحة')
    })

    it('يجب أن يحمي البيانات الحساسة', () => {
      cy.mockLogin('merchant')
      
      // التحقق من عدم عرض معلومات حساسة في الكود المصدري
      cy.visitWithLocale('/merchant/integrations')
      cy.waitForLoadingToFinish()
      
      // التحقق من إخفاء مفاتيح API
      cy.get('[data-testid="api-key-display"]').should('contain.text', '****')
      cy.get('[data-testid="api-key-display"]').should('not.contain.text', 'test-api-key')
      
      // التحقق من تشفير البيانات المرسلة
      cy.intercept('POST', '**/api/integrations**').as('integrationRequest')
      cy.get('[data-testid="save-integration"]').click()
      cy.wait('@integrationRequest').then((interception) => {
        expect(interception.request.body).to.not.contain('test-api-key')
      })
    })
  })
})
