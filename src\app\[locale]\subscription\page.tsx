"use client";

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, ArrowLeft, CreditCard, Loader2 } from "lucide-react";
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { merchantPlans, customerPlans, representativePlans } from '@/constants/plans';
import type { SubscriptionPlan } from '@/types';
import AdvancedPaymentForm from '@/components/payment/AdvancedPaymentForm';
import { useSubscription } from '@/hooks/useSubscription';

export default function SubscriptionPage() {
  const { t, locale } = useLocale();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const { createSubscription, activateSubscription } = useSubscription();

  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [showPayment, setShowPayment] = useState(false);
  const [processing, setProcessing] = useState(false);

  // جلب الخطة المحددة من URL
  useEffect(() => {
    const planId = searchParams.get('plan');
    if (planId) {
      // البحث في جميع الخطط
      const allPlans = [...merchantPlans, ...customerPlans, ...representativePlans];
      const plan = allPlans.find(p => p.id === planId);
      
      if (plan) {
        setSelectedPlan(plan);
        // إذا كانت الخطة مدفوعة، عرض نموذج الدفع
        if (plan.priceDisplayKey !== 'free') {
          setShowPayment(true);
        }
      } else {
        // إذا لم توجد الخطة، العودة لصفحة الأسعار
        router.push(`/${locale}/pricing`);
      }
    } else {
      // إذا لم يتم تحديد خطة، العودة لصفحة الأسعار
      router.push(`/${locale}/pricing`);
    }
    setLoading(false);
  }, [searchParams, router, locale]);

  // التحقق من تسجيل الدخول
  useEffect(() => {
    if (!loading && !user) {
      router.push(`/${locale}/login?redirect=/subscription?plan=${selectedPlan?.id}`);
    }
  }, [user, loading, router, locale, selectedPlan]);

  const formatPrice = (plan: SubscriptionPlan) => {
    if (plan.priceDisplayKey === 'free') {
      return t('free');
    }
    if (plan.priceValue !== undefined && plan.currencyKey && plan.periodKey) {
      return t(plan.priceDisplayKey, {
        price: plan.priceValue,
        currency: t(plan.currencyKey),
        period: t(plan.periodKey)
      });
    }
    return '';
  };

  const handlePaymentSuccess = async (result: any) => {
    if (!selectedPlan) return;

    try {
      setProcessing(true);

      // إنشاء الاشتراك مع معلومات الدفع
      const subscriptionId = await createSubscription(
        selectedPlan.id,
        result.paymentId,
        result.transactionId,
        selectedPlan.priceValue
      );

      if (subscriptionId) {
        // تفعيل الاشتراك
        await activateSubscription(subscriptionId, result.transactionId);

        // توجيه المستخدم لصفحة النجاح
        router.push(`/${locale}/subscription/success?plan=${selectedPlan.id}&payment=${result.transactionId}`);
      }
    } catch (error) {
      console.error('Error processing subscription:', error);
      handlePaymentError('فشل في معالجة الاشتراك');
    } finally {
      setProcessing(false);
    }
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    // يمكن عرض رسالة خطأ هنا
  };

  const handleActivateFreePlan = async () => {
    if (!selectedPlan || !user) return;

    try {
      setProcessing(true);

      // إنشاء الاشتراك المجاني
      const subscriptionId = await createSubscription(selectedPlan.id);

      if (subscriptionId) {
        // تفعيل الاشتراك المجاني
        await activateSubscription(subscriptionId);

        // توجيه المستخدم لصفحة النجاح
        router.push(`/${locale}/subscription/success?plan=${selectedPlan.id}&type=free`);
      }
    } catch (error) {
      console.error('Error activating free plan:', error);
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!selectedPlan || !user) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-12">
      {/* العودة للخلف */}
      <Button
        variant="ghost"
        onClick={() => router.back()}
        className="mb-6"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        {t('back')}
      </Button>

      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">{t('subscriptionCheckout')}</h1>
          <p className="text-muted-foreground">{t('completeSubscription')}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* معلومات الخطة */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl">{t(selectedPlan.nameKey)}</CardTitle>
                {selectedPlan.isPopular && (
                  <Badge variant="default">{t('popular')}</Badge>
                )}
              </div>
              <CardDescription className="text-2xl font-bold text-primary">
                {formatPrice(selectedPlan)}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <h4 className="font-semibold">{t('includedFeatures')}</h4>
                <ul className="space-y-2">
                  {selectedPlan.features.map((feature) => (
                    <li key={feature.nameKey} className="flex items-center space-x-2 rtl:space-x-reverse">
                      {feature.available ? (
                        <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                      )}
                      <span className={`text-sm ${feature.available ? 'text-foreground' : 'text-muted-foreground line-through'}`}>
                        {t(feature.nameKey)}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* نموذج الدفع أو تفعيل الخطة المجانية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="w-5 h-5 mr-2" />
                {selectedPlan.priceDisplayKey === 'free' ? t('activatePlan') : t('paymentDetails')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedPlan.priceDisplayKey === 'free' ? (
                <div className="space-y-4">
                  <p className="text-muted-foreground">{t('freeActivationMessage')}</p>
                  <Button
                    onClick={handleActivateFreePlan}
                    className="w-full"
                    disabled={processing}
                  >
                    {processing ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        {t('activating')}
                      </>
                    ) : (
                      t('activateFreePlan')
                    )}
                  </Button>
                </div>
              ) : showPayment && selectedPlan.priceValue ? (
                <AdvancedPaymentForm
                  orderId={`subscription-${selectedPlan.id}-${Date.now()}`}
                  amount={selectedPlan.priceValue}
                  currency="SAR"
                  description={`${t('subscriptionFor')} ${t(selectedPlan.nameKey)}`}
                  onPaymentSuccess={handlePaymentSuccess}
                  onPaymentError={handlePaymentError}
                />
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">{t('loadingPaymentForm')}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
