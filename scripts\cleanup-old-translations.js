#!/usr/bin/env node

/**
 * سكريبت تنظيف ملفات الترجمة القديمة
 * يحذف ملفات النسخ الاحتياطية القديمة ويحتفظ بالملفات الأساسية
 */

const fs = require('fs');
const path = require('path');

// مسار مجلد الترجمات
const LOCALES_DIR = path.join(__dirname, '../src/locales');

/**
 * الحصول على قائمة ملفات النسخ الاحتياطية
 */
function getBackupFiles() {
  try {
    const files = fs.readdirSync(LOCALES_DIR);
    return files.filter(file => 
      file.includes('_backup_') && 
      file.endsWith('.json')
    );
  } catch (error) {
    console.error('❌ خطأ في قراءة مجلد الترجمات:', error.message);
    return [];
  }
}

/**
 * حذف ملفات النسخ الاحتياطية القديمة
 */
function cleanupBackupFiles(keepLatest = 3) {
  const backupFiles = getBackupFiles();
  
  if (backupFiles.length === 0) {
    console.log('✅ لا توجد ملفات نسخ احتياطية للحذف');
    return;
  }
  
  console.log(`📁 تم العثور على ${backupFiles.length} ملف نسخة احتياطية`);
  
  // تجميع الملفات حسب النوع (ar أو en)
  const arBackups = backupFiles.filter(f => f.startsWith('ar_backup_')).sort().reverse();
  const enBackups = backupFiles.filter(f => f.startsWith('en_backup_')).sort().reverse();
  
  let deletedCount = 0;
  
  // حذف النسخ الاحتياطية القديمة للملف العربي
  if (arBackups.length > keepLatest) {
    const toDelete = arBackups.slice(keepLatest);
    toDelete.forEach(file => {
      try {
        fs.unlinkSync(path.join(LOCALES_DIR, file));
        console.log(`🗑️ تم حذف: ${file}`);
        deletedCount++;
      } catch (error) {
        console.error(`❌ فشل حذف ${file}:`, error.message);
      }
    });
  }
  
  // حذف النسخ الاحتياطية القديمة للملف الإنجليزي
  if (enBackups.length > keepLatest) {
    const toDelete = enBackups.slice(keepLatest);
    toDelete.forEach(file => {
      try {
        fs.unlinkSync(path.join(LOCALES_DIR, file));
        console.log(`🗑️ تم حذف: ${file}`);
        deletedCount++;
      } catch (error) {
        console.error(`❌ فشل حذف ${file}:`, error.message);
      }
    });
  }
  
  console.log(`\n✅ تم حذف ${deletedCount} ملف نسخة احتياطية قديمة`);
  console.log(`📦 تم الاحتفاظ بآخر ${keepLatest} نسخ احتياطية لكل ملف`);
}

/**
 * عرض معلومات الملفات الحالية
 */
function showCurrentFiles() {
  try {
    const files = fs.readdirSync(LOCALES_DIR);
    const translationFiles = files.filter(f => f.endsWith('.json'));
    
    console.log('\n📋 الملفات الحالية في مجلد الترجمات:');
    translationFiles.forEach(file => {
      const filePath = path.join(LOCALES_DIR, file);
      const stats = fs.statSync(filePath);
      const sizeKB = Math.round(stats.size / 1024);
      
      if (file.includes('_backup_')) {
        console.log(`   📄 ${file} (${sizeKB} KB) - نسخة احتياطية`);
      } else {
        console.log(`   📄 ${file} (${sizeKB} KB) - ملف أساسي`);
      }
    });
  } catch (error) {
    console.error('❌ خطأ في عرض الملفات:', error.message);
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🧹 بدء تنظيف ملفات الترجمات القديمة...\n');
  
  // عرض الملفات الحالية
  showCurrentFiles();
  
  // تنظيف النسخ الاحتياطية القديمة
  console.log('\n🗑️ تنظيف النسخ الاحتياطية القديمة...');
  cleanupBackupFiles(3); // الاحتفاظ بآخر 3 نسخ احتياطية
  
  // عرض الملفات بعد التنظيف
  showCurrentFiles();
  
  console.log('\n✅ تم الانتهاء من تنظيف ملفات الترجمات!');
  console.log('\n💡 نصائح:');
  console.log('   - الملف المدموج الجديد: translations.json');
  console.log('   - يمكنك الآن حذف ar.json و en.json إذا كنت تريد استخدام الملف المدموج فقط');
  console.log('   - تأكد من تحديث جميع المراجع في الكود لاستخدام الملف الجديد');
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  cleanupBackupFiles,
  getBackupFiles,
  showCurrentFiles
};
