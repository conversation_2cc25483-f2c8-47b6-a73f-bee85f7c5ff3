# إعدادات الذكاء الاصطناعي الآمن - مِخْلاة
# نسخ هذا الملف إلى .env.local وتحديث القيم

# ===== إعدادات الأمان الأساسية =====
USE_LOCAL_AI=true                    # استخدام الذكاء الاصطناعي المحلي
ENCRYPT_EXTERNAL=true                # تشفير الطلبات الخارجية
ANONYMIZE_DATA=true                  # إخفاء هوية البيانات
AUDIT_LOGGING=true                   # تسجيل المراجعة

# ===== إعدادات التشفير =====
DOCUMENT_ENCRYPTION_KEY=your-super-secret-encryption-key-change-this-in-production
ENCRYPTION_ALGORITHM=AES-256-GCM

# ===== إعدادات Ollama المحلي =====
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=aya:8b-instruct         # نموذج يدعم العربية
OLLAMA_TIMEOUT=30000                 # مهلة زمنية بالمللي ثانية

# ===== إعدادات OCR المحلي =====
TESSERACT_LANGUAGES=ara+eng          # اللغات المدعومة
TESSERACT_CONFIG=--psm 6 --oem 3     # إعدادات Tesseract

# ===== إعدادات الأداء =====
MAX_CONCURRENT_ANALYSIS=5            # أقصى عدد تحليلات متزامنة
ANALYSIS_TIMEOUT=60000               # مهلة زمنية للتحليل
CACHE_ANALYSIS_RESULTS=true         # تخزين نتائج التحليل مؤقتاً

# ===== إعدادات الجودة =====
MIN_CONFIDENCE_THRESHOLD=80          # أقل نسبة ثقة مقبولة
MAX_CONFIDENCE_FOR_EXTERNAL=85      # أقصى ثقة للتحليل الخارجي
REQUIRE_MANUAL_REVIEW_BELOW=70      # مراجعة يدوية تحت هذه النسبة

# ===== إعدادات المراجعة والامتثال =====
AUDIT_LOG_RETENTION_DAYS=365        # مدة حفظ سجلات المراجعة
COMPLIANCE_MODE=saudi_data_protection # وضع الامتثال
GDPR_COMPLIANCE=true                 # امتثال GDPR
DATA_RESIDENCY=saudi_arabia          # إقامة البيانات

# ===== إعدادات النسخ الاحتياطي =====
BACKUP_ANALYSIS_LOGS=true           # نسخ احتياطي لسجلات التحليل
BACKUP_ENCRYPTION=true              # تشفير النسخ الاحتياطية
BACKUP_RETENTION_MONTHS=12          # مدة حفظ النسخ الاحتياطية

# ===== إعدادات التنبيهات =====
ALERT_ON_LOW_CONFIDENCE=true        # تنبيه عند انخفاض الثقة
ALERT_ON_SECURITY_BREACH=true       # تنبيه عند خرق الأمان
ALERT_EMAIL=<EMAIL>         # بريد التنبيهات

# ===== إعدادات التطوير (فقط للتطوير) =====
DEVELOPMENT_MODE=false              # وضع التطوير
MOCK_AI_RESPONSES=false             # محاكاة استجابات الذكاء الاصطناعي
DEBUG_AI_ANALYSIS=false             # تصحيح تحليل الذكاء الاصطناعي

# ===== إعدادات الخدمات الخارجية (اختيارية ومشفرة) =====
# استخدم فقط إذا كان التشفير مفعل
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_KEY_FILE=path/to/encrypted/service-account.json
AWS_ACCESS_KEY_ID=your-encrypted-access-key
AWS_SECRET_ACCESS_KEY=your-encrypted-secret-key
AWS_REGION=me-south-1               # منطقة الشرق الأوسط
AZURE_COGNITIVE_SERVICES_KEY=your-encrypted-key
AZURE_COGNITIVE_SERVICES_ENDPOINT=https://your-endpoint.cognitiveservices.azure.com/

# ===== إعدادات قاعدة البيانات للمراجعة =====
AUDIT_DB_HOST=localhost
AUDIT_DB_PORT=5432
AUDIT_DB_NAME=mikhla_audit
AUDIT_DB_USER=audit_user
AUDIT_DB_PASSWORD=secure_audit_password
AUDIT_DB_SSL=true

# ===== إعدادات الشبكة والأمان =====
ALLOWED_DOCUMENT_DOMAINS=storage.googleapis.com,s3.amazonaws.com,mikhla-storage.com
MAX_DOCUMENT_SIZE_MB=10
ALLOWED_DOCUMENT_TYPES=pdf,jpg,jpeg,png
VIRUS_SCAN_ENABLED=true
FIREWALL_ENABLED=true

# ===== إعدادات الأداء المتقدم =====
ENABLE_GPU_ACCELERATION=false       # تسريع GPU للنماذج المحلية
MEMORY_LIMIT_MB=4096                # حد الذاكرة للتحليل
CPU_CORES_LIMIT=4                   # حد معالجات CPU

# ===== إعدادات الدفع =====
# PayPal Configuration
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_WEBHOOK_ID=your-paypal-webhook-id
NEXT_PUBLIC_APP_URL=https://mikhla.com

# Stripe Configuration (اختياري)
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# مدى Configuration (اختياري)
MADA_MERCHANT_ID=your-mada-merchant-id
MADA_TERMINAL_ID=your-mada-terminal-id
MADA_SECRET_KEY=your-mada-secret-key

# ===== إعدادات التخزين المؤقت =====
REDIS_URL=redis://localhost:6379
CACHE_TTL_SECONDS=3600              # مدة التخزين المؤقت
CACHE_MAX_SIZE_MB=512               # أقصى حجم للتخزين المؤقت

# ===== إعدادات المراقبة =====
MONITORING_ENABLED=true
METRICS_ENDPOINT=/metrics
HEALTH_CHECK_ENDPOINT=/health
PROMETHEUS_PORT=9090

# ===== إعدادات الإشعارات =====
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/webhook/url
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USER=<EMAIL>
EMAIL_SMTP_PASSWORD=your-email-password

# ===== إعدادات النشر =====
ENVIRONMENT=production              # production, staging, development
LOG_LEVEL=info                      # debug, info, warn, error
CORS_ORIGINS=https://mikhla.com,https://admin.mikhla.com
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# ===== إعدادات الأمان المتقدم =====
ENABLE_2FA=true                     # تفعيل المصادقة الثنائية
SESSION_TIMEOUT_MINUTES=30         # انتهاء الجلسة
PASSWORD_COMPLEXITY=high           # تعقيد كلمة المرور
BRUTE_FORCE_PROTECTION=true        # حماية من الهجمات

# ===== إعدادات النسخ الاحتياطي المتقدم =====
S3_BACKUP_BUCKET=mikhla-secure-backups
S3_BACKUP_REGION=me-south-1
S3_BACKUP_ENCRYPTION=AES256
BACKUP_SCHEDULE=0 2 * * *           # يومياً في الساعة 2 صباحاً

# ===== إعدادات الامتثال المتقدم =====
DATA_CLASSIFICATION=confidential    # تصنيف البيانات
RETENTION_POLICY=7_years           # سياسة الاحتفاظ
DELETION_POLICY=secure_wipe        # سياسة الحذف
AUDIT_TRAIL=comprehensive          # مسار المراجعة

# ===== ملاحظات مهمة =====
# 1. غير جميع كلمات المرور والمفاتيح الافتراضية
# 2. استخدم HTTPS في جميع الاتصالات
# 3. فعل جميع إعدادات الأمان في الإنتاج
# 4. راجع السجلات بانتظام
# 5. احتفظ بنسخ احتياطية مشفرة
# 6. اختبر خطة الاستعادة من الكوارث
# 7. حدث النظام بانتظام
# 8. راقب الأداء والأمان باستمرار
