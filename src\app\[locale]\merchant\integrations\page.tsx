import { Metadata } from 'next';
import { IntegrationSettings } from '@/components/merchant/IntegrationSettings';
import type { Locale } from '@/types';

interface IntegrationsPageProps {
  params: {
    locale: Locale;
  };
}

export async function generateMetadata({ params }: IntegrationsPageProps): Promise<Metadata> {
  return {
    title: 'إعدادات التكامل - مخلة',
    description: 'ربط متجرك مع أنظمة ERP و POS الخارجية',
  };
}

export default function IntegrationsPage({ params }: IntegrationsPageProps) {
  // في التطبيق الحقيقي، ستحتاج للتحقق من المصادقة
  const merchantId = 'demo-merchant-id';

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-7xl mx-auto">
        <IntegrationSettings merchantId={merchantId} />
      </div>
    </div>
  );
}
