// cypress/e2e/performance-optimization-tests.cy.ts - اختبارات الأداء والتحسين

describe('⚡ اختبارات الأداء والتحسين', () => {
  let performanceMetrics: any[] = [];
  let memorySnapshots: any[] = [];

  beforeEach(() => {
    performanceMetrics = [];
    memorySnapshots = [];

    // مراقبة الأداء
    cy.window().then((win) => {
      // مراقبة أحداث الأداء
      win.addEventListener('performance-metric', (event: any) => {
        performanceMetrics.push({
          metric: event.detail.metric,
          value: event.detail.value,
          timestamp: Date.now()
        });
      });

      // أخذ لقطات للذاكرة
      const takeMemorySnapshot = () => {
        if ((win.performance as any).memory) {
          memorySnapshots.push({
            used: (win.performance as any).memory.usedJSHeapSize,
            total: (win.performance as any).memory.totalJSHeapSize,
            limit: (win.performance as any).memory.jsHeapSizeLimit,
            timestamp: Date.now()
          });
        }
      };

      // أخذ لقطة كل 5 ثواني
      setInterval(takeMemorySnapshot, 5000);
      takeMemorySnapshot(); // لقطة أولية
    });

    cy.login('merchant');
    cy.visit('/ai-document-processing');
  });

  describe('🚀 اختبارات الأداء', () => {
    it('يجب أن يحمل الصفحة في وقت معقول', () => {
      cy.window().then((win) => {
        const loadTime = win.performance.timing.loadEventEnd - win.performance.timing.navigationStart;
        expect(loadTime).to.be.lessThan(5000); // أقل من 5 ثواني
        cy.log(`وقت تحميل الصفحة: ${loadTime}ms`);
      });
    });

    it('يجب أن يحمل النماذج في وقت معقول', () => {
      const startTime = Date.now();
      
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      
      // انتظار تحميل النماذج الأساسية
      cy.get('[data-testid="model-status-ready"]', { timeout: 120000 })
        .should('have.length.at.least', 3)
        .then(() => {
          const loadTime = Date.now() - startTime;
          expect(loadTime).to.be.lessThan(120000); // أقل من دقيقتين
          cy.log(`وقت تحميل النماذج: ${loadTime}ms`);
        });
    });

    it('يجب أن يعالج المستندات بسرعة', () => {
      // تحميل النماذج أولاً
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      cy.get('[data-testid="model-status-ready"]', { timeout: 120000 })
        .should('have.length.at.least', 3);

      // قياس وقت المعالجة
      const startTime = Date.now();
      
      cy.get('[data-testid="upload-tab"]').click();
      
      const testFile = new File(['test content'], 'test-document.jpg', {
        type: 'image/jpeg'
      });
      
      cy.get('[data-testid="file-input"]').selectFile(testFile, { force: true });
      cy.get('[data-testid="process-document-btn"]').click();
      
      cy.get('[data-testid="processing-complete"]', { timeout: 30000 })
        .should('be.visible')
        .then(() => {
          const processingTime = Date.now() - startTime;
          expect(processingTime).to.be.lessThan(15000); // أقل من 15 ثانية
          cy.log(`وقت المعالجة: ${processingTime}ms`);
        });
    });

    it('يجب أن يحافظ على استجابة الواجهة أثناء المعالجة', () => {
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      
      // أثناء تحميل النماذج، يجب أن تبقى الواجهة مستجيبة
      cy.get('[data-testid="memory-tab"]').click();
      cy.get('[data-testid="memory-usage-chart"]').should('be.visible');
      
      cy.get('[data-testid="optimization-tab"]').click();
      cy.get('[data-testid="total-optimizations"]').should('be.visible');
      
      cy.get('[data-testid="settings-tab"]').click();
      cy.get('[data-testid="auto-cleanup-toggle"]').should('be.visible');
    });
  });

  describe('🧠 اختبارات إدارة الذاكرة', () => {
    it('يجب ألا يتجاوز حد الذاكرة المحدد', () => {
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      
      cy.get('[data-testid="model-status-ready"]', { timeout: 120000 })
        .should('have.length.at.least', 3);
      
      cy.get('[data-testid="memory-tab"]').click();
      
      // التحقق من استخدام الذاكرة
      cy.get('[data-testid="memory-usage-percentage"]').then(($el) => {
        const usageText = $el.text();
        const usage = parseFloat(usageText.replace('%', ''));
        expect(usage).to.be.lessThan(90); // أقل من 90%
        cy.log(`استخدام الذاكرة: ${usage}%`);
      });
    });

    it('يجب أن يحرر الذاكرة عند إلغاء تحميل النماذج', () => {
      // تحميل النماذج
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      cy.get('[data-testid="model-status-ready"]', { timeout: 120000 })
        .should('have.length.at.least', 3);
      
      // قياس الذاكرة بعد التحميل
      cy.get('[data-testid="memory-tab"]').click();
      cy.get('[data-testid="total-usage"]').then(($el) => {
        const usageAfterLoad = parseInt($el.text().replace(/[^\d]/g, ''));
        
        // إلغاء تحميل نموذج
        cy.get('[data-testid="models-tab"]').click();
        cy.get('[data-testid="unload-model-btn"]').first().click();
        
        // التحقق من تحرير الذاكرة
        cy.get('[data-testid="memory-tab"]').click();
        cy.get('[data-testid="total-usage"]').then(($el2) => {
          const usageAfterUnload = parseInt($el2.text().replace(/[^\d]/g, ''));
          expect(usageAfterUnload).to.be.lessThan(usageAfterLoad);
          cy.log(`تحرير الذاكرة: ${usageAfterLoad - usageAfterUnload} bytes`);
        });
      });
    });

    it('يجب أن يكتشف تسريبات الذاكرة', () => {
      cy.window().then((win) => {
        const initialMemory = (win.performance as any).memory?.usedJSHeapSize || 0;
        
        // تحميل وإلغاء تحميل النماذج عدة مرات
        for (let i = 0; i < 3; i++) {
          cy.get('[data-testid="models-tab"]').click();
          cy.get('[data-testid="load-model-btn"]').first().click();
          cy.get('[data-testid="model-status-ready"]', { timeout: 30000 }).should('exist');
          cy.get('[data-testid="unload-model-btn"]').first().click();
          cy.get('[data-testid="model-status-not-loaded"]').should('exist');
        }
        
        // التحقق من عدم تراكم الذاكرة
        cy.then(() => {
          const finalMemory = (win.performance as any).memory?.usedJSHeapSize || 0;
          const memoryIncrease = finalMemory - initialMemory;
          
          // يجب ألا تزيد الذاكرة بأكثر من 100MB
          expect(memoryIncrease).to.be.lessThan(100 * 1024 * 1024);
          cy.log(`زيادة الذاكرة: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
        });
      });
    });
  });

  describe('🔧 اختبارات التحسين التلقائي', () => {
    it('يجب أن يشغل التحسين التلقائي', () => {
      cy.get('[data-testid="optimization-tab"]').click();
      
      // تفعيل التحسين التلقائي
      cy.get('[data-testid="auto-optimization-toggle"]').click();
      
      // تحميل النماذج لإنشاء ضغط على الذاكرة
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      
      // انتظار تشغيل التحسين التلقائي
      cy.wait(5000); // انتظار 5 ثواني
      
      cy.get('[data-testid="optimization-tab"]').click();
      
      // التحقق من تشغيل التحسين
      cy.get('[data-testid="recent-optimizations"]').should('contain', 'تم التحسين');
    });

    it('يجب أن يطبق استراتيجيات التحسين المختلفة', () => {
      cy.get('[data-testid="optimization-tab"]').click();
      
      // تحديد استراتيجيات متعددة
      cy.get('[data-testid="strategy-memory-cleanup"]').check();
      cy.get('[data-testid="strategy-cache-optimization"]').check();
      cy.get('[data-testid="strategy-garbage-collection"]').check();
      
      // تشغيل التحسين
      cy.get('[data-testid="run-optimization-btn"]').click();
      
      // التحقق من تطبيق الاستراتيجيات
      cy.get('[data-testid="optimization-complete"]', { timeout: 15000 }).should('be.visible');
      
      // التحقق من النتائج
      cy.get('[data-testid="memory-freed"]').should('be.visible');
      cy.get('[data-testid="optimizations-applied"]').should('contain', '3');
    });

    it('يجب أن يولد توصيات ذكية', () => {
      cy.get('[data-testid="optimization-tab"]').click();
      
      // تحميل نماذج كثيرة لإنشاء حالة تحتاج تحسين
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      cy.get('[data-testid="model-status-ready"]', { timeout: 120000 })
        .should('have.length.at.least', 3);
      
      cy.get('[data-testid="optimization-tab"]').click();
      
      // التحقق من وجود توصيات
      cy.get('[data-testid="recommendations"]').should('be.visible');
      cy.get('[data-testid="recommendation-item"]').should('have.length.at.least', 1);
    });
  });

  describe('🏥 اختبارات مراقبة الصحة', () => {
    it('يجب أن يراقب صحة النظام', () => {
      // في المستقبل عندما يتم دمج مراقب الصحة
      cy.get('[data-testid="system-health"]').should('exist');
      
      // التحقق من مكونات الصحة
      cy.get('[data-testid="health-memory"]').should('be.visible');
      cy.get('[data-testid="health-models"]').should('be.visible');
      cy.get('[data-testid="health-performance"]').should('be.visible');
      cy.get('[data-testid="health-optimization"]').should('be.visible');
      cy.get('[data-testid="health-stability"]').should('be.visible');
    });

    it('يجب أن يولد تنبيهات للمشاكل', () => {
      // محاكاة حالة مشكلة (ذاكرة ممتلئة)
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      
      // انتظار امتلاء الذاكرة
      cy.get('[data-testid="memory-tab"]').click();
      cy.get('[data-testid="memory-usage-percentage"]').then(($el) => {
        const usage = parseFloat($el.text().replace('%', ''));
        
        if (usage > 80) {
          // يجب أن تظهر تنبيهات
          cy.get('[data-testid="health-alerts"]').should('be.visible');
          cy.get('[data-testid="alert-memory-warning"]').should('exist');
        }
      });
    });
  });

  describe('📊 اختبارات الأداء المتقدمة', () => {
    it('يجب أن يحافظ على FPS مستقر', () => {
      let frameCount = 0;
      let lastTime = Date.now();
      
      cy.window().then((win) => {
        const measureFPS = () => {
          frameCount++;
          const currentTime = Date.now();
          
          if (currentTime - lastTime >= 1000) {
            const fps = frameCount;
            frameCount = 0;
            lastTime = currentTime;
            
            // يجب أن يكون FPS أعلى من 30
            expect(fps).to.be.greaterThan(30);
            cy.log(`FPS: ${fps}`);
          }
          
          requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);
      });
      
      // تحميل النماذج أثناء قياس FPS
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      
      // انتظار لقياس FPS
      cy.wait(5000);
    });

    it('يجب أن يتعامل مع الحمولة العالية', () => {
      // محاكاة معالجة متعددة
      const files = [
        new File(['test1'], 'doc1.jpg', { type: 'image/jpeg' }),
        new File(['test2'], 'doc2.jpg', { type: 'image/jpeg' }),
        new File(['test3'], 'doc3.jpg', { type: 'image/jpeg' })
      ];
      
      // تحميل النماذج أولاً
      cy.get('[data-testid="models-tab"]').click();
      cy.get('[data-testid="preload-models-btn"]').click();
      cy.get('[data-testid="model-status-ready"]', { timeout: 120000 })
        .should('have.length.at.least', 3);
      
      // معالجة ملفات متعددة
      files.forEach((file, index) => {
        cy.get('[data-testid="upload-tab"]').click();
        cy.get('[data-testid="file-input"]').selectFile(file, { force: true });
        cy.get('[data-testid="process-document-btn"]').click();
        
        // انتظار بدء المعالجة قبل الانتقال للتالي
        cy.get('[data-testid="processing-started"]').should('be.visible');
      });
      
      // التحقق من أن النظام لم ينهار
      cy.get('[data-testid="system-responsive"]').should('be.visible');
    });
  });

  afterEach(() => {
    // تحليل البيانات المجمعة
    cy.then(() => {
      console.log('📊 تقرير الأداء:');
      console.log(`- مقاييس الأداء المجمعة: ${performanceMetrics.length}`);
      console.log(`- لقطات الذاكرة: ${memorySnapshots.length}`);
      
      if (memorySnapshots.length > 1) {
        const initialMemory = memorySnapshots[0].used;
        const finalMemory = memorySnapshots[memorySnapshots.length - 1].used;
        const memoryChange = finalMemory - initialMemory;
        
        console.log(`- تغيير الذاكرة: ${(memoryChange / 1024 / 1024).toFixed(2)}MB`);
      }
      
      // حفظ البيانات للتحليل اللاحق
      cy.writeFile('cypress/reports/performance-metrics.json', {
        metrics: performanceMetrics,
        memory: memorySnapshots,
        timestamp: Date.now()
      });
    });
  });
});
