"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  MessageCircle,
  Send,
  X,
  Bot,
  User,
  ThumbsUp,
  ThumbsDown,
  Minimize2,
  Maximize2
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/useAuth';
import GeminiChatbotService from '@/services/geminiChatbotService';

interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  suggestions?: string[];
}

interface ChatbotWidgetProps {
  isOpen?: boolean;
  onToggle?: () => void;
  position?: 'bottom-right' | 'bottom-left' | 'center';
  theme?: 'light' | 'dark';
}

/**
 * واجهة الشات بوت الذكي التفاعلية
 * نظام محادثة متطور مع دعم العربية والإنجليزية
 */
const ChatbotWidget: React.FC<ChatbotWidgetProps> = ({
  isOpen = false,
  onToggle,
  position = 'bottom-right',
  theme = 'light'
}) => {
  const { user } = useAuth();
  const [isWidgetOpen, setIsWidgetOpen] = useState(isOpen);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [currentSuggestions, setCurrentSuggestions] = useState<string[]>([]);
  const [conversationHistory, setConversationHistory] = useState<Array<{role: string, content: string}>>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // رسالة الترحيب الأولى
  const welcomeMessage: ChatMessage = {
    id: 'welcome',
    text: GeminiChatbotService.getWelcomeMessage(),
    isBot: true,
    timestamp: new Date(),
    suggestions: GeminiChatbotService.getQuickSuggestions()
  };

  // تهيئة الشات بوت
  useEffect(() => {
    if (isWidgetOpen && messages.length === 0) {
      initializeChatbot();
    }
  }, [isWidgetOpen]);

  // التمرير التلقائي للأسفل
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // تركيز على حقل الإدخال عند فتح الشات
  useEffect(() => {
    if (isWidgetOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isWidgetOpen, isMinimized]);

  const initializeChatbot = async () => {
    try {
      console.log('🤖 بدء تهيئة الشات بوت مع Gemini...');

      // التحقق من توفر خدمة Gemini
      if (!GeminiChatbotService.isServiceAvailable()) {
        throw new Error('خدمة Gemini غير متوفرة - مفتاح API مفقود');
      }

      // إضافة رسالة الترحيب
      setMessages([welcomeMessage]);
      setCurrentSuggestions(welcomeMessage.suggestions || []);
      setConversationHistory([]);
      console.log('✅ تم تهيئة الشات بوت مع Gemini بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تهيئة الشات بوت:', error);
      toast.error('حدث خطأ في تهيئة المحادثة');

      // إضافة رسالة ترحيب بديلة حتى لو فشلت التهيئة
      setMessages([welcomeMessage]);
      setCurrentSuggestions(welcomeMessage.suggestions || []);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleToggleWidget = () => {
    const newState = !isWidgetOpen;
    setIsWidgetOpen(newState);
    if (onToggle) {
      onToggle();
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isTyping) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputMessage.trim(),
      isBot: false,
      timestamp: new Date()
    };

    console.log('📤 إرسال رسالة المستخدم:', userMessage.text);

    // إضافة رسالة المستخدم
    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);
    setShowSuggestions(false);

    // تحديث تاريخ المحادثة
    const updatedHistory = [...conversationHistory, { role: 'user', content: userMessage.text }];

    try {
      console.log('🤖 معالجة الرسالة بواسطة Gemini...');

      // معالجة الرسالة بواسطة Gemini
      const response = await GeminiChatbotService.processMessage(
        userMessage.text,
        conversationHistory
      );

      console.log('✅ تم الحصول على رد من Gemini:', response);

      if (!response.success) {
        throw new Error(response.error || 'فشل في الحصول على رد من Gemini');
      }

      // محاكاة وقت الكتابة قصير
      await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: response.response,
        isBot: true,
        timestamp: new Date(),
        suggestions: GeminiChatbotService.getQuickSuggestions()
      };

      console.log('📥 إضافة رد الشات بوت:', botMessage.text);
      setMessages(prev => [...prev, botMessage]);
      setCurrentSuggestions(GeminiChatbotService.getQuickSuggestions());
      setShowSuggestions(true);

      // تحديث تاريخ المحادثة
      setConversationHistory([
        ...updatedHistory,
        { role: 'assistant', content: response.response }
      ]);

    } catch (error) {
      console.error('❌ خطأ في إرسال الرسالة:', error);

      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: 'أعتذر، حدث خطأ تقني. يرجى المحاولة مرة أخرى أو التواصل مع الدعم مباشرة.',
        isBot: true,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
      toast.error('حدث خطأ في إرسال الرسالة');
    } finally {
      setIsTyping(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
    setShowSuggestions(false);
    // إرسال الاقتراح تلقائياً
    setTimeout(() => {
      handleSendMessage();
    }, 100);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2';
      default:
        return 'bottom-4 right-4';
    }
  };

  // زر فتح/إغلاق الشات بوت
  if (!isWidgetOpen) {
    return (
      <div className={`fixed ${getPositionClasses()} z-50`}>
        <Button
          onClick={handleToggleWidget}
          size="lg"
          className="rounded-full w-16 h-16 bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse"
        >
          <MessageCircle className="w-8 h-8 text-white" />
        </Button>
      </div>
    );
  }

  return (
    <div className={`fixed ${getPositionClasses()} z-50`}>
      <Card className={`w-96 h-[600px] shadow-2xl border-0 ${
        theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white'
      } ${isMinimized ? 'h-16' : ''} transition-all duration-300`}>
        
        {/* رأس الشات بوت */}
        <CardHeader className="pb-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="relative">
                <Bot className="w-8 h-8" />
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
              </div>
              <div>
                <CardTitle className="text-lg font-bold">مساعد مِخْلاة</CardTitle>
                <p className="text-sm opacity-90">مساعد ذكي متاح 24/7</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="text-white hover:bg-white/20"
              >
                {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleToggleWidget}
                className="text-white hover:bg-white/20"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {!isMinimized && (
          <>
            {/* منطقة الرسائل */}
            <CardContent className="p-0 flex-1 flex flex-col h-[480px]">
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.isBot ? 'justify-start' : 'justify-end'}`}
                    >
                      <div className={`flex items-start space-x-2 rtl:space-x-reverse max-w-[80%] ${
                        message.isBot ? 'flex-row' : 'flex-row-reverse'
                      }`}>
                        
                        {/* أيقونة المرسل */}
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          message.isBot 
                            ? 'bg-blue-100 text-blue-600' 
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {message.isBot ? <Bot className="w-4 h-4" /> : <User className="w-4 h-4" />}
                        </div>

                        {/* محتوى الرسالة */}
                        <div className={`rounded-lg p-3 ${
                          message.isBot
                            ? 'bg-gray-100 text-gray-800'
                            : 'bg-blue-600 text-white'
                        }`}>
                          <p className="text-sm whitespace-pre-wrap">{message.text}</p>

                          <p className="text-xs opacity-70 mt-1">
                            {formatTime(message.timestamp)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* مؤشر الكتابة */}
                  {isTyping && (
                    <div className="flex justify-start">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center">
                          <Bot className="w-4 h-4" />
                        </div>
                        <div className="bg-gray-100 rounded-lg p-3">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div ref={messagesEndRef} />
              </ScrollArea>

              {/* الاقتراحات السريعة */}
              {showSuggestions && currentSuggestions.length > 0 && (
                <div className="p-4 border-t bg-gray-50">
                  <p className="text-sm text-gray-600 mb-2">اقتراحات سريعة:</p>
                  <div className="flex flex-wrap gap-2">
                    {currentSuggestions.map((suggestion, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="text-xs h-8"
                      >
                        {suggestion}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              <Separator />

              {/* منطقة الإدخال */}
              <div className="p-4">
                <div className="flex space-x-2 rtl:space-x-reverse">
                  <Input
                    ref={inputRef}
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="اكتب رسالتك هنا..."
                    disabled={isTyping}
                    className="flex-1"
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!inputMessage.trim() || isTyping}
                    size="sm"
                    className="px-3"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>

                {/* تحذير خصوصية بسيط */}
                <div className="mt-2 text-center">
                  <p className="text-xs text-muted-foreground">
                    💡 للاستفسارات العامة فقط - لا تشارك معلومات شخصية حساسة
                  </p>
                </div>
              </div>
            </CardContent>
          </>
        )}
      </Card>
    </div>
  );
};

export default ChatbotWidget;
