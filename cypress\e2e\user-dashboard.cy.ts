/// <reference types="cypress" />

/**
 * اختبارات لوحة تحكم المستخدم
 * تختبر جميع وظائف حساب العميل والطلبات والملف الشخصي
 */

describe('👤 لوحة تحكم المستخدم', () => {
  beforeEach(() => {
    // إعداد البيانات الوهمية
    cy.mockLogin('customer')
    cy.mockFirebaseAuth()
    cy.mockLoyaltyProgram()
    
    // زيارة لوحة تحكم المستخدم
    cy.visitWithLocale('/dashboard')
    cy.waitForLoadingToFinish()
  })

  afterEach(() => {
    cy.mockLogout()
  })

  describe('📊 لوحة التحكم الرئيسية', () => {
    it('يجب أن تعرض لوحة تحكم المستخدم بشكل صحيح', () => {
      // التحقق من وجود العناصر الأساسية
      cy.get('[data-testid="user-dashboard"]').should('be.visible')
      cy.shouldContainArabicText('لوحة التحكم')
      cy.shouldContainArabicText('مرحباً')
      cy.shouldContainArabicText('طلباتي')
      cy.shouldContainArabicText('ملفي الشخصي')
      
      // التحقق من وجود الإحصائيات الشخصية
      cy.get('[data-testid="user-stats"]').should('be.visible')
      cy.get('[data-testid="total-orders"]').should('be.visible')
      cy.get('[data-testid="total-spent"]').should('be.visible')
      cy.get('[data-testid="loyalty-points"]').should('be.visible')
      cy.get('[data-testid="saved-items"]').should('be.visible')
    })

    it('يجب أن تعرض الطلبات الأخيرة', () => {
      // التحقق من قائمة الطلبات الأخيرة
      cy.get('[data-testid="recent-orders"]').should('be.visible')
      cy.shouldContainArabicText('طلباتي الأخيرة')
      
      // التحقق من وجود بيانات الطلبات
      cy.get('[data-testid="order-item"]').should('have.length.at.least', 1)
      cy.get('[data-testid="order-number"]').should('be.visible')
      cy.get('[data-testid="order-date"]').should('be.visible')
      cy.get('[data-testid="order-status"]').should('be.visible')
      cy.get('[data-testid="order-total"]').should('be.visible')
    })

    it('يجب أن تعرض المنتجات المفضلة', () => {
      // التحقق من قائمة المفضلة
      cy.get('[data-testid="favorite-products"]').should('be.visible')
      cy.shouldContainArabicText('منتجاتي المفضلة')
      
      // التحقق من وجود منتجات مفضلة
      cy.get('[data-testid="favorite-item"]').should('have.length.at.least', 1)
      cy.get('[data-testid="product-name"]').should('be.visible')
      cy.get('[data-testid="product-price"]').should('be.visible')
      cy.get('[data-testid="add-to-cart"]').should('be.visible')
    })

    it('يجب أن تعرض نقاط الولاء', () => {
      // التحقق من قسم نقاط الولاء
      cy.get('[data-testid="loyalty-section"]').should('be.visible')
      cy.shouldContainArabicText('نقاط الولاء')
      
      // التحقق من رصيد النقاط
      cy.get('[data-testid="points-balance"]').should('be.visible')
      cy.get('[data-testid="current-tier"]').should('be.visible')
      cy.get('[data-testid="next-tier-progress"]').should('be.visible')
      
      // التحقق من المكافآت المتاحة
      cy.get('[data-testid="available-rewards"]').should('be.visible')
      cy.get('[data-testid="reward-item"]').should('have.length.at.least', 1)
    })

    it('يجب أن تعرض الإشعارات', () => {
      // التحقق من قسم الإشعارات
      cy.get('[data-testid="notifications-section"]').should('be.visible')
      cy.shouldContainArabicText('الإشعارات')
      
      // التحقق من وجود إشعارات
      cy.get('[data-testid="notification-item"]').should('have.length.at.least', 1)
      cy.get('[data-testid="notification-title"]').should('be.visible')
      cy.get('[data-testid="notification-date"]').should('be.visible')
      cy.get('[data-testid="notification-status"]').should('be.visible')
    })
  })

  describe('📋 إدارة الطلبات', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة الطلبات
      cy.visitWithLocale('/dashboard/orders')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض قائمة الطلبات', () => {
      // التحقق من وجود جدول الطلبات
      cy.get('[data-testid="orders-table"]').should('be.visible')
      
      // التحقق من أعمدة الجدول
      cy.shouldContainArabicText('رقم الطلب')
      cy.shouldContainArabicText('التاريخ')
      cy.shouldContainArabicText('المبلغ')
      cy.shouldContainArabicText('الحالة')
      cy.shouldContainArabicText('الإجراءات')
    })

    it('يجب أن يعرض تفاصيل الطلب', () => {
      // عرض تفاصيل الطلب
      cy.get('[data-testid="order-row"]').first().within(() => {
        cy.get('[data-testid="view-order"]').click()
      })
      
      // التحقق من فتح صفحة تفاصيل الطلب
      cy.get('[data-testid="order-details"]').should('be.visible')
      cy.shouldContainArabicText('تفاصيل الطلب')
      
      // التحقق من وجود المعلومات
      cy.get('[data-testid="order-info"]').should('be.visible')
      cy.get('[data-testid="order-items"]').should('be.visible')
      cy.get('[data-testid="shipping-info"]').should('be.visible')
      cy.get('[data-testid="payment-info"]').should('be.visible')
      cy.get('[data-testid="order-timeline"]').should('be.visible')
    })

    it('يجب أن يتتبع الطلب', () => {
      // فتح تفاصيل طلب مشحون
      cy.get('[data-testid="shipped-order"]').first().click()
      
      // التحقق من معلومات التتبع
      cy.get('[data-testid="tracking-info"]').should('be.visible')
      cy.get('[data-testid="tracking-number"]').should('be.visible')
      cy.get('[data-testid="shipping-status"]').should('be.visible')
      cy.get('[data-testid="estimated-delivery"]').should('be.visible')
      
      // التحقق من خريطة التتبع
      cy.get('[data-testid="tracking-map"]').should('be.visible')
      cy.get('[data-testid="delivery-progress"]').should('be.visible')
    })

    it('يجب أن يلغي الطلب', () => {
      // إلغاء طلب قابل للإلغاء
      cy.get('[data-testid="cancellable-order"]').first().within(() => {
        cy.get('[data-testid="cancel-order"]').click()
      })
      
      // تأكيد الإلغاء
      cy.get('[data-testid="cancel-reason"]').select('changed_mind')
      cy.get('[data-testid="cancel-notes"]').type('لم أعد بحاجة للمنتج')
      cy.get('[data-testid="confirm-cancel"]').click()
      
      // التحقق من نجاح الإلغاء
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إلغاء الطلب بنجاح')
    })

    it('يجب أن يطلب إرجاع المنتج', () => {
      // طلب إرجاع منتج
      cy.get('[data-testid="delivered-order"]').first().click()
      cy.get('[data-testid="request-return"]').click()
      
      // ملء نموذج الإرجاع
      cy.get('[data-testid="return-reason"]').select('defective')
      cy.get('[data-testid="return-description"]').type('المنتج وصل معيب')
      cy.get('[data-testid="return-images"]').selectFile('cypress/fixtures/return-evidence.jpg')
      
      // إرسال طلب الإرجاع
      cy.get('[data-testid="submit-return"]').click()
      
      // التحقق من نجاح الطلب
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إرسال طلب الإرجاع بنجاح')
    })

    it('يجب أن يفلتر الطلبات', () => {
      // فلترة حسب الحالة
      cy.get('[data-testid="status-filter"]').select('delivered')
      
      // التحقق من الفلترة
      cy.get('[data-testid="order-row"]').each(($row) => {
        cy.wrap($row).find('[data-testid="order-status"]')
          .should('contain.text', 'تم التسليم')
      })
      
      // فلترة حسب التاريخ
      cy.get('[data-testid="date-filter"]').select('last_month')
      
      // التحقق من تحديث النتائج
      cy.get('[data-testid="orders-table"]').should('be.visible')
    })

    it('يجب أن يبحث في الطلبات', () => {
      // البحث برقم الطلب
      cy.get('[data-testid="order-search"]').type('ORD-001')
      cy.get('[data-testid="search-btn"]').click()
      
      // التحقق من نتائج البحث
      cy.get('[data-testid="order-row"]').should('contain.text', 'ORD-001')
      
      // مسح البحث
      cy.get('[data-testid="clear-search"]').click()
      cy.get('[data-testid="order-row"]').should('have.length.at.least', 1)
    })
  })

  describe('👤 الملف الشخصي', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة الملف الشخصي
      cy.visitWithLocale('/profile')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن يعرض معلومات الملف الشخصي', () => {
      // التحقق من وجود معلومات الملف الشخصي
      cy.get('[data-testid="profile-info"]').should('be.visible')
      cy.shouldContainArabicText('الملف الشخصي')
      
      // التحقق من المعلومات الأساسية
      cy.get('[data-testid="user-name"]').should('be.visible')
      cy.get('[data-testid="user-email"]').should('be.visible')
      cy.get('[data-testid="user-phone"]').should('be.visible')
      cy.get('[data-testid="user-avatar"]').should('be.visible')
    })

    it('يجب أن يحرر المعلومات الشخصية', () => {
      // فتح نموذج التحرير
      cy.get('[data-testid="edit-profile"]').click()
      
      // تحديث المعلومات
      cy.fillForm({
        'first-name': 'أحمد',
        'last-name': 'محمد',
        'phone': '+966501234567',
        'date-of-birth': '1990-01-01'
      })
      
      // رفع صورة شخصية جديدة
      cy.get('[data-testid="avatar-upload"]').selectFile('cypress/fixtures/avatar.jpg')
      
      // حفظ التغييرات
      cy.get('[data-testid="save-profile"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث الملف الشخصي بنجاح')
    })

    it('يجب أن يدير العناوين', () => {
      // الانتقال إلى إدارة العناوين
      cy.get('[data-testid="manage-addresses"]').click()
      
      // إضافة عنوان جديد
      cy.get('[data-testid="add-address"]').click()
      
      // ملء نموذج العنوان
      cy.fillForm({
        'address-title': 'المنزل',
        'street': 'شارع الملك فهد',
        'building': '123',
        'apartment': '4أ',
        'city': 'الرياض',
        'postal-code': '12345'
      })
      
      // تحديد الموقع على الخريطة
      cy.get('[data-testid="map-picker"]').click(200, 200)
      
      // حفظ العنوان
      cy.get('[data-testid="save-address"]').click()
      
      // التحقق من إضافة العنوان
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة العنوان بنجاح')
    })

    it('يجب أن يحرر عنوان موجود', () => {
      cy.get('[data-testid="manage-addresses"]').click()
      
      // تحرير عنوان موجود
      cy.get('[data-testid="address-item"]').first().within(() => {
        cy.get('[data-testid="edit-address"]').click()
      })
      
      // تحديث العنوان
      cy.get('[data-testid="street"]').clear().type('شارع العليا')
      cy.get('[data-testid="building"]').clear().type('456')
      
      // حفظ التغييرات
      cy.get('[data-testid="save-address"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
    })

    it('يجب أن يحذف عنوان', () => {
      cy.get('[data-testid="manage-addresses"]').click()
      
      // حذف عنوان
      cy.get('[data-testid="address-item"]').first().within(() => {
        cy.get('[data-testid="delete-address"]').click()
      })
      
      // تأكيد الحذف
      cy.get('[data-testid="confirm-delete"]').click()
      
      // التحقق من نجاح الحذف
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم حذف العنوان بنجاح')
    })

    it('يجب أن يغير كلمة المرور', () => {
      // فتح نموذج تغيير كلمة المرور
      cy.get('[data-testid="change-password"]').click()
      
      // ملء النموذج
      cy.fillForm({
        'current-password': 'oldpassword123',
        'new-password': 'newpassword123',
        'confirm-password': 'newpassword123'
      })
      
      // حفظ كلمة المرور الجديدة
      cy.get('[data-testid="save-password"]').click()
      
      // التحقق من نجاح التغيير
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تغيير كلمة المرور بنجاح')
    })
  })

  describe('🔔 إدارة الإشعارات', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة الإشعارات
      cy.visitWithLocale('/dashboard/notifications')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض قائمة الإشعارات', () => {
      // التحقق من وجود قائمة الإشعارات
      cy.get('[data-testid="notifications-list"]').should('be.visible')
      cy.shouldContainArabicText('الإشعارات')
      
      // التحقق من وجود إشعارات
      cy.get('[data-testid="notification-item"]').should('have.length.at.least', 1)
      cy.get('[data-testid="notification-title"]').should('be.visible')
      cy.get('[data-testid="notification-content"]').should('be.visible')
      cy.get('[data-testid="notification-date"]').should('be.visible')
    })

    it('يجب أن يقرأ الإشعارات', () => {
      // قراءة إشعار غير مقروء
      cy.get('[data-testid="unread-notification"]').first().click()
      
      // التحقق من تحديث حالة القراءة
      cy.get('[data-testid="notification-read-status"]').should('contain.text', 'مقروء')
    })

    it('يجب أن يحذف الإشعارات', () => {
      // حذف إشعار
      cy.get('[data-testid="notification-item"]').first().within(() => {
        cy.get('[data-testid="delete-notification"]').click()
      })
      
      // تأكيد الحذف
      cy.get('[data-testid="confirm-delete"]').click()
      
      // التحقق من نجاح الحذف
      cy.get('[data-testid="success-message"]').should('be.visible')
    })

    it('يجب أن يدير إعدادات الإشعارات', () => {
      // فتح إعدادات الإشعارات
      cy.get('[data-testid="notification-settings"]').click()
      
      // تخصيص أنواع الإشعارات
      cy.get('[data-testid="order-notifications"]').check()
      cy.get('[data-testid="promotion-notifications"]').check()
      cy.get('[data-testid="loyalty-notifications"]').check()
      
      // اختيار طرق الإشعار
      cy.get('[data-testid="email-notifications"]').check()
      cy.get('[data-testid="sms-notifications"]').uncheck()
      cy.get('[data-testid="push-notifications"]').check()
      
      // حفظ الإعدادات
      cy.get('[data-testid="save-notification-settings"]').click()
      
      // التحقق من نجاح الحفظ
      cy.get('[data-testid="success-message"]').should('be.visible')
    })
  })

  describe('⭐ نقاط الولاء والمكافآت', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة الولاء
      cy.visitWithLocale('/loyalty')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض رصيد النقاط', () => {
      // التحقق من عرض رصيد النقاط
      cy.get('[data-testid="points-balance"]').should('be.visible')
      cy.get('[data-testid="available-points"]').should('contain.text', '1000')
      cy.get('[data-testid="current-tier"]').should('be.visible')
      cy.get('[data-testid="next-tier-progress"]').should('be.visible')
    })

    it('يجب أن تعرض تاريخ النقاط', () => {
      // عرض تاريخ النقاط
      cy.get('[data-testid="points-history"]').click()
      
      // التحقق من عرض التاريخ
      cy.get('[data-testid="points-transactions"]').should('be.visible')
      cy.get('[data-testid="transaction-item"]').should('have.length.at.least', 1)
      
      // التحقق من تفاصيل المعاملة
      cy.get('[data-testid="transaction-date"]').should('be.visible')
      cy.get('[data-testid="transaction-type"]').should('be.visible')
      cy.get('[data-testid="transaction-points"]').should('be.visible')
      cy.get('[data-testid="transaction-description"]').should('be.visible')
    })

    it('يجب أن يستبدل المكافآت', () => {
      // استبدال مكافأة
      cy.get('[data-testid="reward-card"]').first().within(() => {
        cy.get('[data-testid="redeem-btn"]').click()
      })
      
      // تأكيد الاستبدال
      cy.get('[data-testid="confirm-redemption"]').click()
      
      // التحقق من نجاح الاستبدال
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم استبدال المكافأة بنجاح')
    })
  })

  describe('📱 التجاوب والأجهزة المحمولة', () => {
    it('يجب أن تعمل على الأجهزة المحمولة', () => {
      // اختبار التجاوب على الهاتف
      cy.viewport('iphone-x')
      
      // التحقق من التجاوب
      cy.get('[data-testid="user-dashboard"]').should('be.visible')
      cy.get('[data-testid="mobile-navigation"]').should('be.visible')
      
      // اختبار القائمة المحمولة
      cy.get('[data-testid="mobile-menu-toggle"]').click()
      cy.get('[data-testid="mobile-menu"]').should('be.visible')
      
      // اختبار الإحصائيات على الهاتف
      cy.get('[data-testid="mobile-stats"]').should('be.visible')
    })

    it('يجب أن تعمل على الأجهزة اللوحية', () => {
      // اختبار التجاوب على الجهاز اللوحي
      cy.viewport('ipad-2')
      
      // التحقق من التجاوب
      cy.get('[data-testid="user-dashboard"]').should('be.visible')
      cy.get('[data-testid="tablet-layout"]').should('be.visible')
      
      // اختبار التنقل على الجهاز اللوحي
      cy.get('[data-testid="tablet-navigation"]').should('be.visible')
    })
  })
})
