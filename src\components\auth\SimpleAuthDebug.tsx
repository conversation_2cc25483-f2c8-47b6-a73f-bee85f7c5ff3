// src/components/auth/SimpleAuthDebug.tsx
"use client";

import { useEffect, useState } from 'react';
import { auth } from '@/lib/firebase';
import { onAuthStateChanged } from 'firebase/auth';

export default function SimpleAuthDebug() {
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setDebugInfo({
        hasUser: !!currentUser,
        userId: currentUser?.uid?.substring(0, 8),
        email: currentUser?.email,
        timestamp: new Date().toLocaleTimeString(),
        url: window.location.href,
        pathname: window.location.pathname,
        search: window.location.search,
      });
    });

    return () => unsubscribe();
  }, []);

  // عرض معلومات التصحيح فقط في بيئة التطوير
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed top-4 left-4 bg-black/90 text-white p-3 rounded-lg text-xs max-w-xs z-50">
      <h3 className="font-bold mb-2 text-yellow-400">🔍 Auth Debug</h3>
      <div className="space-y-1 text-green-300">
        <div>User: {debugInfo.hasUser ? '✅' : '❌'}</div>
        {debugInfo.userId && <div>ID: {debugInfo.userId}...</div>}
        {debugInfo.email && <div>Email: {debugInfo.email}</div>}
        <div>Time: {debugInfo.timestamp}</div>
        <div className="text-blue-300">URL: {debugInfo.pathname}</div>
        {debugInfo.search && <div className="text-purple-300">Params: {debugInfo.search}</div>}
      </div>
    </div>
  );
}
