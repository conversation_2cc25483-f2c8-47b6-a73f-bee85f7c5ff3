# 🚀 Netlify Functions - مِخْلاة

## 📁 مجلد Functions

هذا المجلد مخصص لـ Netlify Functions المخصصة.

### 🎯 الغرض
- إزالة تحذير Netlify حول المجلد المفقود
- مكان لإضافة functions مخصصة في المستقبل

### 📝 ملاحظات
- حالياً يتم استخدام Next.js API routes بدلاً من Netlify Functions
- جميع API endpoints موجودة في `src/app/api/`
- هذا المجلد جاهز للاستخدام عند الحاجة

### 🔗 المراجع
- [Netlify Functions Documentation](https://docs.netlify.com/functions/overview/)
- [Next.js API Routes](https://nextjs.org/docs/api-routes/introduction)
