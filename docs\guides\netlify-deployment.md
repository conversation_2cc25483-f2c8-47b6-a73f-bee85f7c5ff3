# دليل النشر على Netlify - مشروع مِخْلاة

## 🎯 نظرة عامة

دليل شامل لنشر مشروع مِخْلاة على منصة Netlify بنجاح.

## 📋 المتطلبات الأساسية

### 1. إعد<PERSON> المشروع محلياً:
```bash
# تثبيت التبعيات
npm install

# اختبار البناء محلياً
npm run build

# التأكد من عمل التطبيق
npm run dev
```

### 2. ملفات التكوين المطلوبة:
- ✅ `netlify.toml` - إعدادات النشر
- ✅ `package.json` - scripts البناء
- ✅ `next.config.ts` - تكوين Next.js
- ✅ `.env.example` - مثال متغيرات البيئة

## 🚀 خطوات النشر

### المرحلة 1: إعداد Repository

1. **رفع الكود إلى GitHub:**
```bash
git add .
git commit -m "Ready for Netlify deployment"
git push origin main
```

2. **التأكد من وجود الملفات:**
- netlify.toml في الجذر
- package.json محدث
- جميع ملفات src/

### المرحلة 2: إعداد Netlify

1. **إنشاء موقع جديد:**
   - اذهب إلى [Netlify Dashboard](https://app.netlify.com/)
   - اضغط "New site from Git"
   - اختر GitHub repository

2. **إعدادات البناء:**
```
Build command: npm run build
Publish directory: .next
```

3. **متغيرات البيئة:**
```
GOOGLE_AI_API_KEY=your_google_ai_api_key
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

### المرحلة 3: تحسين الأداء

1. **إعدادات متقدمة في netlify.toml:**
```toml
[build.environment]
  NODE_OPTIONS = "--max-old-space-size=4096"
  NPM_FLAGS = "--production=false"
  NETLIFY_CACHE_NEXTJS = "true"
  NEXT_TELEMETRY_DISABLED = "1"
```

2. **تحسين الصور:**
```toml
[build.processing.images]
  compress = true
```

3. **ضغط الملفات:**
```toml
[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true
```

## 🔧 إعدادات خاصة بمِخْلاة

### 1. نظام الذكاء الاصطناعي:
- يستخدم النظام السحابي (Google Gemini)
- لا حاجة لرفع نماذج محلية
- حجم صغير ومحسن للنشر

### 2. نظام التوطين:
- دعم العربية والإنجليزية
- إعادة توجيه تلقائية حسب اللغة
- ترجمات محسنة

### 3. Firebase Integration:
- Firestore للبيانات
- Firebase Auth للمصادقة
- Firebase Storage للملفات

## 📊 مراقبة الأداء

### 1. مؤشرات مهمة:
- **وقت البناء**: يجب أن يكون < 5 دقائق
- **حجم البناء**: يجب أن يكون < 500MB
- **عدد الصفحات**: 97 صفحة حالياً
- **First Load JS**: متوسط 300KB

### 2. تحسينات الأداء:
```javascript
// في next.config.ts
experimental: {
  optimizeCss: true,
  scrollRestoration: true,
  optimizePackageImports: ['@/locales'],
}
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة:
1. **Build failed**: راجع [دليل استكشاف الأخطاء](./netlify-troubleshooting.md)
2. **Environment variables**: تأكد من إضافتها في Netlify Dashboard
3. **Import errors**: تحقق من مسارات الاستيراد

### أدوات التشخيص:
```bash
# اختبار البناء محلياً
npm run build

# فحص الأخطاء
npm run lint

# اختبار التطبيق
npm run dev
```

## ✅ قائمة التحقق النهائية

قبل النشر، تأكد من:
- [ ] البناء المحلي يعمل بدون أخطاء
- [ ] جميع متغيرات البيئة مضبوطة
- [ ] netlify.toml يحتوي على الإعدادات الصحيحة
- [ ] Firebase مكون بشكل صحيح
- [ ] نظام الذكاء الاصطناعي يعمل
- [ ] الترجمات محدثة
- [ ] الاختبارات تمر بنجاح

## 🎉 بعد النشر الناجح

1. **اختبر الموقع المباشر**
2. **تحقق من جميع الصفحات**
3. **اختبر نظام المصادقة**
4. **تأكد من عمل الذكاء الاصطناعي**
5. **راقب الأداء والأخطاء**

## 📚 مراجع إضافية

- [وثائق Netlify](https://docs.netlify.com/)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [Firebase Setup](https://firebase.google.com/docs)
- [دليل نظام الذكاء الاصطناعي](./ai-approval-system.md)
