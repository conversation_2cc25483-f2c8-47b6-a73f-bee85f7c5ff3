// src/app/[locale]/auth-success/page.tsx
import type { Locale } from '@/lib/i18n';
import SimpleAuthSuccess from '@/components/auth/SimpleAuthSuccess';

interface AuthSuccessPageProps {
  params: { locale: Locale };
  searchParams?: { userType?: string };
}

export default async function AuthSuccessPage({ params, searchParams }: AuthSuccessPageProps) {
  const paramsData = await Promise.resolve(params);
  const searchParamsData = await Promise.resolve(searchParams);
  const locale = paramsData.locale;
  const userType = searchParamsData?.userType || 'customer';

  return <SimpleAuthSuccess locale={locale} userType={userType} />;
}
