# ملف إعدادات البيئة المحلية - مِخْلاة
# انسخ هذا الملف إلى .env.local وحدث القيم حسب بيئتك

# ===== إعدادات Firebase =====
NEXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id

# ===== إعدادات Cloudinary =====
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your-cloud-name
NEXT_PUBLIC_CLOUDINARY_API_KEY=your-api-key
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=your-upload-preset

# ===== إعدادات الذكاء الاصطناعي المحلي =====
# تفعيل النظام المحلي (true للإنتاج، false للتطوير)
USE_LOCAL_AI=true

# إعدادات الأمان
ENCRYPT_EXTERNAL=false          # تشفير الطلبات الخارجية
ANONYMIZE_DATA=true             # إخفاء هوية البيانات
AUDIT_LOGGING=true              # تسجيل المراجعة
AI_LOCAL_ONLY=true              # استخدام النماذج المحلية فقط
AI_SECURITY_LEVEL=high          # مستوى الأمان

# مسارات النماذج
AI_MODELS_PATH=/ai-models       # مسار النماذج
ONNX_WASM_PATH=/ai-models/wasm  # مسار ملفات WASM

# إعدادات الأداء
AI_MAX_CONCURRENT_ANALYSIS=3    # أقصى عدد تحليلات متزامنة
AI_ANALYSIS_TIMEOUT=30000       # مهلة زمنية بالمللي ثانية
AI_CACHE_ENABLED=true           # تفعيل التخزين المؤقت
MAX_DOCUMENT_SIZE_MB=10         # أقصى حجم للمستند

# إعدادات التشفير (للطوارئ فقط)
DOCUMENT_ENCRYPTION_KEY=change-this-in-production-to-secure-key
ENCRYPTION_ALGORITHM=AES-256-GCM

# إعدادات البيئة
NODE_ENV=development            # development, production, staging
NEXT_TELEMETRY_DISABLED=1       # تعطيل إحصائيات Next.js
NODE_OPTIONS=--max-old-space-size=4096  # تحسين الذاكرة

# إعدادات النشر
NETLIFY_DEPLOYMENT=false        # true عند النشر على Netlify
NETLIFY_CACHE_NEXTJS=true       # تفعيل تخزين Next.js المؤقت

# ===== إعدادات التطوير (اختيارية) =====
# تفعيل وضع التطوير للذكاء الاصطناعي
DEVELOPMENT_MODE=true           # true للتطوير
MOCK_AI_RESPONSES=false         # محاكاة استجابات الذكاء الاصطناعي
DEBUG_AI_ANALYSIS=false         # تصحيح تحليل الذكاء الاصطناعي

# إعدادات OCR المحلي
TESSERACT_LANGUAGES=ara+eng     # اللغات المدعومة
TESSERACT_CONFIG=--psm 6 --oem 3  # إعدادات Tesseract

# إعدادات الجودة
MIN_CONFIDENCE_THRESHOLD=80     # أقل نسبة ثقة مقبولة
MAX_CONFIDENCE_FOR_EXTERNAL=85 # أقصى ثقة للتحليل الخارجي
REQUIRE_MANUAL_REVIEW_BELOW=70  # مراجعة يدوية تحت هذه النسبة

# ===== إعدادات الخدمات الخارجية (اختيارية ومشفرة) =====
# استخدم فقط إذا كان التشفير مفعل وللطوارئ
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_KEY_FILE=path/to/service-account.json
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=me-south-1
AZURE_COGNITIVE_SERVICES_KEY=your-key
AZURE_COGNITIVE_SERVICES_ENDPOINT=your-endpoint

# ===== إعدادات المراقبة =====
MONITORING_ENABLED=true         # تفعيل المراقبة
METRICS_ENDPOINT=/metrics       # نقطة نهاية المقاييس
HEALTH_CHECK_ENDPOINT=/health   # نقطة فحص الصحة

# ===== إعدادات التنبيهات =====
ALERT_ON_LOW_CONFIDENCE=true    # تنبيه عند انخفاض الثقة
ALERT_ON_SECURITY_BREACH=true   # تنبيه عند خرق الأمان
ALERT_EMAIL=<EMAIL>    # بريد التنبيهات

# ===== ملاحظات مهمة =====
# 1. غير جميع كلمات المرور والمفاتيح الافتراضية
# 2. استخدم HTTPS في جميع الاتصالات
# 3. فعل جميع إعدادات الأمان في الإنتاج
# 4. راجع السجلات بانتظام
# 5. احتفظ بنسخ احتياطية مشفرة
