{"build_info": {"timestamp": "2025-07-30 13:54:59", "total_files": 41, "total_size_mb": 39.05, "netlify_compatible": true}, "files": [{"path": "build_manifest.json", "size_mb": 0.0, "type": "json"}, {"path": "build_report.json", "size_mb": 0.0, "type": "json"}, {"path": "cleanup-report.json", "size_mb": 0.0, "type": "json"}, {"path": "local-system-report.json", "size_mb": 0.0, "type": "json"}, {"path": "configs\\ai-config.json", "size_mb": 0.01, "type": "json"}, {"path": "configs\\cloud-ai-config.json", "size_mb": 0.0, "type": "json"}, {"path": "configs\\enhanced-privacy-config.json", "size_mb": 0.0, "type": "json"}, {"path": "configs\\local-models-config.json", "size_mb": 0.01, "type": "json"}, {"path": "configs\\local-privacy-config.json", "size_mb": 0.0, "type": "json"}, {"path": "configs\\local_privacy_config.json", "size_mb": 0.0, "type": "json"}, {"path": "configs\\netlify-deployment.json", "size_mb": 0.0, "type": "json"}, {"path": "configs\\netlify_optimized_config.json", "size_mb": 0.0, "type": "json"}, {"path": "configs\\performance-config.json", "size_mb": 0.0, "type": "json"}, {"path": "configs\\privacy-config.json", "size_mb": 0.0, "type": "json"}, {"path": "configs\\privacy-first-ai-config.json", "size_mb": 0.01, "type": "json"}, {"path": "configs\\wasm_config.json", "size_mb": 0.0, "type": "json"}, {"path": "models\\ocr\\ara.traineddata", "size_mb": 2.38, "type": "traineddata"}, {"path": "models\\ocr\\eng.traineddata", "size_mb": 22.38, "type": "traineddata"}, {"path": "scripts\\clean-local-ai.js", "size_mb": 0.01, "type": "js"}, {"path": "scripts\\cleanup-local-models.js", "size_mb": 0.01, "type": "js"}, {"path": "scripts\\download-models.js", "size_mb": 0.01, "type": "js"}, {"path": "scripts\\migrate-to-cloud-ai.js", "size_mb": 0.01, "type": "js"}, {"path": "scripts\\no-download-local.js", "size_mb": 0.01, "type": "js"}, {"path": "scripts\\setup-config.js", "size_mb": 0.01, "type": "js"}, {"path": "scripts\\setup-local-ai.js", "size_mb": 0.01, "type": "js"}, {"path": "scripts\\setup-local-only-ai.js", "size_mb": 0.02, "type": "js"}, {"path": "scripts\\test-local-ai.js", "size_mb": 0.01, "type": "js"}, {"path": "scripts\\validate-config.js", "size_mb": 0.01, "type": "js"}, {"path": "utils\\ai-manager.js", "size_mb": 0.0, "type": "js"}, {"path": "utils\\cache-manager.js", "size_mb": 0.01, "type": "js"}, {"path": "utils\\enhanced-cloud-ai-manager.js", "size_mb": 0.0, "type": "js"}, {"path": "utils\\local-ai-engine.js", "size_mb": 0.01, "type": "js"}, {"path": "utils\\local-privacy-ai-manager.js", "size_mb": 0.02, "type": "js"}, {"path": "utils\\memory-manager.js", "size_mb": 0.01, "type": "js"}, {"path": "utils\\model-loader.js", "size_mb": 0.01, "type": "js"}, {"path": "utils\\privacy-first-ai-manager.js", "size_mb": 0.02, "type": "js"}, {"path": "utils\\privacy-guardian.js", "size_mb": 0.01, "type": "js"}, {"path": "wasm\\ort-wasm.wasm", "size_mb": 9.4, "type": "wasm"}, {"path": "wasm\\tesseract-core.wasm.js", "size_mb": 4.52, "type": "js"}, {"path": "wasm\\tesseract-worker.min.js", "size_mb": 0.12, "type": "js"}, {"path": "workers\\ocr-worker.js", "size_mb": 0.02, "type": "js"}], "models_summary": {"ocr": "Tesseract WASM - 100% Local", "nlp": "Compromise.js - 100% Local", "validation": "Local Rules - 100% Local"}, "deployment_ready": true, "privacy_level": "100%"}