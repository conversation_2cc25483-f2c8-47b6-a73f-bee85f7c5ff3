'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Star, 
  Gift, 
  TrendingUp, 
  Users, 
  Award,
  Calendar,
  ShoppingBag,
  Crown,
  Sparkles
} from 'lucide-react';
import { LoyaltyCard } from '@/components/customer/LoyaltyCard';
import { LoyaltyRewards } from '@/components/customer/LoyaltyRewards';
import { loyaltyService } from '@/services/loyaltyService';
import { CustomerLoyalty, PointTransaction } from '@/types/loyalty';
import { useLocale } from '@/hooks/use-locale';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

export default function LoyaltyPage() {
  const { user } = useAuth();
  const { t } = useLocale();
  const [activeTab, setActiveTab] = useState('overview');
  const [loyaltyData, setLoyaltyData] = useState<CustomerLoyalty[]>([]);
  const [transactions, setTransactions] = useState<PointTransaction[]>([]);
  const [loading, setLoading] = useState(true);

  // للتبسيط، سنستخدم تاجر واحد كمثال
  // في التطبيق الحقيقي، يجب جلب جميع التجار التي انضم لها العميل
  const DEMO_MERCHANT_ID = 'demo-merchant-123';

  useEffect(() => {
    if (user?.uid) {
      loadLoyaltyData();
    }
  }, [user?.uid]);

  const loadLoyaltyData = async () => {
    if (!user?.uid) return;

    try {
      setLoading(true);
      
      // جلب بيانات الولاء للعميل
      const loyaltyInfo = await loyaltyService.getCustomerLoyalty(user.uid, DEMO_MERCHANT_ID);
      if (loyaltyInfo) {
        setLoyaltyData([loyaltyInfo]);
        
        // جلب معاملات النقاط
        const transactionHistory = await loyaltyService.getCustomerTransactions(
          user.uid, 
          DEMO_MERCHANT_ID, 
          50
        );
        setTransactions(transactionHistory);
      }
    } catch (error) {
      console.error('Error loading loyalty data:', error);
      toast.error('فشل في تحميل بيانات الولاء');
    } finally {
      setLoading(false);
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'earned':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'redeemed':
        return <Gift className="h-4 w-4 text-red-600" />;
      case 'bonus':
        return <Star className="h-4 w-4 text-yellow-600" />;
      case 'expired':
        return <Calendar className="h-4 w-4 text-gray-600" />;
      default:
        return <Award className="h-4 w-4" />;
    }
  };

  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'earned':
        return 'نقاط مكتسبة';
      case 'redeemed':
        return 'نقاط مستبدلة';
      case 'bonus':
        return 'نقاط إضافية';
      case 'expired':
        return 'نقاط منتهية';
      default:
        return type;
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return format(date, 'dd/MM/yyyy HH:mm', { locale: ar });
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <p>يرجى تسجيل الدخول للوصول إلى برنامج الولاء</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* العنوان الرئيسي */}
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">برنامج الولاء</h1>
        <p className="text-gray-600">
          اكسب نقاط مع كل عملية شراء واستبدلها بمكافآت رائعة
        </p>
      </div>

      {/* بطاقة الولاء الرئيسية */}
      <LoyaltyCard
        customerId={user.uid}
        merchantId={DEMO_MERCHANT_ID}
        onRewardsClick={() => setActiveTab('rewards')}
      />

      {/* التبويبات */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="rewards">المكافآت</TabsTrigger>
          <TabsTrigger value="history">التاريخ</TabsTrigger>
        </TabsList>

        {/* نظرة عامة */}
        <TabsContent value="overview" className="space-y-4">
          {loyaltyData.length > 0 ? (
            <div className="grid gap-6">
              {/* إحصائيات سريعة */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">النقاط المتاحة</p>
                        <p className="text-2xl font-bold text-blue-600">
                          {loyaltyData[0].availablePoints.toLocaleString()}
                        </p>
                      </div>
                      <div className="p-3 bg-blue-100 rounded-full">
                        <Star className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                        <p className="text-2xl font-bold text-green-600">
                          {loyaltyData[0].lifetimeOrders}
                        </p>
                      </div>
                      <div className="p-3 bg-green-100 rounded-full">
                        <ShoppingBag className="h-6 w-6 text-green-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">إجمالي الإنفاق</p>
                        <p className="text-2xl font-bold text-purple-600">
                          {loyaltyData[0].lifetimeSpent.toLocaleString()} ريال
                        </p>
                      </div>
                      <div className="p-3 bg-purple-100 rounded-full">
                        <TrendingUp className="h-6 w-6 text-purple-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">النقاط المستبدلة</p>
                        <p className="text-2xl font-bold text-orange-600">
                          {loyaltyData[0].statistics.totalPointsRedeemed.toLocaleString()}
                        </p>
                      </div>
                      <div className="p-3 bg-orange-100 rounded-full">
                        <Gift className="h-6 w-6 text-orange-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* نصائح لكسب المزيد من النقاط */}
              <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-blue-600" />
                    نصائح لكسب المزيد من النقاط
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-blue-100 rounded-full">
                        <ShoppingBag className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-blue-900">تسوق بانتظام</h4>
                        <p className="text-sm text-blue-700">
                          احصل على نقاط مع كل عملية شراء
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-purple-100 rounded-full">
                        <Users className="h-4 w-4 text-purple-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-purple-900">ادع أصدقاءك</h4>
                        <p className="text-sm text-purple-700">
                          احصل على نقاط إضافية لكل صديق جديد
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-green-100 rounded-full">
                        <Calendar className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-green-900">عيد ميلادك</h4>
                        <p className="text-sm text-green-700">
                          نقاط إضافية خاصة في عيد ميلادك
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-yellow-100 rounded-full">
                        <Star className="h-4 w-4 text-yellow-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-yellow-900">العروض الخاصة</h4>
                        <p className="text-sm text-yellow-700">
                          تابع العروض للحصول على نقاط مضاعفة
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">
                  لم تنضم لأي برنامج ولاء بعد
                </h3>
                <p className="text-gray-500 mb-4">
                  ابدأ التسوق للانضمام تلقائياً لبرامج الولاء والحصول على نقاط
                </p>
                <Button>
                  ابدأ التسوق الآن
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* المكافآت */}
        <TabsContent value="rewards" className="space-y-4">
          <LoyaltyRewards
            customerId={user.uid}
            merchantId={DEMO_MERCHANT_ID}
          />
        </TabsContent>

        {/* تاريخ النقاط */}
        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>تاريخ النقاط</CardTitle>
            </CardHeader>
            <CardContent>
              {transactions.length > 0 ? (
                <div className="space-y-3">
                  {transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        {getTransactionIcon(transaction.type)}
                        <div>
                          <div className="font-medium">{transaction.description}</div>
                          <div className="text-sm text-gray-500">
                            {formatDate(transaction.createdAt)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-semibold ${
                          transaction.type === 'redeemed' ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {transaction.type === 'redeemed' ? '-' : '+'}
                          {Math.abs(transaction.points).toLocaleString()} نقطة
                        </div>
                        <div className="text-sm text-gray-500">
                          {getTransactionTypeLabel(transaction.type)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">
                    لا يوجد تاريخ للنقاط
                  </h3>
                  <p className="text-gray-500">
                    ستظهر معاملات النقاط هنا عند بدء التسوق
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
