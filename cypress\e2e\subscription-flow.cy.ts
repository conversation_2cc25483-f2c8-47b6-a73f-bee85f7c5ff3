// cypress/e2e/subscription-flow.cy.ts

describe('نظام الاشتراكات', () => {
  beforeEach(() => {
    // زيارة صفحة الأسعار
    cy.visit('http://localhost:9002/ar/pricing');
    cy.wait(2000);
  });

  it('يجب أن تعرض صفحة الأسعار بشكل صحيح', () => {
    // التحقق من وجود العنوان
    cy.contains('خطط الاشتراك').should('be.visible');
    
    // التحقق من وجود التبويبات
    cy.contains('التجار').should('be.visible');
    cy.contains('العملاء').should('be.visible');
    cy.contains('المندوبين').should('be.visible');
    
    // التحقق من وجود بطاقات الخطط
    cy.get('[data-testid="subscription-card"]').should('have.length.greaterThan', 0);
  });

  it('يجب أن تعمل أزرار الاشتراك للخطط المجانية', () => {
    // النقر على تبويب العملاء
    cy.contains('العملاء').click();
    cy.wait(1000);
    
    // البحث عن الخطة المجانية والنقر عليها
    cy.contains('مجاني').parents('.card').within(() => {
      cy.get('button').contains('البدء').click();
    });
    
    // يجب أن يتم توجيه المستخدم لصفحة تسجيل الدخول
    cy.url().should('include', '/login');
  });

  it('يجب أن تعمل أزرار الاشتراك للخطط المدفوعة', () => {
    // النقر على تبويب العملاء
    cy.contains('العملاء').click();
    cy.wait(1000);
    
    // البحث عن الخطة المدفوعة (9.99 ريال) والنقر عليها
    cy.contains('9.99').parents('.card').within(() => {
      cy.get('button').contains('اختيار الخطة').click();
    });
    
    // يجب أن يتم توجيه المستخدم لصفحة تسجيل الدخول
    cy.url().should('include', '/login');
  });

  it('يجب أن تعمل عملية الاشتراك المجاني بعد تسجيل الدخول', () => {
    // تسجيل الدخول أولاً (محاكاة)
    cy.visit('http://localhost:9002/ar/login');
    cy.wait(2000);
    
    // ملء نموذج تسجيل الدخول
    cy.get('input[type="email"]').type('<EMAIL>');
    cy.get('input[type="password"]').type('password123');
    cy.get('button[type="submit"]').click();
    cy.wait(3000);
    
    // الذهاب لصفحة الأسعار
    cy.visit('http://localhost:9002/ar/pricing');
    cy.wait(2000);
    
    // النقر على تبويب العملاء
    cy.contains('العملاء').click();
    cy.wait(1000);
    
    // النقر على الخطة المجانية
    cy.contains('مجاني').parents('.card').within(() => {
      cy.get('button').contains('البدء').click();
    });
    
    // يجب أن يتم توجيه المستخدم لصفحة التفعيل
    cy.url().should('include', '/subscription/activate');
  });

  it('يجب أن تعمل صفحة تفعيل الخطة المجانية', () => {
    // الذهاب مباشرة لصفحة التفعيل مع خطة مجانية
    cy.visit('http://localhost:9002/ar/subscription/activate?plan=customer-basic');
    cy.wait(2000);
    
    // التحقق من وجود العناصر المطلوبة
    cy.contains('تفعيل الخطة المجانية').should('be.visible');
    cy.contains('العميل الأساسي').should('be.visible');
    cy.contains('مجاني').should('be.visible');
    
    // التحقق من وجود زر التفعيل
    cy.contains('تفعيل الآن').should('be.visible');
  });

  it('يجب أن تعمل صفحة الاشتراك المدفوع', () => {
    // الذهاب مباشرة لصفحة الاشتراك مع خطة مدفوعة
    cy.visit('http://localhost:9002/ar/subscription?plan=customer-premium');
    cy.wait(2000);
    
    // التحقق من وجود العناصر المطلوبة
    cy.contains('إتمام الاشتراك').should('be.visible');
    cy.contains('العميل المميز').should('be.visible');
    cy.contains('9.99').should('be.visible');
    
    // التحقق من وجود نموذج الدفع
    cy.contains('تفاصيل الدفع').should('be.visible');
  });

  it('يجب أن تعمل صفحة النجاح', () => {
    // الذهاب مباشرة لصفحة النجاح
    cy.visit('http://localhost:9002/ar/subscription/success?plan=customer-premium&type=paid');
    cy.wait(2000);
    
    // التحقق من وجود رسالة النجاح
    cy.contains('تم تفعيل الخطة المدفوعة').should('be.visible');
    cy.contains('العميل المميز').should('be.visible');
    
    // التحقق من وجود أزرار الإجراءات
    cy.contains('الذهاب للوحة التحكم').should('be.visible');
    cy.contains('عرض الملف الشخصي').should('be.visible');
  });

  it('يجب أن تعمل صفحة الإلغاء', () => {
    // الذهاب مباشرة لصفحة الإلغاء
    cy.visit('http://localhost:9002/ar/subscription/cancel?plan=customer-premium');
    cy.wait(2000);
    
    // التحقق من وجود رسالة الإلغاء
    cy.contains('تم إلغاء الاشتراك').should('be.visible');
    cy.contains('لم يتم خصم أي مبلغ').should('be.visible');
    
    // التحقق من وجود أزرار الإجراءات
    cy.contains('المحاولة مرة أخرى').should('be.visible');
    cy.contains('عرض جميع الخطط').should('be.visible');
  });

  it('يجب أن تعمل جميع أنواع الخطط (تجار، عملاء، مندوبين)', () => {
    // اختبار خطط التجار
    cy.contains('التجار').click();
    cy.wait(1000);
    cy.contains('التاجر الأساسي').should('be.visible');
    cy.contains('مجاني').should('be.visible');
    
    // اختبار خطط العملاء
    cy.contains('العملاء').click();
    cy.wait(1000);
    cy.contains('العميل الأساسي').should('be.visible');
    cy.contains('العميل المميز').should('be.visible');
    
    // اختبار خطط المندوبين
    cy.contains('المندوبين').click();
    cy.wait(1000);
    cy.contains('الخطة الأساسية').should('be.visible');
    cy.contains('مجاني').should('be.visible');
  });

  it('يجب أن تعمل الروابط والتنقل بشكل صحيح', () => {
    // النقر على تبويب العملاء
    cy.contains('العملاء').click();
    cy.wait(1000);
    
    // النقر على خطة مدفوعة
    cy.contains('9.99').parents('.card').within(() => {
      cy.get('button').click();
    });
    
    // التحقق من التوجيه الصحيح
    cy.url().should('include', '/login');
    cy.url().should('include', 'redirect=/subscription');
    cy.url().should('include', 'plan=customer-premium');
  });

  it('يجب أن تظهر الميزات بشكل صحيح في كل خطة', () => {
    // النقر على تبويب العملاء
    cy.contains('العملاء').click();
    cy.wait(1000);
    
    // التحقق من وجود الميزات في الخطة المجانية
    cy.contains('مجاني').parents('.card').within(() => {
      cy.get('svg').should('have.length.greaterThan', 0); // أيقونات الميزات
      cy.contains('تصفح جميع المنتجات').should('be.visible');
    });
    
    // التحقق من وجود الميزات في الخطة المدفوعة
    cy.contains('9.99').parents('.card').within(() => {
      cy.get('svg').should('have.length.greaterThan', 0); // أيقونات الميزات
      cy.contains('شحن مجاني').should('be.visible');
    });
  });
});
