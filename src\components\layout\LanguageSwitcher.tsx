"use client";

import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Globe, Loader2 } from 'lucide-react';
import type { Locale } from '@/lib/i18n';
import { i18n } from '@/lib/i18n';
import { useLocale, saveUserLanguagePreference } from '@/hooks/use-locale';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useState, useRef } from 'react';

export default function LanguageSwitcher({ currentLocale }: { currentLocale: Locale }) {
  const router = useRouter();
  const pathname = usePathname();
  const { t } = useLocale();
  const [isSwitching, setIsSwitching] = useState(false);
  const lastSwitchTime = useRef<number>(0);
  const switchingRef = useRef<boolean>(false);

  const toggleLocale = async () => {
    const now = Date.now();

    // حماية قوية من النقرات المتعددة والحلقة اللا نهائية
    if (isSwitching || switchingRef.current) {
      if (process.env.NODE_ENV === 'development') {
        console.log('⚠️ [LanguageSwitcher] تبديل اللغة قيد التنفيذ، تم تجاهل الطلب');
      }
      return;
    }

    // منع النقرات السريعة (أقل من ثانيتين)
    if (now - lastSwitchTime.current < 2000) {
      if (process.env.NODE_ENV === 'development') {
        console.log('⚠️ [LanguageSwitcher] نقرة سريعة جداً، تم تجاهل الطلب');
      }
      return;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('🌐 [LanguageSwitcher] بدء تبديل اللغة...');
      console.log('🌐 [LanguageSwitcher] اللغة الحالية:', currentLocale);
      console.log('🌐 [LanguageSwitcher] المسار الحالي:', pathname);
    }

    setIsSwitching(true);
    switchingRef.current = true;
    lastSwitchTime.current = now;

    try {
      // تحديد اللغة الجديدة (العكس من اللغة الحالية)
      const newLocale: Locale = currentLocale === 'en' ? 'ar' : 'en';

      // تغيير المسار باللغة الجديدة
      const newPathname = pathname.replace(`/${currentLocale}`, `/${newLocale}`);

      if (process.env.NODE_ENV === 'development') {
        console.log('🌐 [LanguageSwitcher] اللغة الجديدة:', newLocale);
        console.log('🌐 [LanguageSwitcher] المسار الجديد:', newPathname);
      }

      // التحقق من صحة المسار الجديد
      if (!newPathname.startsWith(`/${newLocale}`)) {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ [LanguageSwitcher] خطأ في تكوين المسار الجديد');
          console.error('❌ [LanguageSwitcher] المسار المتوقع يجب أن يبدأ بـ:', `/${newLocale}`);
          console.error('❌ [LanguageSwitcher] المسار الفعلي:', newPathname);
        }
        setIsSwitching(false);
        return;
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('✅ [LanguageSwitcher] بدء التنقل إلى:', newPathname);
      }

      // حفظ اللغة المفضلة الجديدة
      saveUserLanguagePreference(newLocale);

      // تحديث cookie للـ middleware
      document.cookie = `preferred-language=${newLocale}; path=/; max-age=31536000`; // سنة واحدة

      // استخدام router.push للتنقل السلس بدلاً من إعادة التحميل الكاملة
      router.push(newPathname);

      if (process.env.NODE_ENV === 'development') {
        console.log('✅ [LanguageSwitcher] تم استدعاء router.push بنجاح');
      }

      // إعادة تعيين حالة التحميل بعد فترة أطول لضمان الاستقرار
      setTimeout(() => {
        setIsSwitching(false);
        switchingRef.current = false;
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ [LanguageSwitcher] تم إنهاء حالة التحميل');
        }
      }, 3000); // زيادة الوقت إلى 3 ثوانٍ

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ [LanguageSwitcher] خطأ في تبديل اللغة:', error);
        console.error('❌ [LanguageSwitcher] تفاصيل الخطأ:', {
          currentLocale,
          pathname,
          error: error.message
        });
      }
      setIsSwitching(false);
      switchingRef.current = false;
    }
  };

  // تحديد نص التلميح الذي سيظهر عند تحويم المؤشر فوق الزر
  const tooltipText = currentLocale === 'en' ? t('switchToArabic') : t('switchToEnglish');
  // عرض اختصار اللغة الحالية
  const currentLanguage = currentLocale === 'en' ? 'EN' : 'ع';

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="gap-2 px-3"
            onClick={toggleLocale}
            disabled={isSwitching}
            aria-label={tooltipText}
          >
            {isSwitching ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Globe className="h-4 w-4" />
            )}
            <span className="font-medium">{currentLanguage}</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          {tooltipText}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
