describe('اختبار بسيط لتبديل اللغة', () => {
  it('زيارة صفحة اختيار نوع المستخدم وتبديل اللغة', () => {
    // زيارة الصفحة
    cy.visit('http://localhost:9002/ar/user-type-selection')
    cy.wait(5000)
    
    // التحقق من تحميل الصفحة
    cy.get('body').should('be.visible')
    cy.url().should('include', '/ar/user-type-selection')
    
    // البحث عن زر تبديل اللغة والنقر عليه
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("EN")').length > 0) {
        cy.log('وجدت زر EN')
        cy.get('button:contains("EN")').click()
        cy.wait(5000)
        cy.url().should('include', '/en/user-type-selection')
      } else {
        cy.log('لم أجد زر EN')
      }
    })
  })

  it('اختبار الصفحة الرئيسية', () => {
    cy.visit('http://localhost:9002')
    cy.wait(3000)
    cy.get('body').should('be.visible')
  })
})
