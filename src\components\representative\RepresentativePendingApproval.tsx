'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { useRouter } from 'next/navigation';
import { doc, getDoc, onSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  User, 
  Car, 
  FileText, 
  Phone,
  Mail,
  Calendar,
  Loader2,
  RefreshCw,
  Eye,
  Download
} from 'lucide-react';
import Link from 'next/link';
import type { RepresentativeDocument } from '@/types';

export default function RepresentativePendingApproval() {
  const { user, loading } = useAuth();
  const { t, locale } = useLocale();
  const router = useRouter();
  const [representativeData, setRepresentativeData] = useState<RepresentativeDocument | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // تحديث البيانات في الوقت الفعلي
  useEffect(() => {
    if (!user) return;

    const unsubscribe = onSnapshot(
      doc(db, 'representatives', user.uid),
      (doc) => {
        if (doc.exists()) {
          const data = doc.data() as RepresentativeDocument;
          setRepresentativeData(data);
          
          // إذا تم قبول المندوب، توجيهه للوحة التحكم
          if (data.approvalStatus === 'approved' && data.isActive) {
            router.push(`/${locale}/representative/dashboard`);
          }
        } else {
          router.push(`/${locale}/representative/signup`);
        }
        setIsLoading(false);
      },
      (error) => {
        console.error('Error listening to representative data:', error);
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [user, router, locale]);

  const refreshData = async () => {
    if (!user) return;
    
    setRefreshing(true);
    try {
      const doc = await getDoc(doc(db, 'representatives', user.uid));
      if (doc.exists()) {
        setRepresentativeData(doc.data() as RepresentativeDocument);
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getStatusInfo = () => {
    switch (representativeData?.approvalStatus) {
      case 'pending':
        return {
          title: 'طلبك قيد المراجعة',
          description: 'نحن نراجع طلب انضمامك كمندوب توصيل. سيتم إشعارك بالنتيجة خلال 24-48 ساعة.',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          icon: Clock,
          progress: 50
        };
      case 'under_review':
        return {
          title: 'تحت المراجعة المتقدمة',
          description: 'طلبك يخضع للمراجعة المتقدمة من قبل فريق الموافقات.',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          icon: Eye,
          progress: 75
        };
      case 'approved':
        return {
          title: 'تم قبول طلبك! 🎉',
          description: 'مبروك! تم قبول طلبك وتفعيل حسابك. يمكنك الآن بدء العمل.',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          icon: CheckCircle,
          progress: 100
        };
      case 'rejected':
        return {
          title: 'تم رفض طلبك',
          description: representativeData?.approvalNotes || 'تم رفض طلبك. يرجى مراجعة المتطلبات والمحاولة مرة أخرى.',
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          icon: XCircle,
          progress: 0
        };
      case 'suspended':
        return {
          title: 'تم تعليق حسابك',
          description: representativeData?.approvalNotes || 'تم تعليق حسابك مؤقتاً. يرجى التواصل مع الدعم الفني.',
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          icon: AlertCircle,
          progress: 25
        };
      default:
        return {
          title: 'حالة غير معروفة',
          description: 'حدث خطأ في تحديد حالة طلبك.',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          icon: AlertCircle,
          progress: 0
        };
    }
  };

  if (loading || isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">جاري تحميل بيانات المندوب...</p>
      </div>
    );
  }

  if (!representativeData) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl text-center">
        <AlertCircle className="h-16 w-16 text-destructive mx-auto mb-4" />
        <h1 className="text-2xl font-bold mb-2">لم يتم العثور على بيانات المندوب</h1>
        <p className="text-muted-foreground mb-6">يرجى إكمال عملية التسجيل أولاً</p>
        <Button asChild>
          <Link href={`/${locale}/representative/signup`}>
            إكمال التسجيل
          </Link>
        </Button>
      </div>
    );
  }

  const statusInfo = getStatusInfo();
  const StatusIcon = statusInfo.icon;

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">حالة طلب المندوب</h1>
        <p className="text-muted-foreground">تابع حالة طلب انضمامك كمندوب توصيل</p>
      </div>

      {/* حالة الطلب الرئيسية */}
      <Card className={`mb-8 ${statusInfo.bgColor} ${statusInfo.borderColor} border-2`}>
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <StatusIcon className={`h-20 w-20 ${statusInfo.color}`} />
          </div>
          <CardTitle className={`text-2xl ${statusInfo.color}`}>{statusInfo.title}</CardTitle>
          <CardDescription className="text-base mt-2">
            {statusInfo.description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-center">
              <Badge 
                variant={
                  representativeData.approvalStatus === 'approved' ? 'default' :
                  representativeData.approvalStatus === 'rejected' ? 'destructive' :
                  representativeData.approvalStatus === 'suspended' ? 'secondary' :
                  'outline'
                }
                className="text-sm px-4 py-2"
              >
                {representativeData.approvalStatus === 'pending' ? 'قيد المراجعة' :
                 representativeData.approvalStatus === 'under_review' ? 'تحت المراجعة' :
                 representativeData.approvalStatus === 'approved' ? 'مقبول' :
                 representativeData.approvalStatus === 'rejected' ? 'مرفوض' :
                 representativeData.approvalStatus === 'suspended' ? 'معلق' :
                 'غير محدد'}
              </Badge>
            </div>
            
            {/* شريط التقدم */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>تقدم المراجعة</span>
                <span>{statusInfo.progress}%</span>
              </div>
              <Progress value={statusInfo.progress} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* معلومات الطلب */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              معلومات الطلب
            </CardTitle>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={refreshData}
              disabled={refreshing}
            >
              {refreshing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              تحديث
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3">
              <User className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">اسم المندوب</p>
                <p className="font-medium">{representativeData.displayName}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Phone className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">رقم الهاتف</p>
                <p className="font-medium">{representativeData.phoneNumber}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Mail className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">البريد الإلكتروني</p>
                <p className="font-medium">{representativeData.email}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Car className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">نوع المركبة</p>
                <p className="font-medium">
                  {representativeData.vehicle?.type === 'motorcycle' ? 'دراجة نارية' :
                   representativeData.vehicle?.type === 'car' ? 'سيارة' :
                   representativeData.vehicle?.type === 'bicycle' ? 'دراجة هوائية' :
                   'غير محدد'}
                </p>
              </div>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">تاريخ التقديم</p>
                <p className="font-medium">
                  {representativeData.createdAt?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد'}
                </p>
              </div>
            </div>
            
            {representativeData.approvalDate && (
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">تاريخ المراجعة</p>
                  <p className="font-medium">
                    {representativeData.approvalDate.toDate?.()?.toLocaleDateString('ar-SA')}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* أزرار الإجراءات */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button asChild variant="outline">
          <Link href={`/${locale}`}>
            العودة للرئيسية
          </Link>
        </Button>
        
        {representativeData.approvalStatus === 'rejected' && (
          <Button asChild>
            <Link href={`/${locale}/representative/signup`}>
              إعادة التقديم
            </Link>
          </Button>
        )}
        
        {representativeData.approvalStatus === 'approved' && representativeData.isActive && (
          <Button asChild>
            <Link href={`/${locale}/representative/dashboard`}>
              الذهاب للوحة التحكم
            </Link>
          </Button>
        )}
      </div>
    </div>
  );
}
