import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { doc, updateDoc, serverTimestamp } from 'firebase/firestore';

interface CaptureOrderRequest {
  orderID: string;
  orderId: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: CaptureOrderRequest = await request.json();
    const { orderID, orderId } = body;

    // التحقق من البيانات المطلوبة
    if (!orderID || !orderId) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      );
    }

    const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
    const clientSecret = process.env.PAYPAL_CLIENT_SECRET;
    const baseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://api.paypal.com' 
      : 'https://api.sandbox.paypal.com';

    if (!clientId || !clientSecret) {
      return NextResponse.json(
        { error: 'PayPal credentials not configured' },
        { status: 500 }
      );
    }

    // الحصول على access token
    const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
    
    const tokenResponse = await fetch(`${baseUrl}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${auth}`
      },
      body: 'grant_type=client_credentials'
    });

    if (!tokenResponse.ok) {
      throw new Error('Failed to get PayPal access token');
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    // التقاط الطلب
    const captureResponse = await fetch(`${baseUrl}/v2/checkout/orders/${orderID}/capture`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!captureResponse.ok) {
      const errorData = await captureResponse.json();
      throw new Error(`PayPal capture failed: ${errorData.message || captureResponse.statusText}`);
    }

    const captureResult = await captureResponse.json();
    
    // تحديث حالة الطلب في قاعدة البيانات
    if (captureResult.status === 'COMPLETED') {
      try {
        // تحديث حالة الدفع
        const paymentRef = doc(db, 'payments', orderId);
        await updateDoc(paymentRef, {
          status: 'completed',
          paypalOrderId: orderID,
          paypalCaptureId: captureResult.id,
          paypalDetails: captureResult,
          completedAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });

        // تحديث حالة الطلب
        const orderRef = doc(db, 'orders', orderId);
        await updateDoc(orderRef, {
          paymentStatus: 'paid',
          status: 'confirmed',
          updatedAt: serverTimestamp()
        });

      } catch (dbError) {
        console.error('خطأ في تحديث قاعدة البيانات:', dbError);
        // لا نرجع خطأ هنا لأن الدفع تم بنجاح
      }
    }
    
    return NextResponse.json({
      id: captureResult.id,
      status: captureResult.status,
      orderID: orderID,
      orderId: orderId,
      details: captureResult
    });

  } catch (error) {
    console.error('خطأ في التقاط طلب PayPal:', error);
    return NextResponse.json(
      { 
        error: 'فشل في التقاط طلب PayPal',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}
