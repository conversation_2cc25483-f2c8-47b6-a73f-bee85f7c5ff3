/**
 * خدمة خوارزميات التطابق المتقدمة - المرحلة الأولى
 * تطبيق خوارزميات متقدمة لتحسين دقة التطابق من 96% إلى 98%
 */

export interface MatchingResult {
  similarity: number;
  confidence: number;
  algorithm: string;
  details: {
    levenshtein?: number;
    fuzzy?: number;
    soundex?: number;
    jaroWinkler?: number;
    normalized?: number;
  };
}

export interface CombinedMatchingResult {
  overallSimilarity: number;
  confidence: number;
  bestAlgorithm: string;
  results: MatchingResult[];
  recommendation: 'accept' | 'review' | 'reject';
}

export class AdvancedMatchingService {
  
  /**
   * تطبيق جميع خوارزميات التطابق والحصول على أفضل نتيجة
   */
  static calculateAdvancedSimilarity(str1: string, str2: string): CombinedMatchingResult {
    // تطبيع النصوص أولاً
    const normalized1 = this.normalizeArabicText(str1);
    const normalized2 = this.normalizeArabicText(str2);
    
    const results: MatchingResult[] = [];
    
    // 1. خوارزمية Levenshtein المحسنة
    const levenshteinResult = this.calculateLevenshteinSimilarity(normalized1, normalized2);
    results.push(levenshteinResult);
    
    // 2. خوارزمية Fuzzy String Matching
    const fuzzyResult = this.calculateFuzzySimilarity(normalized1, normalized2);
    results.push(fuzzyResult);
    
    // 3. خوارزمية Jaro-Winkler
    const jaroWinklerResult = this.calculateJaroWinklerSimilarity(normalized1, normalized2);
    results.push(jaroWinklerResult);
    
    // 4. خوارزمية Soundex للأسماء العربية
    const soundexResult = this.calculateSoundexSimilarity(normalized1, normalized2);
    results.push(soundexResult);
    
    // 5. حساب النتيجة المجمعة بالأوزان
    const combinedResult = this.calculateWeightedCombination(results);
    
    return combinedResult;
  }
  
  /**
   * تطبيع النصوص العربية
   */
  private static normalizeArabicText(text: string): string {
    if (!text) return '';
    
    return text
      // إزالة التشكيل
      .replace(/[\u064B-\u0652\u0670\u0640]/g, '')
      // توحيد الألف
      .replace(/[آأإ]/g, 'ا')
      // توحيد التاء المربوطة والهاء
      .replace(/[ة]/g, 'ه')
      // توحيد الياء
      .replace(/[ي]/g, 'ى')
      // إزالة المسافات الزائدة
      .replace(/\s+/g, ' ')
      .trim()
      .toLowerCase();
  }
  
  /**
   * خوارزمية Levenshtein المحسنة
   */
  private static calculateLevenshteinSimilarity(str1: string, str2: string): MatchingResult {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    const maxLength = Math.max(str1.length, str2.length);
    const similarity = maxLength === 0 ? 100 : ((maxLength - matrix[str2.length][str1.length]) / maxLength) * 100;
    
    return {
      similarity,
      confidence: this.calculateConfidence(similarity, 'levenshtein'),
      algorithm: 'levenshtein',
      details: { levenshtein: similarity }
    };
  }
  
  /**
   * خوارزمية Fuzzy String Matching
   */
  private static calculateFuzzySimilarity(str1: string, str2: string): MatchingResult {
    if (!str1 || !str2) {
      return {
        similarity: 0,
        confidence: 0,
        algorithm: 'fuzzy',
        details: { fuzzy: 0 }
      };
    }
    
    // تطبيق خوارزمية Token Sort Ratio
    const tokens1 = str1.split(' ').sort();
    const tokens2 = str2.split(' ').sort();
    
    const sortedStr1 = tokens1.join(' ');
    const sortedStr2 = tokens2.join(' ');
    
    // حساب التشابه بين النصوص المرتبة
    const levenshteinSim = this.calculateLevenshteinSimilarity(sortedStr1, sortedStr2);
    
    // تطبيق خوارزمية Token Set Ratio
    const set1 = new Set(tokens1);
    const set2 = new Set(tokens2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    const setRatio = union.size === 0 ? 0 : (intersection.size / union.size) * 100;
    
    // دمج النتائج
    const fuzzyScore = (levenshteinSim.similarity * 0.7) + (setRatio * 0.3);
    
    return {
      similarity: fuzzyScore,
      confidence: this.calculateConfidence(fuzzyScore, 'fuzzy'),
      algorithm: 'fuzzy',
      details: { fuzzy: fuzzyScore }
    };
  }
  
  /**
   * خوارزمية Jaro-Winkler
   */
  private static calculateJaroWinklerSimilarity(str1: string, str2: string): MatchingResult {
    if (!str1 || !str2) {
      return {
        similarity: 0,
        confidence: 0,
        algorithm: 'jaro-winkler',
        details: { jaroWinkler: 0 }
      };
    }
    
    // حساب Jaro similarity أولاً
    const jaro = this.calculateJaroSimilarity(str1, str2);
    
    // حساب prefix length (أقصى 4 أحرف)
    let prefixLength = 0;
    const maxPrefix = Math.min(4, Math.min(str1.length, str2.length));
    
    for (let i = 0; i < maxPrefix; i++) {
      if (str1[i] === str2[i]) {
        prefixLength++;
      } else {
        break;
      }
    }
    
    // تطبيق معادلة Jaro-Winkler
    const jaroWinkler = jaro + (0.1 * prefixLength * (1 - jaro));
    const similarity = jaroWinkler * 100;
    
    return {
      similarity,
      confidence: this.calculateConfidence(similarity, 'jaro-winkler'),
      algorithm: 'jaro-winkler',
      details: { jaroWinkler: similarity }
    };
  }
  
  /**
   * حساب Jaro similarity
   */
  private static calculateJaroSimilarity(str1: string, str2: string): number {
    const len1 = str1.length;
    const len2 = str2.length;
    
    if (len1 === 0 && len2 === 0) return 1;
    if (len1 === 0 || len2 === 0) return 0;
    
    const matchWindow = Math.floor(Math.max(len1, len2) / 2) - 1;
    if (matchWindow < 0) return 0;
    
    const str1Matches = new Array(len1).fill(false);
    const str2Matches = new Array(len2).fill(false);
    
    let matches = 0;
    let transpositions = 0;
    
    // العثور على المطابقات
    for (let i = 0; i < len1; i++) {
      const start = Math.max(0, i - matchWindow);
      const end = Math.min(i + matchWindow + 1, len2);
      
      for (let j = start; j < end; j++) {
        if (str2Matches[j] || str1[i] !== str2[j]) continue;
        str1Matches[i] = true;
        str2Matches[j] = true;
        matches++;
        break;
      }
    }
    
    if (matches === 0) return 0;
    
    // حساب التبديلات
    let k = 0;
    for (let i = 0; i < len1; i++) {
      if (!str1Matches[i]) continue;
      while (!str2Matches[k]) k++;
      if (str1[i] !== str2[k]) transpositions++;
      k++;
    }
    
    return (matches / len1 + matches / len2 + (matches - transpositions / 2) / matches) / 3;
  }
  
  /**
   * خوارزمية Soundex للأسماء العربية
   */
  private static calculateSoundexSimilarity(str1: string, str2: string): MatchingResult {
    const soundex1 = this.arabicSoundex(str1);
    const soundex2 = this.arabicSoundex(str2);
    
    const similarity = soundex1 === soundex2 ? 100 : 0;
    
    return {
      similarity,
      confidence: this.calculateConfidence(similarity, 'soundex'),
      algorithm: 'soundex',
      details: { soundex: similarity }
    };
  }
  
  /**
   * تطبيق Soundex للأسماء العربية
   */
  private static arabicSoundex(text: string): string {
    if (!text) return '';
    
    // تطبيع النص
    const normalized = this.normalizeArabicText(text);
    
    // خريطة الأصوات العربية
    const soundMap: { [key: string]: string } = {
      'ب': '1', 'ف': '1', 'ڤ': '1', 'پ': '1',
      'ت': '2', 'ث': '2', 'د': '2', 'ذ': '2', 'ط': '2', 'ظ': '2',
      'ج': '3', 'ش': '3', 'چ': '3',
      'ح': '4', 'خ': '4',
      'ر': '5',
      'ز': '6', 'س': '6', 'ص': '6',
      'ل': '7',
      'م': '8',
      'ن': '9'
    };
    
    let soundex = normalized.charAt(0).toUpperCase();
    let prevCode = soundMap[normalized.charAt(0)] || '';
    
    for (let i = 1; i < normalized.length && soundex.length < 4; i++) {
      const char = normalized.charAt(i);
      const code = soundMap[char] || '';
      
      if (code && code !== prevCode) {
        soundex += code;
        prevCode = code;
      } else if (!code) {
        prevCode = '';
      }
    }
    
    // إكمال الكود إلى 4 أحرف
    while (soundex.length < 4) {
      soundex += '0';
    }
    
    return soundex.substring(0, 4);
  }
  
  /**
   * حساب الثقة بناءً على الخوارزمية والنتيجة
   */
  private static calculateConfidence(similarity: number, algorithm: string): number {
    const baseConfidence = similarity;
    
    // تعديل الثقة حسب نوع الخوارزمية
    switch (algorithm) {
      case 'levenshtein':
        return Math.min(baseConfidence * 0.9, 100);
      case 'fuzzy':
        return Math.min(baseConfidence * 1.1, 100);
      case 'jaro-winkler':
        return Math.min(baseConfidence * 1.05, 100);
      case 'soundex':
        return similarity > 0 ? Math.min(baseConfidence * 0.8, 100) : 0;
      default:
        return baseConfidence;
    }
  }
  
  /**
   * دمج النتائج بالأوزان المحسنة
   */
  private static calculateWeightedCombination(results: MatchingResult[]): CombinedMatchingResult {
    // أوزان الخوارزميات
    const weights = {
      'levenshtein': 0.25,
      'fuzzy': 0.35,
      'jaro-winkler': 0.30,
      'soundex': 0.10
    };
    
    let weightedSum = 0;
    let totalWeight = 0;
    let bestResult = results[0];
    
    for (const result of results) {
      const weight = weights[result.algorithm as keyof typeof weights] || 0.25;
      weightedSum += result.similarity * weight;
      totalWeight += weight;
      
      if (result.confidence > bestResult.confidence) {
        bestResult = result;
      }
    }
    
    const overallSimilarity = totalWeight > 0 ? weightedSum / totalWeight : 0;
    const confidence = Math.min(overallSimilarity * 1.1, 100);
    
    // تحديد التوصية
    let recommendation: 'accept' | 'review' | 'reject';
    if (overallSimilarity >= 85) {
      recommendation = 'accept';
    } else if (overallSimilarity >= 70) {
      recommendation = 'review';
    } else {
      recommendation = 'reject';
    }
    
    return {
      overallSimilarity,
      confidence,
      bestAlgorithm: bestResult.algorithm,
      results,
      recommendation
    };
  }
}
