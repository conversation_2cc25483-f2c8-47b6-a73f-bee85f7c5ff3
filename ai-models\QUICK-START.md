# 🚀 دليل البدء السريع - النماذج المحلية

## 📦 **إعداد سريع (5 دقائق)**

### **1. تثبيت المكتبات**
```bash
bun install
```

### **2. تحميل النماذج**
```bash
bun run setup-ai-models
```

### **3. التحقق من النماذج**
```bash
bun run validate-ai-models
```

### **4. تشغيل المشروع**
```bash
bun run dev
```

## 🎯 **الاستخدام**

### **في الكود:**
```typescript
import { LocalAIAnalysisService } from '@/services/localAIAnalysisService';

// تحليل مستند محلياً
const result = await LocalAIAnalysisService.analyzeDocument(
  'document-url',
  'commercial_registration'
);

console.log('النتيجة:', result);
```

## 📊 **النماذج المتاحة**
- ✅ **تحليل النصوص العربية** (15MB)
- ✅ **تصنيف المستندات** (12MB)
- ✅ **OCR عربي** (25MB)
- ✅ **كشف الاحتيال** (22MB)

## 🌐 **النشر على Netlify**
1. رفع المشروع مع مجلد `ai-models/`
2. Netlify سيتعرف على الإعدادات تلقائياً
3. النماذج ستكون متاحة على `/ai-models/`

## ✅ **جاهز للاستخدام!**
النظام الآن يعمل محلياً بدون خدمات خارجية.
