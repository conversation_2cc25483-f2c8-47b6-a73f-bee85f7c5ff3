"use client";

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, Loader2, <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import { useAuth } from '@/context/AuthContext';
import { useLocale } from '@/hooks/use-locale';
import { merchantPlans, customerPlans, representativePlans } from '@/constants/plans';
import type { SubscriptionPlan } from '@/types';
import { useSubscription } from '@/hooks/useSubscription';

export default function ActivateSubscriptionPage() {
  const { t, locale } = useLocale();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const { createSubscription, activateSubscription } = useSubscription();

  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [activating, setActivating] = useState(false);
  const [activated, setActivated] = useState(false);

  // جلب الخطة المحددة من URL
  useEffect(() => {
    const planId = searchParams.get('plan');
    if (planId) {
      const allPlans = [...merchantPlans, ...customerPlans, ...representativePlans];
      const plan = allPlans.find(p => p.id === planId);
      
      if (plan && plan.priceDisplayKey === 'free') {
        setSelectedPlan(plan);
      } else {
        // إذا لم تكن خطة مجانية، العودة لصفحة الاشتراك
        router.push(`/${locale}/subscription?plan=${planId}`);
      }
    } else {
      router.push(`/${locale}/pricing`);
    }
    setLoading(false);
  }, [searchParams, router, locale]);

  // التحقق من تسجيل الدخول
  useEffect(() => {
    if (!loading && !user) {
      router.push(`/${locale}/login?redirect=/subscription/activate?plan=${selectedPlan?.id}`);
    }
  }, [user, loading, router, locale, selectedPlan]);

  const handleActivate = async () => {
    if (!selectedPlan || !user) return;

    try {
      setActivating(true);

      // إنشاء الاشتراك المجاني
      const subscriptionId = await createSubscription(selectedPlan.id);

      if (subscriptionId) {
        // تفعيل الاشتراك
        await activateSubscription(subscriptionId);

        setActivated(true);

        // توجيه المستخدم لصفحة النجاح بعد 3 ثوان
        setTimeout(() => {
          router.push(`/${locale}/subscription/success?plan=${selectedPlan.id}&type=free`);
        }, 3000);
      }

    } catch (error) {
      console.error('Error activating plan:', error);
    } finally {
      setActivating(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!selectedPlan || !user) {
    return null;
  }

  if (activated) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="text-center py-12">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h1 className="text-2xl font-bold mb-2">{t('planActivatedSuccessfully')}</h1>
              <p className="text-muted-foreground mb-4">
                {t('planActivatedMessage', { planName: t(selectedPlan.nameKey) })}
              </p>
              <p className="text-sm text-muted-foreground">
                {t('redirectingToSuccess')}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-12">
      {/* العودة للخلف */}
      <Button
        variant="ghost"
        onClick={() => router.back()}
        className="mb-6"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        {t('back')}
      </Button>

      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">{t('activateFreePlan')}</h1>
          <p className="text-muted-foreground">{t('confirmPlanActivation')}</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-center">{t(selectedPlan.nameKey)}</CardTitle>
            <CardDescription className="text-center text-2xl font-bold text-primary">
              {t('free')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="font-semibold mb-3">{t('youWillGet')}</h4>
              <ul className="space-y-2">
                {selectedPlan.features.slice(0, 5).map((feature) => (
                  <li key={feature.nameKey} className="flex items-center space-x-2 rtl:space-x-reverse">
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm">{t(feature.nameKey)}</span>
                  </li>
                ))}
                {selectedPlan.features.length > 5 && (
                  <li className="text-sm text-muted-foreground">
                    {t('andMoreFeatures', { count: selectedPlan.features.length - 5 })}
                  </li>
                )}
              </ul>
            </div>

            <div className="bg-muted p-4 rounded-lg">
              <p className="text-sm text-muted-foreground text-center">
                {t('freeActivationNote')}
              </p>
            </div>

            <Button 
              onClick={handleActivate}
              className="w-full"
              disabled={activating}
              size="lg"
            >
              {activating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t('activating')}
                </>
              ) : (
                t('activateNow')
              )}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
