'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, AlertCircle, CheckCircle, X } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

interface DocumentUploaderProps {
  userType: 'merchant' | 'representative';
  onUploadComplete: (processingId: string) => void;
  onUploadError: (error: string) => void;
}

interface UploadedFile {
  file: File;
  preview: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  processingId?: string;
  error?: string;
}

const SUPPORTED_FORMATS = {
  'image/jpeg': '.jpg',
  'image/jpg': '.jpg', 
  'image/png': '.png',
  'image/webp': '.webp',
  'image/heic': '.heic',
  'application/pdf': '.pdf'
};

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export default function DocumentUploader({ 
  userType, 
  onUploadComplete, 
  onUploadError 
}: DocumentUploaderProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // معالجة الملفات المرفوضة
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach((error: any) => {
        if (error.code === 'file-too-large') {
          onUploadError(`الملف ${file.name} كبير جداً. الحد الأقصى 10 ميجابايت`);
        } else if (error.code === 'file-invalid-type') {
          onUploadError(`نوع الملف ${file.name} غير مدعوم`);
        }
      });
    });

    // معالجة الملفات المقبولة
    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      file,
      preview: URL.createObjectURL(file),
      status: 'pending',
      progress: 0
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);
  }, [onUploadError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: SUPPORTED_FORMATS,
    maxSize: MAX_FILE_SIZE,
    multiple: true,
    disabled: isUploading
  });

  const uploadFile = async (fileIndex: number) => {
    const file = uploadedFiles[fileIndex];
    if (!file) return;

    setIsUploading(true);
    
    // تحديث حالة الملف إلى "جاري الرفع"
    setUploadedFiles(prev => prev.map((f, i) => 
      i === fileIndex ? { ...f, status: 'uploading', progress: 0 } : f
    ));

    try {
      const formData = new FormData();
      formData.append('document', file.file);
      formData.append('userType', userType);
      formData.append('documentType', 'auto-detect');

      // محاكاة تقدم الرفع
      const progressInterval = setInterval(() => {
        setUploadedFiles(prev => prev.map((f, i) => 
          i === fileIndex && f.progress < 90 
            ? { ...f, progress: f.progress + 10 } 
            : f
        ));
      }, 200);

      const response = await fetch('/api/ai-document-processing/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}` // يجب الحصول على التوكن من السياق
        },
        body: formData
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في رفع الملف');
      }

      const result = await response.json();
      
      // تحديث حالة الملف إلى "جاري المعالجة"
      setUploadedFiles(prev => prev.map((f, i) => 
        i === fileIndex 
          ? { 
              ...f, 
              status: 'processing', 
              progress: 100,
              processingId: result.data.processingId 
            } 
          : f
      ));

      onUploadComplete(result.data.processingId);

    } catch (error) {
      console.error('خطأ في رفع الملف:', error);
      
      setUploadedFiles(prev => prev.map((f, i) => 
        i === fileIndex 
          ? { 
              ...f, 
              status: 'error', 
              progress: 0,
              error: error instanceof Error ? error.message : 'خطأ غير معروف'
            } 
          : f
      ));

      onUploadError(error instanceof Error ? error.message : 'خطأ في رفع الملف');
    } finally {
      setIsUploading(false);
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => {
      const newFiles = [...prev];
      URL.revokeObjectURL(newFiles[index].preview);
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  const getStatusIcon = (status: UploadedFile['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'processing':
      case 'uploading':
        return <div className="h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: UploadedFile['status']) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'uploading':
        return 'جاري الرفع...';
      case 'processing':
        return 'جاري المعالجة...';
      case 'completed':
        return 'مكتمل';
      case 'error':
        return 'خطأ';
      default:
        return '';
    }
  };

  const getStatusColor = (status: UploadedFile['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'processing':
      case 'uploading':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* منطقة رفع الملفات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            رفع المستندات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
              }
              ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            
            {isDragActive ? (
              <p className="text-blue-600 font-medium">اسحب الملفات هنا...</p>
            ) : (
              <div>
                <p className="text-lg font-medium text-gray-700 mb-2">
                  اسحب الملفات هنا أو انقر للاختيار
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  الصيغ المدعومة: JPG, PNG, WebP, HEIC, PDF
                </p>
                <p className="text-xs text-gray-400">
                  الحد الأقصى: 10 ميجابايت لكل ملف
                </p>
              </div>
            )}
          </div>

          {/* معلومات إضافية */}
          <Alert className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              للحصول على أفضل النتائج، تأكد من أن المستندات واضحة ومقروءة، 
              وأن الإضاءة جيدة، وأن النص غير مقطوع.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* قائمة الملفات المرفوعة */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>الملفات المرفوعة ({uploadedFiles.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {uploadedFiles.map((file, index) => (
                <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                  {/* معاينة الملف */}
                  <div className="flex-shrink-0">
                    {file.file.type.startsWith('image/') ? (
                      <img 
                        src={file.preview} 
                        alt={file.file.name}
                        className="h-16 w-16 object-cover rounded"
                      />
                    ) : (
                      <div className="h-16 w-16 bg-gray-100 rounded flex items-center justify-center">
                        <FileText className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* معلومات الملف */}
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">
                      {file.file.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {(file.file.size / 1024 / 1024).toFixed(2)} ميجابايت
                    </p>
                    
                    {/* شريط التقدم */}
                    {(file.status === 'uploading' || file.status === 'processing') && (
                      <div className="mt-2">
                        <Progress value={file.progress} className="h-2" />
                        <p className="text-xs text-gray-500 mt-1">
                          {file.progress}%
                        </p>
                      </div>
                    )}

                    {/* رسالة الخطأ */}
                    {file.status === 'error' && file.error && (
                      <p className="text-sm text-red-600 mt-1">
                        {file.error}
                      </p>
                    )}
                  </div>

                  {/* الحالة والأزرار */}
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(file.status)}>
                      <span className="flex items-center gap-1">
                        {getStatusIcon(file.status)}
                        {getStatusText(file.status)}
                      </span>
                    </Badge>

                    {file.status === 'pending' && (
                      <Button
                        size="sm"
                        onClick={() => uploadFile(index)}
                        disabled={isUploading}
                      >
                        رفع
                      </Button>
                    )}

                    {file.status === 'error' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => uploadFile(index)}
                        disabled={isUploading}
                      >
                        إعادة المحاولة
                      </Button>
                    )}

                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeFile(index)}
                      disabled={isUploading}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
