// src/services/classificationService.ts - خدمة التصنيف واتخاذ القرار المتقدمة
import { ClassificationResult } from './huggingFaceAIService';

// نماذج التصنيف واتخاذ القرار
const CLASSIFICATION_MODELS = {
  documentClassifier: {
    primary: 'facebook/bart-large-mnli',
    secondary: 'microsoft/DialoGPT-large',
    fallback: 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2'
  },
  decisionMaker: {
    primary: 'microsoft/DialoGPT-large',
    secondary: 'facebook/bart-large-mnli',
    fallback: 'gpt2'
  }
};

// قواعد العمل للموافقة التلقائية
const BUSINESS_RULES = {
  merchant: {
    required_fields: ['businessName', 'ownerName', 'registrationNumber'],
    optional_fields: ['businessActivity', 'address', 'phone'],
    validation_rules: {
      registrationNumber: {
        pattern: /^\d{10}$/,
        description: 'رقم السجل التجاري يجب أن يكون 10 أرقام'
      },
      businessName: {
        minLength: 3,
        maxLength: 100,
        description: 'اسم المنشأة يجب أن يكون بين 3-100 حرف'
      }
    },
    risk_factors: {
      missing_required_field: 30,
      invalid_registration_number: 40,
      expired_document: 50,
      suspicious_activity: 60,
      duplicate_registration: 70
    },
    approval_thresholds: {
      auto_approve: 20,
      manual_review: 50,
      auto_reject: 80
    }
  },
  representative: {
    required_fields: ['ownerName', 'nationalId', 'drivingLicense'],
    optional_fields: ['address', 'phone', 'vehicleInfo'],
    validation_rules: {
      nationalId: {
        pattern: /^\d{10}$/,
        description: 'رقم الهوية الوطنية يجب أن يكون 10 أرقام'
      },
      drivingLicense: {
        pattern: /^\d+$/,
        description: 'رقم رخصة القيادة يجب أن يكون أرقام فقط'
      }
    },
    risk_factors: {
      missing_required_field: 25,
      invalid_national_id: 35,
      expired_license: 45,
      traffic_violations: 55,
      age_restriction: 40
    },
    approval_thresholds: {
      auto_approve: 15,
      manual_review: 40,
      auto_reject: 70
    }
  }
};

// أنواع المستندات المدعومة
const DOCUMENT_TYPES = {
  'commercial_registration': {
    name: 'السجل التجاري',
    category: 'merchant',
    required_for: ['merchant'],
    validation_priority: 'high'
  },
  'freelance_document': {
    name: 'وثيقة العمل الحر',
    category: 'merchant',
    required_for: ['merchant'],
    validation_priority: 'high'
  },
  'national_id': {
    name: 'الهوية الوطنية',
    category: 'personal',
    required_for: ['merchant', 'representative'],
    validation_priority: 'high'
  },
  'driving_license': {
    name: 'رخصة القيادة',
    category: 'personal',
    required_for: ['representative'],
    validation_priority: 'high'
  },
  'vehicle_inspection': {
    name: 'شهادة الفحص الدوري',
    category: 'vehicle',
    required_for: ['representative'],
    validation_priority: 'medium'
  }
};

export class ClassificationService {
  private static instance: ClassificationService;
  private apiKey: string;
  private baseUrl: string;

  private constructor() {
    this.apiKey = process.env.HUGGING_FACE_API_KEY || '';
    this.baseUrl = 'https://api-inference.huggingface.co/models';
  }

  static getInstance(): ClassificationService {
    if (!ClassificationService.instance) {
      ClassificationService.instance = new ClassificationService();
    }
    return ClassificationService.instance;
  }

  /**
   * تصنيف المستند واتخاذ قرار الموافقة
   */
  async classifyAndDecide(
    extractedData: Record<string, any>,
    originalText: string,
    userType: 'merchant' | 'representative',
    documentType?: string
  ): Promise<ClassificationResult> {
    const startTime = Date.now();
    
    try {
      // تصنيف نوع المستند إذا لم يكن محدداً
      const finalDocumentType = documentType || await this.classifyDocumentType(originalText);
      
      // التحقق من صحة نوع المستند للمستخدم
      this.validateDocumentForUserType(finalDocumentType, userType);
      
      // حساب نقاط المخاطر
      const riskScore = this.calculateRiskScore(extractedData, userType, finalDocumentType);
      
      // اتخاذ قرار الموافقة
      const decision = this.makeApprovalDecision(riskScore, userType, extractedData);
      
      // توليد أسباب القرار
      const reasons = this.generateDecisionReasons(decision, extractedData, riskScore, userType);
      
      // حساب الثقة في القرار
      const confidence = this.calculateDecisionConfidence(extractedData, riskScore, decision);
      
      return {
        documentType: finalDocumentType,
        decision,
        confidence,
        reasons,
        riskScore,
        processingTime: Date.now() - startTime,
        modelUsed: CLASSIFICATION_MODELS.decisionMaker.primary
      };

    } catch (error) {
      console.error('❌ خطأ في التصنيف واتخاذ القرار:', error);
      throw new Error(`فشل في تصنيف المستند: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  }

  /**
   * تصنيف نوع المستند باستخدام AI
   */
  private async classifyDocumentType(text: string): Promise<string> {
    try {
      // استخدام نموذج التصنيف
      const response = await fetch(`${this.baseUrl}/${CLASSIFICATION_MODELS.documentClassifier.primary}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: text,
          parameters: {
            candidate_labels: Object.keys(DOCUMENT_TYPES)
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.labels && result.labels.length > 0) {
          return result.labels[0];
        }
      }
    } catch (error) {
      console.warn('⚠️ فشل في تصنيف المستند بالـ AI، استخدام الطريقة البديلة');
    }

    // طريقة بديلة باستخدام الكلمات المفتاحية
    return this.classifyDocumentByKeywords(text);
  }

  /**
   * تصنيف المستند باستخدام الكلمات المفتاحية
   */
  private classifyDocumentByKeywords(text: string): string {
    const lowerText = text.toLowerCase();
    
    const keywords = {
      'commercial_registration': ['سجل تجاري', 'تجاري', 'commercial registration', 'منشأة', 'شركة'],
      'freelance_document': ['وثيقة العمل الحر', 'عمل حر', 'freelance', 'مستقل'],
      'national_id': ['هوية وطنية', 'الهوية', 'national id', 'identity'],
      'driving_license': ['رخصة القيادة', 'رخصة قيادة', 'driving license', 'قيادة'],
      'vehicle_inspection': ['فحص دوري', 'شهادة فحص', 'vehicle inspection', 'فحص المركبة']
    };

    let bestMatch = 'unknown';
    let maxScore = 0;

    for (const [docType, keywordList] of Object.entries(keywords)) {
      let score = 0;
      for (const keyword of keywordList) {
        if (lowerText.includes(keyword)) {
          score += keyword.length; // كلمات أطول تحصل على نقاط أكثر
        }
      }
      
      if (score > maxScore) {
        maxScore = score;
        bestMatch = docType;
      }
    }

    return bestMatch;
  }

  /**
   * التحقق من صحة نوع المستند للمستخدم
   */
  private validateDocumentForUserType(documentType: string, userType: 'merchant' | 'representative') {
    const docInfo = DOCUMENT_TYPES[documentType as keyof typeof DOCUMENT_TYPES];
    
    if (!docInfo) {
      throw new Error(`نوع مستند غير مدعوم: ${documentType}`);
    }

    if (!docInfo.required_for.includes(userType)) {
      throw new Error(`هذا النوع من المستندات غير مطلوب لـ ${userType}`);
    }
  }

  /**
   * حساب نقاط المخاطر
   */
  private calculateRiskScore(
    extractedData: Record<string, any>,
    userType: 'merchant' | 'representative',
    documentType: string
  ): number {
    let riskScore = 0;
    const rules = BUSINESS_RULES[userType];
    
    // فحص الحقول المطلوبة
    for (const field of rules.required_fields) {
      if (!extractedData[field] || !extractedData[field].toString().trim()) {
        riskScore += rules.risk_factors.missing_required_field;
        console.log(`⚠️ حقل مطلوب مفقود: ${field}`);
      }
    }

    // فحص قواعد التحقق
    for (const [field, rule] of Object.entries(rules.validation_rules)) {
      const value = extractedData[field];
      if (value) {
        if (rule.pattern && !rule.pattern.test(value.toString())) {
          riskScore += rules.risk_factors.invalid_registration_number || 30;
          console.log(`⚠️ قيمة غير صالحة للحقل ${field}: ${value}`);
        }
        if (rule.minLength && value.toString().length < rule.minLength) {
          riskScore += 20;
        }
        if (rule.maxLength && value.toString().length > rule.maxLength) {
          riskScore += 15;
        }
      }
    }

    // فحوصات إضافية خاصة بنوع المستند
    riskScore += this.performDocumentSpecificChecks(extractedData, documentType, userType);

    // فحص التكرار (محاكاة)
    if (this.checkForDuplicates(extractedData, userType)) {
      riskScore += rules.risk_factors.duplicate_registration || 70;
    }

    return Math.min(riskScore, 100);
  }

  /**
   * فحوصات خاصة بنوع المستند
   */
  private performDocumentSpecificChecks(
    extractedData: Record<string, any>,
    documentType: string,
    userType: string
  ): number {
    let additionalRisk = 0;

    switch (documentType) {
      case 'commercial_registration':
        // فحص صحة رقم السجل التجاري
        if (extractedData.registrationNumber) {
          const regNum = extractedData.registrationNumber.toString();
          if (regNum.length !== 10 || !/^\d+$/.test(regNum)) {
            additionalRisk += 40;
          }
        }
        break;

      case 'national_id':
        // فحص صحة رقم الهوية الوطنية
        if (extractedData.nationalId) {
          if (!this.validateSaudiNationalId(extractedData.nationalId.toString())) {
            additionalRisk += 35;
          }
        }
        break;

      case 'driving_license':
        // فحص تاريخ انتهاء الرخصة
        if (extractedData.expiryDate) {
          const expiryDate = new Date(extractedData.expiryDate);
          const now = new Date();
          if (expiryDate < now) {
            additionalRisk += 45;
          }
        }
        break;
    }

    return additionalRisk;
  }

  /**
   * التحقق من صحة رقم الهوية الوطنية السعودية
   */
  private validateSaudiNationalId(id: string): boolean {
    if (!/^\d{10}$/.test(id)) return false;
    
    const digits = id.split('').map(Number);
    let sum = 0;
    
    for (let i = 0; i < 9; i++) {
      if (i % 2 === 0) {
        const doubled = digits[i] * 2;
        sum += doubled > 9 ? doubled - 9 : doubled;
      } else {
        sum += digits[i];
      }
    }
    
    const checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit === digits[9];
  }

  /**
   * فحص التكرار (محاكاة)
   */
  private checkForDuplicates(extractedData: Record<string, any>, userType: string): boolean {
    // في التطبيق الحقيقي، سيتم فحص قاعدة البيانات
    // هنا نحاكي فحص بسيط
    return false;
  }

  /**
   * اتخاذ قرار الموافقة
   */
  private makeApprovalDecision(
    riskScore: number,
    userType: 'merchant' | 'representative',
    extractedData: Record<string, any>
  ): 'approve' | 'reject' | 'manual_review' {
    const thresholds = BUSINESS_RULES[userType].approval_thresholds;
    
    if (riskScore <= thresholds.auto_approve) {
      return 'approve';
    } else if (riskScore >= thresholds.auto_reject) {
      return 'reject';
    } else {
      return 'manual_review';
    }
  }

  /**
   * توليد أسباب القرار
   */
  private generateDecisionReasons(
    decision: string,
    extractedData: Record<string, any>,
    riskScore: number,
    userType: string
  ): string[] {
    const reasons: string[] = [];
    const rules = BUSINESS_RULES[userType];
    
    switch (decision) {
      case 'approve':
        reasons.push('جميع البيانات المطلوبة متوفرة وصحيحة');
        reasons.push('المستند يلبي معايير الجودة المطلوبة');
        reasons.push(`نقاط المخاطر منخفضة (${riskScore})`);
        break;
        
      case 'reject':
        if (riskScore >= 70) {
          reasons.push('نقاط المخاطر عالية جداً');
        }
        
        // فحص الحقول المفقودة
        for (const field of rules.required_fields) {
          if (!extractedData[field]) {
            reasons.push(`حقل مطلوب مفقود: ${this.getFieldDisplayName(field)}`);
          }
        }
        break;
        
      case 'manual_review':
        reasons.push('يتطلب مراجعة يدوية من فريق الموافقات');
        reasons.push(`نقاط المخاطر متوسطة (${riskScore})`);
        
        if (riskScore > 30) {
          reasons.push('توجد بعض المخاوف التي تحتاج تأكيد');
        }
        break;
    }
    
    return reasons;
  }

  /**
   * الحصول على اسم الحقل للعرض
   */
  private getFieldDisplayName(field: string): string {
    const displayNames: Record<string, string> = {
      'businessName': 'اسم المنشأة',
      'ownerName': 'اسم المالك',
      'registrationNumber': 'رقم السجل التجاري',
      'nationalId': 'رقم الهوية الوطنية',
      'drivingLicense': 'رقم رخصة القيادة',
      'businessActivity': 'النشاط التجاري',
      'address': 'العنوان',
      'phone': 'رقم الهاتف'
    };
    
    return displayNames[field] || field;
  }

  /**
   * حساب الثقة في القرار
   */
  private calculateDecisionConfidence(
    extractedData: Record<string, any>,
    riskScore: number,
    decision: string
  ): number {
    let confidence = 0.7; // قيمة أساسية
    
    // زيادة الثقة بناءً على عدد الحقول المملوءة
    const filledFields = Object.values(extractedData).filter(v => v && v.toString().trim()).length;
    confidence += Math.min(filledFields * 0.03, 0.2);
    
    // تعديل الثقة بناءً على نقاط المخاطر
    if (riskScore <= 20) {
      confidence += 0.1; // ثقة عالية للمخاطر المنخفضة
    } else if (riskScore >= 70) {
      confidence += 0.05; // ثقة متوسطة للمخاطر العالية (قرار واضح)
    }
    
    // تعديل الثقة بناءً على نوع القرار
    if (decision === 'manual_review') {
      confidence -= 0.1; // ثقة أقل للحالات المشكوك فيها
    }
    
    return Math.min(Math.max(confidence, 0.5), 0.95);
  }
}
