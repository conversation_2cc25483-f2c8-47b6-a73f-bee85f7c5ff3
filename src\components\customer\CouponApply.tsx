'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Tag, 
  Check, 
  X, 
  Loader2, 
  Gift, 
  Percent, 
  DollarSign, 
  Truck,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { couponService } from '@/services/couponService';
import { CouponValidation, CartData, DiscountResult } from '@/types/coupon';
import { toast } from 'sonner';
import { useLocale } from '@/context/locale-context';

interface CouponApplyProps {
  cartData: CartData;
  appliedCoupon?: {
    code: string;
    discount: number;
    freeShipping: boolean;
  };
  onCouponApplied: (result: DiscountResult) => void;
  onCouponRemoved: () => void;
  disabled?: boolean;
}

export function CouponApply({ 
  cartData, 
  appliedCoupon, 
  onCouponApplied, 
  onCouponRemoved,
  disabled = false 
}: CouponApplyProps) {
  const { t } = useLocale();
  const [couponCode, setCouponCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<CouponValidation | null>(null);
  const [showValidation, setShowValidation] = useState(false);

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error('يرجى إدخال كود الكوبون');
      return;
    }

    setIsValidating(true);
    setShowValidation(false);

    try {
      const validation = await couponService.validateCoupon(couponCode.trim().toUpperCase(), cartData);
      setValidationResult(validation);
      setShowValidation(true);

      if (validation.isValid && validation.coupon) {
        const discountResult: DiscountResult = {
          originalAmount: cartData.totalAmount,
          discountAmount: validation.discount,
          finalAmount: cartData.totalAmount - validation.discount,
          couponCode: validation.coupon.code,
          freeShipping: validation.freeShipping || false,
          appliedAt: new Date() as any
        };

        onCouponApplied(discountResult);
        toast.success('تم تطبيق الكوبون بنجاح!');
        setCouponCode('');
        setShowValidation(false);
      } else {
        toast.error(validation.message);
      }
    } catch (error) {
      console.error('Error applying coupon:', error);
      toast.error('حدث خطأ أثناء تطبيق الكوبون');
    } finally {
      setIsValidating(false);
    }
  };

  const handleRemoveCoupon = () => {
    onCouponRemoved();
    setValidationResult(null);
    setShowValidation(false);
    toast.success('تم إزالة الكوبون');
  };

  const formatDiscount = (discount: number, type?: string) => {
    if (type === 'free_shipping') {
      return 'شحن مجاني';
    }
    return `${discount.toFixed(2)} ريال`;
  };

  const getCouponIcon = (type?: string) => {
    switch (type) {
      case 'percentage':
        return <Percent className="h-4 w-4" />;
      case 'fixed':
        return <DollarSign className="h-4 w-4" />;
      case 'free_shipping':
        return <Truck className="h-4 w-4" />;
      default:
        return <Gift className="h-4 w-4" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Tag className="h-5 w-5" />
          كوبون الخصم
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* الكوبون المطبق حالياً */}
        {appliedCoupon ? (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-full">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <div className="font-medium text-green-800">
                    كوبون مطبق: {appliedCoupon.code}
                  </div>
                  <div className="text-sm text-green-600">
                    خصم: {formatDiscount(appliedCoupon.discount)}
                    {appliedCoupon.freeShipping && ' + شحن مجاني'}
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRemoveCoupon}
                disabled={disabled}
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                <X className="h-4 w-4 mr-1" />
                إزالة
              </Button>
            </div>
          </div>
        ) : (
          /* نموذج إدخال الكوبون */
          <div className="space-y-3">
            <div className="flex gap-2">
              <Input
                placeholder="أدخل كود الكوبون"
                value={couponCode}
                onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                disabled={disabled || isValidating}
                className="flex-1"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleApplyCoupon();
                  }
                }}
              />
              <Button
                onClick={handleApplyCoupon}
                disabled={disabled || isValidating || !couponCode.trim()}
                className="px-6"
              >
                {isValidating ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  'تطبيق'
                )}
              </Button>
            </div>

            {/* نتيجة التحقق */}
            {showValidation && validationResult && (
              <Alert variant={validationResult.isValid ? "default" : "destructive"}>
                <div className="flex items-center gap-2">
                  {validationResult.isValid ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  <AlertDescription>
                    {validationResult.message}
                    {validationResult.isValid && validationResult.discount > 0 && (
                      <span className="font-medium">
                        {' '}(خصم: {formatDiscount(validationResult.discount, validationResult.coupon?.type)})
                      </span>
                    )}
                  </AlertDescription>
                </div>
              </Alert>
            )}
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="text-sm text-gray-500 space-y-1">
          <div className="flex items-center gap-2">
            <Gift className="h-4 w-4" />
            <span>يمكنك استخدام كوبون واحد فقط لكل طلب</span>
          </div>
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            <span>تحقق من شروط الكوبون قبل التطبيق</span>
          </div>
        </div>

        {/* كوبونات مقترحة (يمكن إضافتها لاحقاً) */}
        <div className="pt-2 border-t">
          <div className="text-sm font-medium text-gray-700 mb-2">
            كوبونات متاحة:
          </div>
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className="cursor-pointer hover:bg-gray-50">
              WELCOME10 - خصم 10%
            </Badge>
            <Badge variant="outline" className="cursor-pointer hover:bg-gray-50">
              FREESHIP - شحن مجاني
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// مكون مبسط لعرض ملخص الخصم في سلة التسوق
interface CouponSummaryProps {
  appliedCoupon?: {
    code: string;
    discount: number;
    freeShipping: boolean;
  };
  originalTotal: number;
}

export function CouponSummary({ appliedCoupon, originalTotal }: CouponSummaryProps) {
  if (!appliedCoupon) return null;

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center text-sm">
        <span className="text-gray-600">المجموع الفرعي:</span>
        <span>{originalTotal.toFixed(2)} ريال</span>
      </div>
      
      <div className="flex justify-between items-center text-sm text-green-600">
        <span className="flex items-center gap-1">
          <Tag className="h-3 w-3" />
          خصم ({appliedCoupon.code}):
        </span>
        <span>-{appliedCoupon.discount.toFixed(2)} ريال</span>
      </div>

      {appliedCoupon.freeShipping && (
        <div className="flex justify-between items-center text-sm text-green-600">
          <span className="flex items-center gap-1">
            <Truck className="h-3 w-3" />
            شحن مجاني:
          </span>
          <span>مجاني</span>
        </div>
      )}

      <div className="flex justify-between items-center font-medium text-lg border-t pt-2">
        <span>المجموع النهائي:</span>
        <span>{(originalTotal - appliedCoupon.discount).toFixed(2)} ريال</span>
      </div>
    </div>
  );
}
