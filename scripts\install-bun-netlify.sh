#!/bin/bash
# سكريبت تثبيت Bun في Netlify
# 🚀 يثبت Bun ويستخدمه بدلاً من npm

set -e

echo "🚀 Installing Bun for Netlify..."

# تحقق من وجود Bun
if command -v bun &> /dev/null; then
    echo "✅ Bun already installed: $(bun --version)"
    exit 0
fi

# تثبيت Bun
echo "📦 Installing Bun..."
curl -fsSL https://bun.sh/install | bash

# إضافة Bun إلى PATH
export PATH="$HOME/.bun/bin:$PATH"

# تحقق من التثبيت
if command -v bun &> /dev/null; then
    echo "✅ Bun installed successfully: $(bun --version)"
else
    echo "❌ Failed to install Bun"
    exit 1
fi

# تثبيت التبعيات باستخدام Bun
echo "📦 Installing dependencies with Bun..."
bun install

echo "🎉 Bun setup complete!"
