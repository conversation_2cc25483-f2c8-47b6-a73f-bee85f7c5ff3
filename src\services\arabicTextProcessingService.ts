/**
 * خدمة معالجة النصوص العربية المتقدمة - المرحلة الأولى
 * تطبيع وتحسين معالجة النصوص العربية لرفع دقة التطابق
 */

export interface TextProcessingResult {
  original: string;
  normalized: string;
  cleaned: string;
  tokens: string[];
  abbreviations: string[];
  corrections: { [key: string]: string };
}

export class ArabicTextProcessingService {
  
  // قاموس الاختصارات الشائعة
  private static readonly COMMON_ABBREVIATIONS: { [key: string]: string } = {
    'شركة': 'ش',
    'مؤسسة': 'م',
    'متجر': 'مت',
    'محل': 'مح',
    'مكتب': 'مك',
    'مصنع': 'مص',
    'معرض': 'مع',
    'مركز': 'مر',
    'جمعية': 'ج',
    'مجموعة': 'مج',
    'شراكة': 'شر',
    'تجارية': 'تج',
    'صناعية': 'صن',
    'خدمية': 'خد',
    'استثمارية': 'است',
    'تطويرية': 'تط',
    'هندسية': 'هن',
    'طبية': 'طب',
    'تقنية': 'تق',
    'رقمية': 'رق',
    'إلكترونية': 'إلك',
    'المحدودة': 'المح',
    'ذات المسؤولية المحدودة': 'ذ.م.م',
    'المساهمة': 'المس',
    'التضامنية': 'التض',
    'التوصية البسيطة': 'ت.ب',
    'المهنية': 'المه'
  };
  
  // قاموس تصحيح الأخطاء الشائعة
  private static readonly COMMON_CORRECTIONS: { [key: string]: string } = {
    'محمد': ['محمد', 'محمود', 'أحمد', 'حمد'],
    'عبدالله': ['عبدالله', 'عبد الله', 'عبداللة'],
    'عبدالرحمن': ['عبدالرحمن', 'عبد الرحمن', 'عبدالرحمان'],
    'عبدالعزيز': ['عبدالعزيز', 'عبد العزيز', 'عبدالعزيز'],
    'إبراهيم': ['إبراهيم', 'ابراهيم', 'براهيم'],
    'إسماعيل': ['إسماعيل', 'اسماعيل', 'سماعيل'],
    'الرياض': ['الرياض', 'رياض', 'الرياظ'],
    'جدة': ['جدة', 'جده', 'جدا'],
    'الدمام': ['الدمام', 'دمام', 'الدمان'],
    'مكة': ['مكة', 'مكه', 'مكا'],
    'المدينة': ['المدينة', 'مدينة', 'المدينه'],
    'تبوك': ['تبوك', 'تبوق', 'طبوك'],
    'أبها': ['أبها', 'ابها', 'أبهى'],
    'الطائف': ['الطائف', 'طائف', 'الطايف'],
    'بريدة': ['بريدة', 'بريده', 'بريدا'],
    'خميس مشيط': ['خميس مشيط', 'خميس مشيط', 'خميس مشيت']
  };
  
  /**
   * معالجة شاملة للنص العربي
   */
  static processArabicText(text: string): TextProcessingResult {
    if (!text) {
      return {
        original: '',
        normalized: '',
        cleaned: '',
        tokens: [],
        abbreviations: [],
        corrections: {}
      };
    }
    
    const original = text;
    
    // 1. التطبيع الأساسي
    let normalized = this.normalizeBasic(text);
    
    // 2. تصحيح الأخطاء الشائعة
    const corrections = this.correctCommonErrors(normalized);
    normalized = corrections.corrected;
    
    // 3. معالجة الاختصارات
    const abbreviationResult = this.expandAbbreviations(normalized);
    normalized = abbreviationResult.expanded;
    
    // 4. التنظيف النهائي
    const cleaned = this.finalCleanup(normalized);
    
    // 5. التقسيم إلى رموز
    const tokens = this.tokenize(cleaned);
    
    return {
      original,
      normalized,
      cleaned,
      tokens,
      abbreviations: abbreviationResult.found,
      corrections: corrections.corrections
    };
  }
  
  /**
   * التطبيع الأساسي للنص العربي
   */
  private static normalizeBasic(text: string): string {
    return text
      // إزالة التشكيل
      .replace(/[\u064B-\u0652\u0670\u0640]/g, '')
      // توحيد الألف
      .replace(/[آأإ]/g, 'ا')
      // توحيد التاء المربوطة والهاء
      .replace(/[ة]/g, 'ه')
      // توحيد الياء
      .replace(/[ي]/g, 'ى')
      // توحيد الواو
      .replace(/[ؤ]/g, 'و')
      // إزالة الأرقام الإنجليزية وتحويلها للعربية
      .replace(/[0-9]/g, (match) => {
        const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return arabicNumerals[parseInt(match)];
      })
      // إزالة الرموز الخاصة غير المرغوبة
      .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\d]/g, ' ')
      // توحيد المسافات
      .replace(/\s+/g, ' ')
      .trim()
      .toLowerCase();
  }
  
  /**
   * تصحيح الأخطاء الشائعة
   */
  private static correctCommonErrors(text: string): { corrected: string; corrections: { [key: string]: string } } {
    let corrected = text;
    const corrections: { [key: string]: string } = {};
    
    // البحث عن الأخطاء وتصحيحها
    for (const [correct, variants] of Object.entries(this.COMMON_CORRECTIONS)) {
      for (const variant of variants) {
        if (variant !== correct && corrected.includes(variant)) {
          corrected = corrected.replace(new RegExp(variant, 'gi'), correct);
          corrections[variant] = correct;
        }
      }
    }
    
    // تصحيح أخطاء إضافية شائعة
    const additionalCorrections = [
      { wrong: /ال([بتثجحخدذرزسشصضطظعغفقكلمنهوى])\s+/g, correct: 'ال$1' },
      { wrong: /\s+ال([بتثجحخدذرزسشصضطظعغفقكلمنهوى])/g, correct: ' ال$1' },
      { wrong: /([اوى])\s+([اوى])/g, correct: '$1$2' },
      { wrong: /\s*-\s*/g, correct: '-' },
      { wrong: /\s*\.\s*/g, correct: '.' }
    ];
    
    for (const correction of additionalCorrections) {
      corrected = corrected.replace(correction.wrong, correction.correct);
    }
    
    return { corrected, corrections };
  }
  
  /**
   * توسيع الاختصارات
   */
  private static expandAbbreviations(text: string): { expanded: string; found: string[] } {
    let expanded = text;
    const found: string[] = [];
    
    // البحث عن الاختصارات وتوسيعها
    for (const [full, abbrev] of Object.entries(this.COMMON_ABBREVIATIONS)) {
      const abbrevRegex = new RegExp(`\\b${abbrev}\\b`, 'gi');
      if (abbrevRegex.test(expanded)) {
        expanded = expanded.replace(abbrevRegex, full);
        found.push(abbrev);
      }
    }
    
    // معالجة اختصارات خاصة
    const specialAbbreviations = [
      { pattern: /\bذ\.م\.م\b/gi, replacement: 'ذات المسؤولية المحدودة' },
      { pattern: /\bش\.م\.م\b/gi, replacement: 'شركة مساهمة مقفلة' },
      { pattern: /\bش\.م\.ع\b/gi, replacement: 'شركة مساهمة عامة' },
      { pattern: /\bت\.ب\b/gi, replacement: 'التوصية البسيطة' },
      { pattern: /\bم\.ت\b/gi, replacement: 'مؤسسة تجارية' }
    ];
    
    for (const abbrev of specialAbbreviations) {
      if (abbrev.pattern.test(expanded)) {
        expanded = expanded.replace(abbrev.pattern, abbrev.replacement);
        found.push(abbrev.pattern.source);
      }
    }
    
    return { expanded, found };
  }
  
  /**
   * التنظيف النهائي
   */
  private static finalCleanup(text: string): string {
    return text
      // إزالة المسافات الزائدة
      .replace(/\s+/g, ' ')
      // إزالة المسافات في بداية ونهاية النص
      .trim()
      // إزالة النقاط والفواصل الزائدة
      .replace(/[.,;:]+$/, '')
      // توحيد الأقواس
      .replace(/[()[\]{}]/g, '')
      // إزالة الأرقام المنفردة
      .replace(/\b\d+\b/g, '')
      // إزالة الكلمات القصيرة جداً (حرف واحد)
      .replace(/\b\w\b/g, '')
      // تنظيف المسافات مرة أخيرة
      .replace(/\s+/g, ' ')
      .trim();
  }
  
  /**
   * تقسيم النص إلى رموز
   */
  private static tokenize(text: string): string[] {
    if (!text) return [];
    
    return text
      .split(/\s+/)
      .filter(token => token.length > 1)
      .map(token => token.trim())
      .filter(token => token.length > 0);
  }
  
  /**
   * مقارنة نصين مع معالجة متقدمة
   */
  static compareTexts(text1: string, text2: string): {
    similarity: number;
    confidence: number;
    details: {
      originalSimilarity: number;
      normalizedSimilarity: number;
      tokenSimilarity: number;
      semanticSimilarity: number;
    };
  } {
    // معالجة النصوص
    const processed1 = this.processArabicText(text1);
    const processed2 = this.processArabicText(text2);
    
    // حساب التشابه على مستويات مختلفة
    const originalSimilarity = this.calculateBasicSimilarity(text1, text2);
    const normalizedSimilarity = this.calculateBasicSimilarity(processed1.normalized, processed2.normalized);
    const tokenSimilarity = this.calculateTokenSimilarity(processed1.tokens, processed2.tokens);
    const semanticSimilarity = this.calculateSemanticSimilarity(processed1.tokens, processed2.tokens);
    
    // حساب التشابه الإجمالي بالأوزان
    const similarity = (
      originalSimilarity * 0.2 +
      normalizedSimilarity * 0.3 +
      tokenSimilarity * 0.3 +
      semanticSimilarity * 0.2
    );
    
    const confidence = Math.min(similarity * 1.1, 100);
    
    return {
      similarity,
      confidence,
      details: {
        originalSimilarity,
        normalizedSimilarity,
        tokenSimilarity,
        semanticSimilarity
      }
    };
  }
  
  /**
   * حساب التشابه الأساسي
   */
  private static calculateBasicSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0;
    
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 100;
    
    const editDistance = this.calculateEditDistance(longer, shorter);
    return ((longer.length - editDistance) / longer.length) * 100;
  }
  
  /**
   * حساب المسافة التحريرية
   */
  private static calculateEditDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }
  
  /**
   * حساب تشابه الرموز
   */
  private static calculateTokenSimilarity(tokens1: string[], tokens2: string[]): number {
    if (tokens1.length === 0 && tokens2.length === 0) return 100;
    if (tokens1.length === 0 || tokens2.length === 0) return 0;
    
    const set1 = new Set(tokens1);
    const set2 = new Set(tokens2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return (intersection.size / union.size) * 100;
  }
  
  /**
   * حساب التشابه الدلالي
   */
  private static calculateSemanticSimilarity(tokens1: string[], tokens2: string[]): number {
    // تطبيق خوارزمية بسيطة للتشابه الدلالي
    let semanticMatches = 0;
    let totalComparisons = 0;
    
    for (const token1 of tokens1) {
      for (const token2 of tokens2) {
        totalComparisons++;
        
        // فحص التشابه الجذري
        if (this.haveSameRoot(token1, token2)) {
          semanticMatches++;
        }
      }
    }
    
    return totalComparisons > 0 ? (semanticMatches / totalComparisons) * 100 : 0;
  }
  
  /**
   * فحص التشابه الجذري للكلمات العربية
   */
  private static haveSameRoot(word1: string, word2: string): boolean {
    if (word1.length < 3 || word2.length < 3) return false;
    
    // استخراج الجذر المحتمل (الأحرف الثلاثة الأولى)
    const root1 = word1.substring(0, 3);
    const root2 = word2.substring(0, 3);
    
    // فحص التشابه في الجذر
    let matches = 0;
    for (let i = 0; i < 3; i++) {
      if (root1[i] === root2[i]) matches++;
    }
    
    return matches >= 2; // على الأقل حرفان متطابقان
  }
}
