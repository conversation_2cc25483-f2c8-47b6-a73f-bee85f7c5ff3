import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc,
  getDoc,
  getDocs,
  query, 
  where, 
  orderBy,
  limit,
  startAfter,
  increment,
  serverTimestamp,
  Timestamp,
  writeBatch,
  runTransaction
} from 'firebase/firestore';
import { 
  LoyaltyProgram,
  CustomerLoyalty,
  PointTransaction,
  RewardRedemption,
  LoyaltyAnalytics,
  CreateLoyaltyProgramData,
  UpdateLoyaltyProgramData,
  LoyaltyMemberFilters,
  LoyaltyMemberSortOptions,
  LoyaltyMembersResponse,
  LoyaltyTier,
  PointTransactionType,
  LoyaltyTierConfig
} from '@/types/loyalty';

export class LoyaltyService {
  private programsCollection = collection(db, 'loyalty_programs');
  private membersCollection = collection(db, 'loyalty_members');
  private transactionsCollection = collection(db, 'loyalty_transactions');
  private redemptionsCollection = collection(db, 'loyalty_redemptions');

  /**
   * إنشاء برنامج ولاء جديد
   */
  async createLoyaltyProgram(merchantId: string, programData: CreateLoyaltyProgramData): Promise<string> {
    try {
      const newProgram: Omit<LoyaltyProgram, 'id'> = {
        ...programData,
        merchantId,
        isActive: true,
        status: 'active',
        tiers: await this.getLoyaltyTiers(),
        rewards: programData.rewards.map(reward => ({
          ...reward,
          id: this.generateId(),
          usedQuantity: 0,
          createdAt: serverTimestamp() as Timestamp,
          updatedAt: serverTimestamp() as Timestamp,
        })),
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
        createdBy: merchantId,
      };

      const docRef = await addDoc(this.programsCollection, newProgram);
      return docRef.id;
    } catch (error) {
      console.error('Error creating loyalty program:', error);
      throw new Error('فشل في إنشاء برنامج الولاء');
    }
  }

  /**
   * الحصول على برنامج الولاء للتاجر
   */
  async getMerchantLoyaltyProgram(merchantId: string): Promise<LoyaltyProgram | null> {
    try {
      const q = query(this.programsCollection, where('merchantId', '==', merchantId));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return { id: doc.id, ...doc.data() } as LoyaltyProgram;
    } catch (error) {
      console.error('Error getting loyalty program:', error);
      return null;
    }
  }

  /**
   * تحديث برنامج الولاء
   */
  async updateLoyaltyProgram(programId: string, updateData: UpdateLoyaltyProgramData): Promise<void> {
    try {
      const programRef = doc(this.programsCollection, programId);
      
      const updatePayload: any = {
        ...updateData,
        updatedAt: serverTimestamp(),
      };

      await updateDoc(programRef, updatePayload);
    } catch (error) {
      console.error('Error updating loyalty program:', error);
      throw new Error('فشل في تحديث برنامج الولاء');
    }
  }

  /**
   * انضمام عميل لبرنامج الولاء
   */
  async joinLoyaltyProgram(customerId: string, merchantId: string): Promise<void> {
    try {
      const program = await this.getMerchantLoyaltyProgram(merchantId);
      if (!program || !program.isActive) {
        throw new Error('برنامج الولاء غير متاح');
      }

      // التحقق من عدم انضمام العميل مسبقاً
      const existingMember = await this.getCustomerLoyalty(customerId, merchantId);
      if (existingMember) {
        throw new Error('العميل منضم بالفعل لبرنامج الولاء');
      }

      const newMember: Omit<CustomerLoyalty, 'id'> = {
        customerId,
        merchantId,
        programId: program.id,
        currentTier: 'bronze',
        totalPoints: program.settings.welcomeBonusPoints,
        availablePoints: program.settings.welcomeBonusPoints,
        lifetimeSpent: 0,
        lifetimeOrders: 0,
        joinedAt: serverTimestamp() as Timestamp,
        lastActivityAt: serverTimestamp() as Timestamp,
        tierAchievedAt: serverTimestamp() as Timestamp,
        nextTierProgress: this.calculateNextTierProgress('bronze', 0, program.tiers),
        statistics: {
          totalPointsEarned: program.settings.welcomeBonusPoints,
          totalPointsRedeemed: 0,
          totalPointsExpired: 0,
          averageOrderValue: 0,
          favoriteCategories: [],
        },
      };

      const batch = writeBatch(db);

      // إضافة العضو
      const memberRef = doc(this.membersCollection);
      batch.set(memberRef, newMember);

      // إضافة معاملة نقاط الترحيب
      if (program.settings.welcomeBonusPoints > 0) {
        const welcomeTransaction: Omit<PointTransaction, 'id'> = {
          customerId,
          merchantId,
          programId: program.id,
          type: 'bonus',
          points: program.settings.welcomeBonusPoints,
          description: 'نقاط ترحيب للانضمام لبرنامج الولاء',
          createdAt: serverTimestamp() as Timestamp,
          metadata: {
            bonusReason: 'welcome_bonus',
            tierAtTime: 'bronze',
          },
        };

        const transactionRef = doc(this.transactionsCollection);
        batch.set(transactionRef, welcomeTransaction);
      }

      await batch.commit();
    } catch (error) {
      console.error('Error joining loyalty program:', error);
      throw new Error('فشل في الانضمام لبرنامج الولاء');
    }
  }

  /**
   * الحصول على بيانات ولاء العميل
   */
  async getCustomerLoyalty(customerId: string, merchantId: string): Promise<CustomerLoyalty | null> {
    try {
      const q = query(
        this.membersCollection, 
        where('customerId', '==', customerId),
        where('merchantId', '==', merchantId)
      );
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return { id: doc.id, ...doc.data() } as CustomerLoyalty;
    } catch (error) {
      console.error('Error getting customer loyalty:', error);
      return null;
    }
  }

  /**
   * إضافة نقاط للعميل عند الشراء
   */
  async addPointsForPurchase(
    customerId: string, 
    merchantId: string, 
    orderId: string, 
    orderAmount: number
  ): Promise<number> {
    try {
      return await runTransaction(db, async (transaction) => {
        const program = await this.getMerchantLoyaltyProgram(merchantId);
        if (!program || !program.isActive) {
          return 0;
        }

        const memberQuery = query(
          this.membersCollection,
          where('customerId', '==', customerId),
          where('merchantId', '==', merchantId)
        );
        const memberSnapshot = await getDocs(memberQuery);
        
        if (memberSnapshot.empty) {
          // إذا لم يكن العميل منضماً، انضمه تلقائياً
          await this.joinLoyaltyProgram(customerId, merchantId);
          return await this.addPointsForPurchase(customerId, merchantId, orderId, orderAmount);
        }

        const memberDoc = memberSnapshot.docs[0];
        const memberData = memberDoc.data() as CustomerLoyalty;

        // التحقق من الحد الأدنى للطلب
        if (orderAmount < program.settings.minimumOrderForPoints) {
          return 0;
        }

        // حساب النقاط
        const tierConfig = program.tiers.find(t => t.tier === memberData.currentTier);
        const multiplier = tierConfig?.pointsMultiplier || 1;
        let pointsToAdd = Math.floor(orderAmount * program.settings.pointsPerSAR * multiplier);
        
        // تطبيق الحد الأقصى للنقاط لكل طلب
        if (program.settings.maxPointsPerOrder > 0) {
          pointsToAdd = Math.min(pointsToAdd, program.settings.maxPointsPerOrder);
        }

        if (pointsToAdd <= 0) {
          return 0;
        }

        // تحديث بيانات العضو
        const newLifetimeSpent = memberData.lifetimeSpent + orderAmount;
        const newLifetimeOrders = memberData.lifetimeOrders + 1;
        const newTotalPoints = memberData.totalPoints + pointsToAdd;
        const newAvailablePoints = memberData.availablePoints + pointsToAdd;

        // التحقق من ترقية المستوى
        const newTier = this.calculateTierForSpent(newLifetimeSpent, program.tiers);
        const tierUpgraded = newTier !== memberData.currentTier;

        const updatedMember: Partial<CustomerLoyalty> = {
          currentTier: newTier,
          totalPoints: newTotalPoints,
          availablePoints: newAvailablePoints,
          lifetimeSpent: newLifetimeSpent,
          lifetimeOrders: newLifetimeOrders,
          lastActivityAt: serverTimestamp() as Timestamp,
          nextTierProgress: this.calculateNextTierProgress(newTier, newLifetimeSpent, program.tiers),
          statistics: {
            ...memberData.statistics,
            totalPointsEarned: memberData.statistics.totalPointsEarned + pointsToAdd,
            averageOrderValue: newLifetimeSpent / newLifetimeOrders,
            lastPurchaseDate: serverTimestamp() as Timestamp,
          },
        };

        if (tierUpgraded) {
          updatedMember.tierAchievedAt = serverTimestamp() as Timestamp;
        }

        // تحديث العضو
        const memberRef = doc(this.membersCollection, memberDoc.id);
        transaction.update(memberRef, updatedMember);

        // إضافة معاملة النقاط
        const pointTransaction: Omit<PointTransaction, 'id'> = {
          customerId,
          merchantId,
          programId: program.id,
          type: 'earned',
          points: pointsToAdd,
          description: `نقاط من طلب #${orderId}`,
          orderId,
          createdAt: serverTimestamp() as Timestamp,
          metadata: {
            orderAmount,
            tierAtTime: memberData.currentTier,
            multiplierUsed: multiplier,
          },
        };

        const transactionRef = doc(this.transactionsCollection);
        transaction.set(transactionRef, pointTransaction);

        return pointsToAdd;
      });
    } catch (error) {
      console.error('Error adding points for purchase:', error);
      throw new Error('فشل في إضافة النقاط');
    }
  }

  /**
   * استبدال النقاط بمكافأة
   */
  async redeemReward(
    customerId: string, 
    merchantId: string, 
    rewardId: string
  ): Promise<RewardRedemption> {
    try {
      return await runTransaction(db, async (transaction) => {
        const program = await this.getMerchantLoyaltyProgram(merchantId);
        if (!program || !program.isActive) {
          throw new Error('برنامج الولاء غير متاح');
        }

        const reward = program.rewards.find(r => r.id === rewardId);
        if (!reward || !reward.isActive) {
          throw new Error('المكافأة غير متاحة');
        }

        const memberQuery = query(
          this.membersCollection,
          where('customerId', '==', customerId),
          where('merchantId', '==', merchantId)
        );
        const memberSnapshot = await getDocs(memberQuery);
        
        if (memberSnapshot.empty) {
          throw new Error('العميل غير منضم لبرنامج الولاء');
        }

        const memberDoc = memberSnapshot.docs[0];
        const memberData = memberDoc.data() as CustomerLoyalty;

        // التحقق من توفر النقاط
        if (memberData.availablePoints < reward.pointsCost) {
          throw new Error('النقاط المتاحة غير كافية');
        }

        // التحقق من قيود المستوى
        if (reward.tierRestrictions && !reward.tierRestrictions.includes(memberData.currentTier)) {
          throw new Error('هذه المكافأة غير متاحة لمستواك الحالي');
        }

        // التحقق من الكمية المتاحة
        if (reward.availableQuantity !== undefined && reward.usedQuantity >= reward.availableQuantity) {
          throw new Error('المكافأة غير متاحة حالياً');
        }

        // إنشاء كود الاستبدال
        const redemptionCode = this.generateRedemptionCode();

        // إنشاء سجل الاستبدال
        const redemption: Omit<RewardRedemption, 'id'> = {
          customerId,
          merchantId,
          rewardId,
          pointsUsed: reward.pointsCost,
          status: 'pending',
          redemptionCode,
          redeemedAt: serverTimestamp() as Timestamp,
          expiresAt: reward.validUntil,
        };

        const redemptionRef = doc(this.redemptionsCollection);
        transaction.set(redemptionRef, redemption);

        // تحديث نقاط العميل
        const updatedMember: Partial<CustomerLoyalty> = {
          availablePoints: memberData.availablePoints - reward.pointsCost,
          lastActivityAt: serverTimestamp() as Timestamp,
          statistics: {
            ...memberData.statistics,
            totalPointsRedeemed: memberData.statistics.totalPointsRedeemed + reward.pointsCost,
          },
        };

        const memberRef = doc(this.membersCollection, memberDoc.id);
        transaction.update(memberRef, updatedMember);

        // إضافة معاملة استبدال النقاط
        const pointTransaction: Omit<PointTransaction, 'id'> = {
          customerId,
          merchantId,
          programId: program.id,
          type: 'redeemed',
          points: -reward.pointsCost,
          description: `استبدال مكافأة: ${reward.name}`,
          rewardId,
          createdAt: serverTimestamp() as Timestamp,
          metadata: {
            tierAtTime: memberData.currentTier,
          },
        };

        const transactionRef = doc(this.transactionsCollection);
        transaction.set(transactionRef, pointTransaction);

        // تحديث عداد استخدام المكافأة
        const programRef = doc(this.programsCollection, program.id);
        const updatedRewards = program.rewards.map(r => 
          r.id === rewardId ? { ...r, usedQuantity: r.usedQuantity + 1 } : r
        );
        transaction.update(programRef, { 
          rewards: updatedRewards,
          updatedAt: serverTimestamp()
        });

        return { id: redemptionRef.id, ...redemption } as RewardRedemption;
      });
    } catch (error) {
      console.error('Error redeeming reward:', error);
      throw new Error('فشل في استبدال المكافأة');
    }
  }

  /**
   * الحصول على معاملات النقاط للعميل
   */
  async getCustomerTransactions(
    customerId: string, 
    merchantId: string, 
    limit: number = 20
  ): Promise<PointTransaction[]> {
    try {
      const q = query(
        this.transactionsCollection,
        where('customerId', '==', customerId),
        where('merchantId', '==', merchantId),
        orderBy('createdAt', 'desc'),
        limit(limit)
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as PointTransaction));
    } catch (error) {
      console.error('Error getting customer transactions:', error);
      return [];
    }
  }

  /**
   * حساب المستوى بناءً على الإنفاق
   */
  private calculateTierForSpent(lifetimeSpent: number, tiers: LoyaltyTierConfig[]): LoyaltyTier {
    const sortedTiers = [...tiers].sort((a, b) => b.minSpent - a.minSpent);
    
    for (const tier of sortedTiers) {
      if (lifetimeSpent >= tier.minSpent) {
        return tier.tier;
      }
    }
    
    return 'bronze';
  }

  /**
   * حساب تقدم المستوى التالي
   */
  private calculateNextTierProgress(
    currentTier: LoyaltyTier, 
    currentSpent: number, 
    tiers: LoyaltyTierConfig[]
  ) {
    const tierOrder: LoyaltyTier[] = ['bronze', 'silver', 'gold', 'platinum'];
    const currentTierIndex = tierOrder.indexOf(currentTier);
    
    if (currentTierIndex === tierOrder.length - 1) {
      // أعلى مستوى
      return {
        nextTier: null,
        currentSpent,
        requiredSpent: currentSpent,
        progressPercentage: 100,
      };
    }

    const nextTier = tierOrder[currentTierIndex + 1];
    const nextTierConfig = tiers.find(t => t.tier === nextTier);
    
    if (!nextTierConfig) {
      return {
        nextTier: null,
        currentSpent,
        requiredSpent: currentSpent,
        progressPercentage: 100,
      };
    }

    const requiredSpent = nextTierConfig.minSpent;
    const progressPercentage = Math.min((currentSpent / requiredSpent) * 100, 100);

    return {
      nextTier,
      currentSpent,
      requiredSpent,
      progressPercentage,
    };
  }

  /**
   * الحصول على المستويات من قاعدة البيانات
   */
  private async getLoyaltyTiers(): Promise<LoyaltyTierConfig[]> {
    try {
      const { db } = await import('@/lib/firebase');
      const { collection, query, orderBy, getDocs } = await import('firebase/firestore');

      const tiersRef = collection(db, 'loyalty_tiers');
      const q = query(tiersRef, orderBy('minSpent', 'asc'));
      const snapshot = await getDocs(q);

      const tiers: LoyaltyTierConfig[] = [];
      snapshot.forEach(doc => {
        const data = doc.data();
        tiers.push({
          tier: data.tier,
          name: data.name,
          description: data.description,
          minSpent: data.minSpent,
          pointsMultiplier: data.pointsMultiplier,
          benefits: data.benefits || [],
          color: data.color,
          icon: data.icon,
        });
      });

      // إذا لم توجد مستويات في قاعدة البيانات، إنشاء المستويات الافتراضية
      if (tiers.length === 0) {
        await this.createDefaultTiers();
        return await this.getLoyaltyTiers(); // استدعاء مرة أخرى بعد الإنشاء
      }

      return tiers;
    } catch (error) {
      console.error('خطأ في جلب مستويات الولاء:', error);
      // إرجاع المستويات الافتراضية في حالة الخطأ
      return this.getDefaultTiersData();
    }
  }

  /**
   * إنشاء المستويات الافتراضية في قاعدة البيانات
   */
  private async createDefaultTiers(): Promise<void> {
    try {
      const { db } = await import('@/lib/firebase');
      const { collection, addDoc } = await import('firebase/firestore');

      const defaultTiers = this.getDefaultTiersData();
      const tiersRef = collection(db, 'loyalty_tiers');

      for (const tier of defaultTiers) {
        await addDoc(tiersRef, {
          ...tier,
          createdAt: new Date(),
          isActive: true
        });
      }

      console.log('تم إنشاء مستويات الولاء الافتراضية');
    } catch (error) {
      console.error('خطأ في إنشاء مستويات الولاء الافتراضية:', error);
    }
  }

  /**
   * البيانات الافتراضية للمستويات (للاستخدام عند الإنشاء أو في حالة الخطأ)
   */
  private getDefaultTiersData(): LoyaltyTierConfig[] {
    return [
      {
        tier: 'bronze',
        name: 'البرونزي',
        description: 'مستوى البداية',
        minSpent: 0,
        pointsMultiplier: 1.0,
        benefits: ['نقاط أساسية', 'عروض خاصة'],
        color: '#CD7F32',
        icon: 'bronze-medal',
      },
      {
        tier: 'silver',
        name: 'الفضي',
        description: 'مستوى متوسط',
        minSpent: 1000,
        pointsMultiplier: 1.25,
        benefits: ['نقاط إضافية 25%', 'شحن مجاني', 'دعم أولوية'],
        color: '#C0C0C0',
        icon: 'silver-medal',
      },
      {
        tier: 'gold',
        name: 'الذهبي',
        description: 'مستوى متقدم',
        minSpent: 5000,
        pointsMultiplier: 1.5,
        benefits: ['نقاط إضافية 50%', 'خصومات حصرية', 'وصول مبكر للمنتجات'],
        color: '#FFD700',
        icon: 'gold-medal',
      },
      {
        tier: 'platinum',
        name: 'البلاتيني',
        description: 'أعلى مستوى',
        minSpent: 15000,
        pointsMultiplier: 2.0,
        benefits: ['نقاط مضاعفة', 'مدير حساب شخصي', 'هدايا حصرية', 'دعوات لفعاليات خاصة'],
        color: '#E5E4E2',
        icon: 'platinum-medal',
      },
    ];
  }

  /**
   * توليد معرف فريد
   */
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * توليد كود استبدال
   */
  private generateRedemptionCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * الحصول على أعضاء برنامج الولاء مع الفلترة
   */
  async getLoyaltyMembers(
    merchantId: string,
    filters?: LoyaltyMemberFilters,
    sortOptions?: LoyaltyMemberSortOptions,
    page: number = 1,
    pageLimit: number = 20
  ): Promise<LoyaltyMembersResponse> {
    try {
      let q = query(
        this.membersCollection,
        where('merchantId', '==', merchantId)
      );

      // تطبيق الفلاتر
      if (filters) {
        if (filters.tier && filters.tier.length > 0) {
          q = query(q, where('currentTier', 'in', filters.tier));
        }

        if (filters.joinDateRange) {
          q = query(q,
            where('joinedAt', '>=', Timestamp.fromDate(filters.joinDateRange.start)),
            where('joinedAt', '<=', Timestamp.fromDate(filters.joinDateRange.end))
          );
        }
      }

      // تطبيق الترتيب
      if (sortOptions) {
        q = query(q, orderBy(sortOptions.field, sortOptions.direction));
      } else {
        q = query(q, orderBy('joinedAt', 'desc'));
      }

      // تطبيق التصفح
      q = query(q, limit(pageLimit));
      if (page > 1) {
        const skipCount = (page - 1) * pageLimit;
        const skipQuery = query(
          this.membersCollection,
          where('merchantId', '==', merchantId),
          orderBy('joinedAt', 'desc'),
          limit(skipCount)
        );
        const skipSnapshot = await getDocs(skipQuery);
        if (!skipSnapshot.empty) {
          const lastDoc = skipSnapshot.docs[skipSnapshot.docs.length - 1];
          q = query(q, startAfter(lastDoc));
        }
      }

      const querySnapshot = await getDocs(q);
      const members: CustomerLoyalty[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CustomerLoyalty));

      // تطبيق فلاتر إضافية في الذاكرة
      let filteredMembers = members;
      if (filters) {
        if (filters.pointsRange) {
          filteredMembers = filteredMembers.filter(member =>
            member.availablePoints >= (filters.pointsRange!.min || 0) &&
            member.availablePoints <= (filters.pointsRange!.max || Infinity)
          );
        }

        if (filters.spentRange) {
          filteredMembers = filteredMembers.filter(member =>
            member.lifetimeSpent >= (filters.spentRange!.min || 0) &&
            member.lifetimeSpent <= (filters.spentRange!.max || Infinity)
          );
        }
      }

      return {
        members: filteredMembers,
        total: filteredMembers.length,
        page,
        limit: pageLimit,
        hasMore: querySnapshot.docs.length === pageLimit
      };
    } catch (error) {
      console.error('Error getting loyalty members:', error);
      throw new Error('فشل في جلب أعضاء برنامج الولاء');
    }
  }

  /**
   * الحصول على إحصائيات برنامج الولاء
   */
  async getLoyaltyAnalytics(merchantId: string, dateRange?: { start: Date; end: Date }): Promise<LoyaltyAnalytics> {
    try {
      const program = await this.getMerchantLoyaltyProgram(merchantId);
      if (!program) {
        throw new Error('برنامج الولاء غير موجود');
      }

      // الحصول على جميع الأعضاء
      let membersQuery = query(
        this.membersCollection,
        where('merchantId', '==', merchantId)
      );

      if (dateRange) {
        membersQuery = query(membersQuery,
          where('joinedAt', '>=', Timestamp.fromDate(dateRange.start)),
          where('joinedAt', '<=', Timestamp.fromDate(dateRange.end))
        );
      }

      const membersSnapshot = await getDocs(membersQuery);
      const members: CustomerLoyalty[] = membersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CustomerLoyalty));

      // الحصول على المعاملات
      let transactionsQuery = query(
        this.transactionsCollection,
        where('merchantId', '==', merchantId)
      );

      if (dateRange) {
        transactionsQuery = query(transactionsQuery,
          where('createdAt', '>=', Timestamp.fromDate(dateRange.start)),
          where('createdAt', '<=', Timestamp.fromDate(dateRange.end))
        );
      }

      const transactionsSnapshot = await getDocs(transactionsQuery);
      const transactions: PointTransaction[] = transactionsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as PointTransaction));

      // حساب الإحصائيات
      const totalMembers = members.length;
      const activeMembers = members.filter(m => {
        const lastActivity = m.lastActivityAt.toDate();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return lastActivity > thirtyDaysAgo;
      }).length;

      const membersByTier = members.reduce((acc, member) => {
        acc[member.currentTier] = (acc[member.currentTier] || 0) + 1;
        return acc;
      }, {} as Record<LoyaltyTier, number>);

      const totalPointsIssued = transactions
        .filter(t => t.type === 'earned' || t.type === 'bonus')
        .reduce((sum, t) => sum + t.points, 0);

      const totalPointsRedeemed = transactions
        .filter(t => t.type === 'redeemed')
        .reduce((sum, t) => sum + Math.abs(t.points), 0);

      const totalPointsExpired = transactions
        .filter(t => t.type === 'expired')
        .reduce((sum, t) => sum + Math.abs(t.points), 0);

      const averagePointsPerMember = totalMembers > 0 ?
        members.reduce((sum, m) => sum + m.totalPoints, 0) / totalMembers : 0;

      const redemptionRate = totalPointsIssued > 0 ?
        (totalPointsRedeemed / totalPointsIssued) * 100 : 0;

      // أفضل المكافآت
      const rewardRedemptions = transactions.filter(t => t.type === 'redeemed' && t.rewardId);
      const rewardStats = rewardRedemptions.reduce((acc, t) => {
        if (!t.rewardId) return acc;

        if (!acc[t.rewardId]) {
          const reward = program.rewards.find(r => r.id === t.rewardId);
          acc[t.rewardId] = {
            rewardId: t.rewardId,
            name: reward?.name || 'مكافأة محذوفة',
            redemptionCount: 0,
            pointsUsed: 0,
          };
        }

        acc[t.rewardId].redemptionCount++;
        acc[t.rewardId].pointsUsed += Math.abs(t.points);
        return acc;
      }, {} as Record<string, any>);

      const topRewards = Object.values(rewardStats)
        .sort((a: any, b: any) => b.redemptionCount - a.redemptionCount)
        .slice(0, 5);

      // الإحصائيات الشهرية
      const monthlyStats = this.groupTransactionsByMonth(transactions);

      return {
        programId: program.id,
        totalMembers,
        activeMembers,
        membersByTier,
        totalPointsIssued,
        totalPointsRedeemed,
        totalPointsExpired,
        averagePointsPerMember,
        redemptionRate,
        memberRetentionRate: totalMembers > 0 ? (activeMembers / totalMembers) * 100 : 0,
        averageOrderValueIncrease: 0, // يحتاج حساب معقد
        topRewards,
        monthlyStats,
        tierDistribution: Object.entries(membersByTier).map(([tier, count]) => ({
          tier: tier as LoyaltyTier,
          count,
          percentage: totalMembers > 0 ? (count / totalMembers) * 100 : 0,
          averageSpent: members
            .filter(m => m.currentTier === tier)
            .reduce((sum, m) => sum + m.lifetimeSpent, 0) / (count || 1),
        })),
      };
    } catch (error) {
      console.error('Error getting loyalty analytics:', error);
      throw new Error('فشل في جلب إحصائيات برنامج الولاء');
    }
  }

  /**
   * تجميع المعاملات حسب الشهر
   */
  private groupTransactionsByMonth(transactions: PointTransaction[]) {
    const monthlyData: Record<string, {
      newMembers: number;
      pointsEarned: number;
      pointsRedeemed: number;
      redemptions: number;
    }> = {};

    transactions.forEach(transaction => {
      const date = transaction.createdAt.toDate();
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          newMembers: 0,
          pointsEarned: 0,
          pointsRedeemed: 0,
          redemptions: 0
        };
      }

      if (transaction.type === 'earned' || transaction.type === 'bonus') {
        monthlyData[monthKey].pointsEarned += transaction.points;
      } else if (transaction.type === 'redeemed') {
        monthlyData[monthKey].pointsRedeemed += Math.abs(transaction.points);
        monthlyData[monthKey].redemptions++;
      }
    });

    return Object.entries(monthlyData).map(([monthKey, data]) => {
      const [year, month] = monthKey.split('-');
      return {
        month,
        year: parseInt(year),
        ...data
      };
    }).sort((a, b) => {
      if (a.year !== b.year) return a.year - b.year;
      return parseInt(a.month) - parseInt(b.month);
    });
  }

  /**
   * إضافة نقاط إضافية (مكافأة)
   */
  async addBonusPoints(
    customerId: string,
    merchantId: string,
    points: number,
    reason: string
  ): Promise<void> {
    try {
      await runTransaction(db, async (transaction) => {
        const memberQuery = query(
          this.membersCollection,
          where('customerId', '==', customerId),
          where('merchantId', '==', merchantId)
        );
        const memberSnapshot = await getDocs(memberQuery);

        if (memberSnapshot.empty) {
          throw new Error('العميل غير منضم لبرنامج الولاء');
        }

        const memberDoc = memberSnapshot.docs[0];
        const memberData = memberDoc.data() as CustomerLoyalty;

        // تحديث نقاط العميل
        const updatedMember: Partial<CustomerLoyalty> = {
          totalPoints: memberData.totalPoints + points,
          availablePoints: memberData.availablePoints + points,
          lastActivityAt: serverTimestamp() as Timestamp,
          statistics: {
            ...memberData.statistics,
            totalPointsEarned: memberData.statistics.totalPointsEarned + points,
          },
        };

        const memberRef = doc(this.membersCollection, memberDoc.id);
        transaction.update(memberRef, updatedMember);

        // إضافة معاملة النقاط الإضافية
        const pointTransaction: Omit<PointTransaction, 'id'> = {
          customerId,
          merchantId,
          programId: memberData.programId,
          type: 'bonus',
          points,
          description: reason,
          createdAt: serverTimestamp() as Timestamp,
          metadata: {
            bonusReason: 'manual_bonus',
            tierAtTime: memberData.currentTier,
          },
        };

        const transactionRef = doc(this.transactionsCollection);
        transaction.set(transactionRef, pointTransaction);
      });
    } catch (error) {
      console.error('Error adding bonus points:', error);
      throw new Error('فشل في إضافة النقاط الإضافية');
    }
  }

  /**
   * تفعيل/إلغاء تفعيل برنامج الولاء
   */
  async toggleLoyaltyProgram(programId: string): Promise<void> {
    try {
      const program = await this.getLoyaltyProgramById(programId);
      if (!program) {
        throw new Error('برنامج الولاء غير موجود');
      }

      await this.updateLoyaltyProgram(programId, {
        isActive: !program.isActive,
        status: !program.isActive ? 'active' : 'inactive'
      });
    } catch (error) {
      console.error('Error toggling loyalty program:', error);
      throw new Error('فشل في تغيير حالة برنامج الولاء');
    }
  }

  /**
   * الحصول على برنامج الولاء بالمعرف
   */
  async getLoyaltyProgramById(programId: string): Promise<LoyaltyProgram | null> {
    try {
      const programRef = doc(this.programsCollection, programId);
      const programDoc = await getDoc(programRef);

      if (!programDoc.exists()) {
        return null;
      }

      return { id: programDoc.id, ...programDoc.data() } as LoyaltyProgram;
    } catch (error) {
      console.error('Error getting loyalty program by ID:', error);
      return null;
    }
  }
}

export const loyaltyService = new LoyaltyService();
