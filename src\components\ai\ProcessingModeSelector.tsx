"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Smartphone, 
  Monitor, 
  Cloud, 
  Zap, 
  Battery, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';
import { 
  selectOptimalProcessing, 
  getPerformanceWarnings, 
  getPerformanceTips,
  type ProcessingOptions 
} from '@/utils/smart-processing-selector';
import { detectDeviceCapabilities, type DeviceCapabilities } from '@/utils/device-capabilities';

interface ProcessingModeSelectorProps {
  documentCount: number;
  documentSizes: number[];
  onModeSelect: (mode: 'local' | 'cloud' | 'hybrid') => void;
  onCancel: () => void;
}

export default function ProcessingModeSelector({
  documentCount,
  documentSizes,
  onModeSelect,
  onCancel
}: ProcessingModeSelectorProps) {
  const [capabilities, setCapabilities] = useState<DeviceCapabilities | null>(null);
  const [options, setOptions] = useState<ProcessingOptions | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedMode, setSelectedMode] = useState<'local' | 'cloud' | 'hybrid'>('local');

  useEffect(() => {
    async function analyzeDevice() {
      setIsLoading(true);
      try {
        const deviceCaps = await detectDeviceCapabilities();
        const processingOpts = await selectOptimalProcessing(documentCount, documentSizes);
        
        setCapabilities(deviceCaps);
        setOptions(processingOpts);
        setSelectedMode(processingOpts.mode);
      } catch (error) {
        console.error('خطأ في تحليل الجهاز:', error);
      } finally {
        setIsLoading(false);
      }
    }

    analyzeDevice();
  }, [documentCount, documentSizes]);

  if (isLoading || !capabilities || !options) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            تحليل قدرات الجهاز...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={33} className="w-full" />
            <p className="text-sm text-muted-foreground text-center">
              جاري فحص جهازك لتحديد أفضل طريقة معالجة...
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const warnings = getPerformanceWarnings(capabilities, options);
  const tips = getPerformanceTips(capabilities);

  const getModeIcon = (mode: string) => {
    switch (mode) {
      case 'local': return <Smartphone className="h-4 w-4" />;
      case 'cloud': return <Cloud className="h-4 w-4" />;
      case 'hybrid': return <Monitor className="h-4 w-4" />;
      default: return <Zap className="h-4 w-4" />;
    }
  };

  const getModeDescription = (mode: string) => {
    switch (mode) {
      case 'local': 
        return 'معالجة محلية - خصوصية 100% لكن قد تستهلك طاقة أكثر';
      case 'cloud': 
        return 'معالجة سحابية - أسرع وأقل استهلاكاً للطاقة';
      case 'hybrid': 
        return 'معالجة هجين - توازن بين السرعة والخصوصية';
      default: 
        return '';
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* معلومات الجهاز */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            معلومات جهازك
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{capabilities.ram}MB</div>
              <div className="text-sm text-muted-foreground">الذاكرة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{capabilities.cores}</div>
              <div className="text-sm text-muted-foreground">المعالجات</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {capabilities.batteryLevel || 'غير معروف'}
                {capabilities.batteryLevel && '%'}
              </div>
              <div className="text-sm text-muted-foreground">البطارية</div>
            </div>
            <div className="text-center">
              <Badge variant={capabilities.isLowEnd ? 'destructive' : 'default'}>
                {capabilities.isLowEnd ? 'جهاز بسيط' : 'جهاز قوي'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* التحذيرات */}
      {warnings.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {warnings.map((warning, index) => (
                <div key={index}>• {warning}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* خيارات المعالجة */}
      <Card>
        <CardHeader>
          <CardTitle>اختر طريقة المعالجة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {(['local', 'cloud', 'hybrid'] as const).map((mode) => (
              <div
                key={mode}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedMode === mode 
                    ? 'border-primary bg-primary/5' 
                    : 'border-border hover:border-primary/50'
                }`}
                onClick={() => setSelectedMode(mode)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getModeIcon(mode)}
                    <div>
                      <div className="font-medium">
                        {mode === 'local' && 'معالجة محلية'}
                        {mode === 'cloud' && 'معالجة سحابية'}
                        {mode === 'hybrid' && 'معالجة هجين'}
                        {mode === options.mode && (
                          <Badge variant="secondary" className="ml-2">مُوصى به</Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {getModeDescription(mode)}
                      </div>
                    </div>
                  </div>
                  {selectedMode === mode && (
                    <CheckCircle className="h-5 w-5 text-primary" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* تقديرات الأداء */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            تقديرات المعالجة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">الوقت المتوقع: {options.estimatedTime} ثانية</span>
            </div>
            <div className="flex items-center gap-2">
              <Battery className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">استهلاك البطارية: {options.estimatedBatteryUsage}%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* نصائح الأداء */}
      {tips.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              نصائح لتحسين الأداء
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {tips.map((tip, index) => (
                <div key={index} className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{tip}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* أزرار التحكم */}
      <div className="flex gap-4 justify-end">
        <Button variant="outline" onClick={onCancel}>
          إلغاء
        </Button>
        <Button onClick={() => onModeSelect(selectedMode)}>
          بدء المعالجة ({selectedMode === 'local' ? 'محلي' : selectedMode === 'cloud' ? 'سحابي' : 'هجين'})
        </Button>
      </div>
    </div>
  );
}
