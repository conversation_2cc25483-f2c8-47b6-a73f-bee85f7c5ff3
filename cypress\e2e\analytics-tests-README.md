# 🔬 اختبارات نظام التحليلات المتقدمة

## 📋 نظرة عامة

هذا المجلد يحتوي على مجموعة شاملة من اختبارات Cypress لنظام التحليلات المتقدمة في منصة مِخْلاة. تم تصميم هذه الاختبارات للتأكد من عمل جميع مكونات النظام بشكل صحيح وبأداء مثالي.

## 📁 هيكل الاختبارات

### 1. **advanced-analytics-system.cy.ts** 📊
**الغرض**: اختبار الوظائف الأساسية لنظام التحليلات
- ✅ صفحة التقارير الرئيسية
- ✅ مؤشرات الأداء الرئيسية (KPI)
- ✅ الرسوم البيانية المتقدمة
- ✅ التبويبات (المبيعات، العملاء، المنتجات)
- ✅ وظائف التصدير
- ✅ التحديث التلقائي
- ✅ التجاوب مع الأجهزة المختلفة
- ✅ دعم اللغة العربية
- ✅ الأداء والتحميل

### 2. **analytics-services-integration.cy.ts** 🔧
**الغرض**: اختبار تكامل خدمات التحليلات
- ✅ خدمة التقارير المتقدمة (AdvancedReportsService)
- ✅ خدمة تحليل سلوك العملاء المحسنة
- ✅ مكون مؤشرات الأداء المحسن
- ✅ مكون الرسوم البيانية المتقدمة
- ✅ التكامل بين الخدمات
- ✅ معالجة الأخطاء

### 3. **analytics-performance-tests.cy.ts** ⚡
**الغرض**: اختبار أداء النظام مع البيانات الكبيرة
- ✅ اختبارات سرعة التحميل
- ✅ معالجة البيانات الكبيرة (1000+ طلب)
- ✅ أداء التحديث والتفاعل
- ✅ استهلاك الذاكرة
- ✅ الأداء على الأجهزة المختلفة
- ✅ الأداء مع الشبكة البطيئة
- ✅ أداء الرسوم البيانية
- ✅ البحث والفلترة

## 🛠️ الأدوات والتقنيات المستخدمة

### **Cypress Framework**
- **الإصدار**: أحدث إصدار مستقر
- **المتصفحات المدعومة**: Chrome, Firefox, Edge
- **أنواع الاختبارات**: E2E, Integration, Performance

### **البيانات الوهمية**
- **ملف البيانات**: `cypress/fixtures/analytics-test-data.json`
- **أنواع البيانات**: طلبات، منتجات، عملاء، تقييمات
- **حجم البيانات**: قابل للتخصيص (من 10 إلى 1000+ سجل)

### **الأوامر المخصصة**
- **ملف الأوامر**: `cypress/support/commands.ts`
- **أوامر التحليلات**: 10+ أمر مخصص
- **أوامر الأداء**: قياس الوقت والذاكرة

## 🚀 كيفية تشغيل الاختبارات

### **1. تشغيل جميع الاختبارات**
```bash
# تشغيل جميع اختبارات التحليلات
npm run test:analytics

# أو باستخدام السكريبت المخصص
node cypress/scripts/run-analytics-tests.js --all --clean --report
```

### **2. تشغيل اختبارات محددة**
```bash
# اختبارات الأداء فقط
node cypress/scripts/run-analytics-tests.js --performance

# اختبارات التكامل فقط
node cypress/scripts/run-analytics-tests.js --integration

# اختبار واحد محدد
npx cypress run --spec "cypress/e2e/advanced-analytics-system.cy.ts"
```

### **3. تشغيل تفاعلي**
```bash
# فتح واجهة Cypress التفاعلية
npx cypress open

# تشغيل في وضع headless
npx cypress run --headless
```

### **4. تشغيل مع متصفحات مختلفة**
```bash
# Chrome (افتراضي)
node cypress/scripts/run-analytics-tests.js --browser chrome

# Firefox
node cypress/scripts/run-analytics-tests.js --browser firefox

# Edge
node cypress/scripts/run-analytics-tests.js --browser edge
```

## 📊 معايير النجاح

### **الأداء**
- ⏱️ تحميل الصفحة: < 3 ثوان
- ⏱️ مؤشرات الأداء: < 2 ثانية
- ⏱️ الرسوم البيانية: < 2.5 ثانية
- ⏱️ معالجة 1000 طلب: < 10 ثوان
- 💾 استهلاك الذاكرة: < 50 MB زيادة

### **الوظائف**
- ✅ جميع مؤشرات الأداء تعرض بيانات صحيحة
- ✅ الرسوم البيانية تفاعلية وتعمل بسلاسة
- ✅ التبويبات تتنقل بدون أخطاء
- ✅ البيانات تتحدث عند تغيير الفترة الزمنية
- ✅ التصدير يعمل (محاكاة)

### **التجاوب**
- 📱 الهواتف المحمولة: 375px - 667px
- 📱 الأجهزة اللوحية: 768px - 1024px
- 🖥️ أجهزة سطح المكتب: 1920px - 1080px

### **اللغة والترجمة**
- 🌐 دعم كامل للغة العربية
- 🌐 تخطيط RTL صحيح
- 🌐 جميع النصوص مترجمة

## 🔧 إعداد البيئة

### **المتطلبات الأساسية**
```bash
# تثبيت التبعيات
npm install

# تثبيت Cypress (إذا لم يكن مثبتاً)
npm install --save-dev cypress

# تثبيت أدوات إضافية للتقارير
npm install --save-dev mochawesome mochawesome-merge marge
```

### **متغيرات البيئة**
```bash
# ملف .env.local
CYPRESS_BASE_URL=http://localhost:3000
CYPRESS_VIEWPORT_WIDTH=1280
CYPRESS_VIEWPORT_HEIGHT=720
CYPRESS_VIDEO=true
CYPRESS_SCREENSHOTS=true
```

### **إعداد Firebase للاختبار**
```javascript
// cypress.config.js
export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000',
    supportFile: 'cypress/support/e2e.ts',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    env: {
      FIREBASE_PROJECT_ID: 'your-test-project',
      FIREBASE_API_KEY: 'your-test-api-key'
    }
  }
})
```

## 📈 تقارير النتائج

### **أنواع التقارير**
1. **JSON Reports**: `cypress/results/*.json`
2. **HTML Reports**: `cypress/results/html/index.html`
3. **Screenshots**: `cypress/screenshots/`
4. **Videos**: `cypress/videos/`

### **إنتاج التقارير**
```bash
# إنتاج تقرير HTML شامل
node cypress/scripts/run-analytics-tests.js --all --report

# عرض التقرير في المتصفح
open cypress/results/html/index.html
```

## 🐛 استكشاف الأخطاء

### **مشاكل شائعة وحلولها**

#### **1. فشل تحميل البيانات**
```javascript
// التحقق من وجود البيانات الوهمية
cy.window().then((win) => {
  expect(win.localStorage.getItem('mockOrders')).to.not.be.null
})
```

#### **2. مهلة انتظار طويلة**
```javascript
// زيادة مهلة الانتظار
cy.get('[data-testid="loading-indicator"]', { timeout: 15000 })
  .should('not.exist')
```

#### **3. مشاكل التجاوب**
```javascript
// التحقق من حجم الشاشة
cy.viewport(375, 667) // iPhone X
cy.get('[data-testid="mobile-menu"]').should('be.visible')
```

#### **4. مشاكل الترجمة**
```javascript
// التحقق من اللغة
cy.get('html').should('have.attr', 'lang', 'ar')
cy.get('body').should('have.css', 'direction', 'rtl')
```

## 📝 إضافة اختبارات جديدة

### **هيكل الاختبار الأساسي**
```javascript
describe('🔬 اسم مجموعة الاختبارات', () => {
  beforeEach(() => {
    cy.mockLogin('merchant')
    cy.loadAnalyticsTestData()
  })

  afterEach(() => {
    cy.cleanAnalyticsTestData()
    cy.mockLogout()
  })

  it('يجب أن يعمل الاختبار بشكل صحيح', () => {
    cy.visitWithLocale('/merchant/reports')
    cy.waitForKPILoad()
    cy.validateKPIData()
  })
})
```

### **أفضل الممارسات**
1. **استخدم data-testid**: `[data-testid="element-name"]`
2. **اختبر السيناريوهات الحقيقية**: تفاعل المستخدم الطبيعي
3. **تحقق من الأخطاء**: اختبر حالات الفشل
4. **قس الأداء**: استخدم `measureLoadTime()`
5. **نظف البيانات**: استخدم `afterEach()` للتنظيف

## 🔄 التحديثات المستقبلية

### **ميزات مخططة**
- [ ] اختبارات الأمان والصلاحيات
- [ ] اختبارات التكامل مع APIs خارجية
- [ ] اختبارات الإشعارات في الوقت الفعلي
- [ ] اختبارات الذكاء الاصطناعي
- [ ] اختبارات الأداء المتقدمة مع WebVitals

### **تحسينات مقترحة**
- [ ] إضافة اختبارات Visual Regression
- [ ] تحسين تقارير الأداء
- [ ] إضافة اختبارات Accessibility
- [ ] تطوير اختبارات Cross-browser متقدمة

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل في الاختبارات:

1. **تحقق من الوثائق**: راجع هذا الملف أولاً
2. **فحص السجلات**: `cypress/logs/`
3. **تشغيل تفاعلي**: `npx cypress open` للتشخيص
4. **تنظيف البيانات**: `--clean` لحل مشاكل البيانات المتراكمة

---

**تم إنشاء هذه الاختبارات لضمان جودة وموثوقية نظام التحليلات المتقدمة في منصة مِخْلاة** 🚀
