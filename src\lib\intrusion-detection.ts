// src/lib/intrusion-detection.ts
// 🕵️ نظام كشف التسلل المتقدم - Apex Level

import { db } from './firebase';
import { doc, setDoc, getDoc, collection, addDoc, query, where, orderBy, limit, getDocs } from 'firebase/firestore';
import { ApexEncryptionEngine } from './encryption';

// ===== INTERFACES =====

export interface SecurityEvent {
  id?: string;
  userId?: string;
  sessionId: string;
  eventType: ThreatType;
  severity: ThreatLevel;
  timestamp: Date;
  sourceIP: string;
  userAgent: string;
  details: any;
  riskScore: number;
  status: 'active' | 'resolved' | 'investigating';
}

export interface ThreatAssessment {
  threatLevel: ThreatLevel;
  riskScore: number;
  indicators: string[];
  recommendations: string[];
  autoResponse: boolean;
  confidence: number;
}

export interface UserBehaviorProfile {
  userId: string;
  normalPatterns: {
    loginTimes: number[];
    locations: string[];
    devices: string[];
    sessionDuration: number;
    activityPatterns: any;
  };
  anomalyThreshold: number;
  lastUpdated: Date;
}

export enum ThreatType {
  BRUTE_FORCE = 'brute_force',
  SUSPICIOUS_LOGIN = 'suspicious_login',
  UNUSUAL_ACTIVITY = 'unusual_activity',
  DATA_EXFILTRATION = 'data_exfiltration',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  MALICIOUS_REQUEST = 'malicious_request',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  GEOGRAPHIC_ANOMALY = 'geographic_anomaly',
  DEVICE_ANOMALY = 'device_anomaly',
  TIME_ANOMALY = 'time_anomaly'
}

export enum ThreatLevel {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4,
  EMERGENCY = 5
}

// ===== APEX INTRUSION DETECTION SYSTEM =====

export class ApexIntrusionDetection {
  private static readonly RISK_THRESHOLDS = {
    LOW: 0.3,
    MEDIUM: 0.5,
    HIGH: 0.7,
    CRITICAL: 0.9
  };

  private static readonly MAX_LOGIN_ATTEMPTS = 5;
  private static readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
  private static readonly ANALYSIS_WINDOW = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * تحليل التهديدات في الوقت الفعلي
   */
  static async analyzeRealTimeThreats(
    userId: string,
    sessionId: string,
    requestData: any
  ): Promise<ThreatAssessment> {
    try {
      const analysis = await Promise.all([
        this.analyzeLoginPatterns(userId, requestData),
        this.analyzeBehavioralAnomalies(userId, requestData),
        this.analyzeNetworkPatterns(requestData),
        this.analyzeGeographicAnomalies(userId, requestData),
        this.analyzeDeviceFingerprint(userId, requestData),
        this.analyzeTimePatterns(userId, requestData)
      ]);

      const threatVector = this.calculateThreatVector(analysis);
      
      // تنفيذ الإجراءات المضادة إذا لزم الأمر
      if (threatVector.threatLevel >= ThreatLevel.HIGH) {
        await this.executeCountermeasures(userId, sessionId, threatVector);
      }

      // تسجيل الحدث الأمني
      await this.logSecurityEvent({
        userId,
        sessionId,
        eventType: this.determineThreatType(analysis),
        severity: threatVector.threatLevel,
        timestamp: new Date(),
        sourceIP: requestData.ip || 'unknown',
        userAgent: requestData.userAgent || 'unknown',
        details: analysis,
        riskScore: threatVector.riskScore,
        status: 'active'
      });

      return threatVector;
    } catch (error) {
      console.error('🔴 خطأ في تحليل التهديدات:', error);
      throw new Error('فشل في تحليل التهديدات');
    }
  }

  /**
   * تحليل أنماط تسجيل الدخول
   */
  private static async analyzeLoginPatterns(
    userId: string, 
    requestData: any
  ): Promise<any> {
    try {
      // جلب محاولات تسجيل الدخول الأخيرة
      const recentAttempts = await this.getRecentLoginAttempts(userId);
      
      const analysis = {
        failedAttempts: recentAttempts.filter(a => !a.success).length,
        rapidAttempts: this.detectRapidAttempts(recentAttempts),
        ipVariation: this.analyzeIPVariation(recentAttempts),
        userAgentVariation: this.analyzeUserAgentVariation(recentAttempts),
        riskScore: 0
      };

      // حساب نقاط المخاطر
      if (analysis.failedAttempts >= this.MAX_LOGIN_ATTEMPTS) {
        analysis.riskScore += 0.8;
      }
      if (analysis.rapidAttempts) {
        analysis.riskScore += 0.6;
      }
      if (analysis.ipVariation > 3) {
        analysis.riskScore += 0.4;
      }

      return analysis;
    } catch (error) {
      console.error('🔴 خطأ في تحليل أنماط تسجيل الدخول:', error);
      return { riskScore: 0 };
    }
  }

  /**
   * تحليل الشذوذ السلوكي
   */
  private static async analyzeBehavioralAnomalies(
    userId: string,
    requestData: any
  ): Promise<any> {
    try {
      const userProfile = await this.getUserBehaviorProfile(userId);
      if (!userProfile) {
        return { riskScore: 0.1 }; // مستخدم جديد
      }

      const currentTime = new Date().getHours();
      const currentLocation = requestData.location || 'unknown';
      const currentDevice = requestData.deviceFingerprint || 'unknown';

      const analysis = {
        timeAnomaly: this.isTimeAnomalous(currentTime, userProfile.normalPatterns.loginTimes),
        locationAnomaly: !userProfile.normalPatterns.locations.includes(currentLocation),
        deviceAnomaly: !userProfile.normalPatterns.devices.includes(currentDevice),
        riskScore: 0
      };

      // حساب نقاط المخاطر
      if (analysis.timeAnomaly) analysis.riskScore += 0.3;
      if (analysis.locationAnomaly) analysis.riskScore += 0.5;
      if (analysis.deviceAnomaly) analysis.riskScore += 0.4;

      return analysis;
    } catch (error) {
      console.error('🔴 خطأ في تحليل الشذوذ السلوكي:', error);
      return { riskScore: 0 };
    }
  }

  /**
   * تحليل أنماط الشبكة
   */
  private static async analyzeNetworkPatterns(requestData: any): Promise<any> {
    try {
      const analysis = {
        suspiciousIP: await this.isSuspiciousIP(requestData.ip),
        torNetwork: this.isTorNetwork(requestData.ip),
        vpnDetected: this.isVPNDetected(requestData.headers),
        riskScore: 0
      };

      if (analysis.suspiciousIP) analysis.riskScore += 0.7;
      if (analysis.torNetwork) analysis.riskScore += 0.9;
      if (analysis.vpnDetected) analysis.riskScore += 0.3;

      return analysis;
    } catch (error) {
      console.error('🔴 خطأ في تحليل أنماط الشبكة:', error);
      return { riskScore: 0 };
    }
  }

  /**
   * تحليل الشذوذ الجغرافي
   */
  private static async analyzeGeographicAnomalies(
    userId: string,
    requestData: any
  ): Promise<any> {
    try {
      const userProfile = await this.getUserBehaviorProfile(userId);
      const currentLocation = requestData.location;

      if (!userProfile || !currentLocation) {
        return { riskScore: 0 };
      }

      const analysis = {
        impossibleTravel: await this.detectImpossibleTravel(userId, currentLocation),
        newCountry: this.isNewCountry(currentLocation, userProfile.normalPatterns.locations),
        riskScore: 0
      };

      if (analysis.impossibleTravel) analysis.riskScore += 0.9;
      if (analysis.newCountry) analysis.riskScore += 0.4;

      return analysis;
    } catch (error) {
      console.error('🔴 خطأ في تحليل الشذوذ الجغرافي:', error);
      return { riskScore: 0 };
    }
  }

  /**
   * تحليل بصمة الجهاز
   */
  private static async analyzeDeviceFingerprint(
    userId: string,
    requestData: any
  ): Promise<any> {
    try {
      const deviceFingerprint = requestData.deviceFingerprint;
      const knownDevices = await this.getKnownDevices(userId);

      const analysis = {
        newDevice: !knownDevices.includes(deviceFingerprint),
        suspiciousDevice: this.isSuspiciousDevice(requestData),
        riskScore: 0
      };

      if (analysis.newDevice) analysis.riskScore += 0.3;
      if (analysis.suspiciousDevice) analysis.riskScore += 0.6;

      return analysis;
    } catch (error) {
      console.error('🔴 خطأ في تحليل بصمة الجهاز:', error);
      return { riskScore: 0 };
    }
  }

  /**
   * تحليل أنماط الوقت
   */
  private static async analyzeTimePatterns(
    userId: string,
    requestData: any
  ): Promise<any> {
    try {
      const userProfile = await this.getUserBehaviorProfile(userId);
      const currentHour = new Date().getHours();

      if (!userProfile) {
        return { riskScore: 0 };
      }

      const analysis = {
        unusualTime: this.isTimeAnomalous(currentHour, userProfile.normalPatterns.loginTimes),
        riskScore: 0
      };

      if (analysis.unusualTime) analysis.riskScore += 0.2;

      return analysis;
    } catch (error) {
      console.error('🔴 خطأ في تحليل أنماط الوقت:', error);
      return { riskScore: 0 };
    }
  }

  /**
   * حساب متجه التهديد
   */
  private static calculateThreatVector(analysis: any[]): ThreatAssessment {
    const totalRiskScore = analysis.reduce((sum, a) => sum + (a.riskScore || 0), 0) / analysis.length;
    
    let threatLevel: ThreatLevel;
    if (totalRiskScore >= this.RISK_THRESHOLDS.CRITICAL) {
      threatLevel = ThreatLevel.CRITICAL;
    } else if (totalRiskScore >= this.RISK_THRESHOLDS.HIGH) {
      threatLevel = ThreatLevel.HIGH;
    } else if (totalRiskScore >= this.RISK_THRESHOLDS.MEDIUM) {
      threatLevel = ThreatLevel.MEDIUM;
    } else {
      threatLevel = ThreatLevel.LOW;
    }

    const indicators = this.extractThreatIndicators(analysis);
    const recommendations = this.generateRecommendations(threatLevel, indicators);

    return {
      threatLevel,
      riskScore: totalRiskScore,
      indicators,
      recommendations,
      autoResponse: threatLevel >= ThreatLevel.HIGH,
      confidence: Math.min(0.95, 0.5 + (totalRiskScore * 0.5))
    };
  }

  /**
   * تنفيذ الإجراءات المضادة
   */
  private static async executeCountermeasures(
    userId: string,
    sessionId: string,
    threat: ThreatAssessment
  ): Promise<void> {
    try {
      const actions = [];

      if (threat.threatLevel >= ThreatLevel.CRITICAL) {
        actions.push(
          this.lockUserAccount(userId),
          this.invalidateAllSessions(userId),
          this.alertSecurityTeam(threat),
          this.enableEnhancedMonitoring(userId)
        );
      } else if (threat.threatLevel >= ThreatLevel.HIGH) {
        actions.push(
          this.requireAdditionalVerification(userId),
          this.limitSessionPrivileges(sessionId),
          this.alertUser(userId, threat)
        );
      }

      await Promise.all(actions);
    } catch (error) {
      console.error('🔴 خطأ في تنفيذ الإجراءات المضادة:', error);
    }
  }

  // ===== HELPER METHODS =====

  private static async getRecentLoginAttempts(userId: string): Promise<any[]> {
    // تطبيق مبسط - في الإنتاج استخدم قاعدة بيانات سريعة
    return [];
  }

  private static detectRapidAttempts(attempts: any[]): boolean {
    if (attempts.length < 3) return false;
    
    const recentAttempts = attempts.slice(-3);
    const timeSpan = recentAttempts[recentAttempts.length - 1].timestamp - recentAttempts[0].timestamp;
    
    return timeSpan < 60000; // أقل من دقيقة
  }

  private static analyzeIPVariation(attempts: any[]): number {
    const uniqueIPs = new Set(attempts.map(a => a.ip));
    return uniqueIPs.size;
  }

  private static analyzeUserAgentVariation(attempts: any[]): number {
    const uniqueUserAgents = new Set(attempts.map(a => a.userAgent));
    return uniqueUserAgents.size;
  }

  private static async getUserBehaviorProfile(userId: string): Promise<UserBehaviorProfile | null> {
    try {
      const profileRef = doc(db, 'user_behavior_profiles', userId);
      const profileSnap = await getDoc(profileRef);
      
      if (!profileSnap.exists()) {
        return null;
      }
      
      return profileSnap.data() as UserBehaviorProfile;
    } catch (error) {
      console.error('🔴 خطأ في جلب ملف السلوك:', error);
      return null;
    }
  }

  private static isTimeAnomalous(currentHour: number, normalTimes: number[]): boolean {
    if (!normalTimes || normalTimes.length === 0) return false;
    
    const tolerance = 2; // ساعتان
    return !normalTimes.some(time => Math.abs(time - currentHour) <= tolerance);
  }

  private static async isSuspiciousIP(ip: string): Promise<boolean> {
    // في الإنتاج، استخدم قاعدة بيانات التهديدات
    const suspiciousIPs = ['*************', '********']; // مثال
    return suspiciousIPs.includes(ip);
  }

  private static isTorNetwork(ip: string): boolean {
    // في الإنتاج، استخدم قاعدة بيانات Tor
    return false;
  }

  private static isVPNDetected(headers: any): boolean {
    // في الإنتاج، استخدم خدمة كشف VPN
    return false;
  }

  private static async detectImpossibleTravel(userId: string, currentLocation: string): Promise<boolean> {
    // تطبيق مبسط - في الإنتاج احسب المسافة والوقت
    return false;
  }

  private static isNewCountry(currentLocation: string, knownLocations: string[]): boolean {
    return !knownLocations.some(loc => loc.includes(currentLocation));
  }

  private static async getKnownDevices(userId: string): Promise<string[]> {
    // جلب الأجهزة المعروفة للمستخدم
    return [];
  }

  private static isSuspiciousDevice(requestData: any): boolean {
    // فحص خصائص الجهاز المشبوهة
    return false;
  }

  private static determineThreatType(analysis: any[]): ThreatType {
    // تحديد نوع التهديد بناءً على التحليل
    return ThreatType.UNUSUAL_ACTIVITY;
  }

  private static extractThreatIndicators(analysis: any[]): string[] {
    const indicators: string[] = [];
    
    analysis.forEach(a => {
      if (a.failedAttempts > 3) indicators.push('محاولات دخول فاشلة متعددة');
      if (a.rapidAttempts) indicators.push('محاولات دخول سريعة');
      if (a.locationAnomaly) indicators.push('موقع جغرافي غير مألوف');
      if (a.deviceAnomaly) indicators.push('جهاز جديد أو مشبوه');
      if (a.timeAnomaly) indicators.push('وقت دخول غير مألوف');
    });
    
    return indicators;
  }

  private static generateRecommendations(level: ThreatLevel, indicators: string[]): string[] {
    const recommendations: string[] = [];
    
    if (level >= ThreatLevel.HIGH) {
      recommendations.push('تفعيل المصادقة الثنائية فوراً');
      recommendations.push('تغيير كلمة المرور');
      recommendations.push('مراجعة الأنشطة الأخيرة');
    }
    
    if (level >= ThreatLevel.CRITICAL) {
      recommendations.push('إيقاف الحساب مؤقتاً');
      recommendations.push('التواصل مع فريق الأمان');
    }
    
    return recommendations;
  }

  private static async logSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      const eventsRef = collection(db, 'security_events');
      await addDoc(eventsRef, event);
    } catch (error) {
      console.error('🔴 خطأ في تسجيل الحدث الأمني:', error);
    }
  }

  // ===== COUNTERMEASURE METHODS =====

  private static async lockUserAccount(userId: string): Promise<void> {
    console.log(`🔒 قفل حساب المستخدم: ${userId}`);
  }

  private static async invalidateAllSessions(userId: string): Promise<void> {
    console.log(`🚫 إلغاء جميع جلسات المستخدم: ${userId}`);
  }

  private static async alertSecurityTeam(threat: ThreatAssessment): Promise<void> {
    console.log(`🚨 تنبيه فريق الأمان: ${threat.threatLevel}`);
  }

  private static async enableEnhancedMonitoring(userId: string): Promise<void> {
    console.log(`👁️ تفعيل المراقبة المحسنة للمستخدم: ${userId}`);
  }

  private static async requireAdditionalVerification(userId: string): Promise<void> {
    console.log(`🔐 طلب تحقق إضافي للمستخدم: ${userId}`);
  }

  private static async limitSessionPrivileges(sessionId: string): Promise<void> {
    console.log(`⚠️ تقييد صلاحيات الجلسة: ${sessionId}`);
  }

  private static async alertUser(userId: string, threat: ThreatAssessment): Promise<void> {
    console.log(`📢 تنبيه المستخدم: ${userId} - ${threat.threatLevel}`);
  }
}

export default ApexIntrusionDetection;
