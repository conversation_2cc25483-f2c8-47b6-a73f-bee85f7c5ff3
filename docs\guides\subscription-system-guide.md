# 💳 دليل نظام الاشتراكات - مِخْلاة

## 📋 نظرة عامة

تم تطوير نظام اشتراكات شامل ومتكامل لمنصة مِخْلاة يدعم جميع أنواع المستخدمين (العملاء، التجار، المندوبين) مع تكامل كامل مع نظام الدفع الإلكتروني.

## 🎯 الميزات الرئيسية

### ✅ **الخطط المدعومة**
- **خطط العملاء**: مجانية ومميزة (9.99 ريال/شهر)
- **خطط التجار**: أساسية (مجانية)، مميزة (99.99 ريال/شهر)، أعمال (199.99 ريال/شهر)
- **خطط المندوبين**: أساسية (مجانية)، مميزة (29.99 ريال/شهر)

### 🔄 **تدفق الاشتراك**
1. **اختيار الخطة** من صفحة الأسعار
2. **التحقق من تسجيل الدخول**
3. **معالجة الاشتراك** (مجاني أو مدفوع)
4. **تأكيد النجاح** وعرض الميزات

### 💰 **طرق الدفع المدعومة**
- البطاقات الائتمانية
- PayPal
- Apple Pay
- Google Pay
- STC Pay
- مدى

## 🏗️ البنية التقنية

### 📁 **الملفات الأساسية**

#### **صفحات الاشتراك**
```
src/app/[locale]/subscription/
├── page.tsx                 # صفحة الاشتراك الرئيسية
├── activate/page.tsx        # تفعيل الخطط المجانية
├── success/page.tsx         # صفحة النجاح
└── cancel/page.tsx          # صفحة الإلغاء
```

#### **الخدمات والـ Hooks**
```
src/services/subscriptionService.ts  # خدمة إدارة الاشتراكات
src/hooks/useSubscription.ts         # Hook للاشتراكات
```

#### **المكونات**
```
src/components/pricing/
├── PricingTabs.tsx          # تبويبات الأسعار
└── SubscriptionCard.tsx     # بطاقة الاشتراك (محدثة)
```

### 🗄️ **قاعدة البيانات**

#### **مجموعة الاشتراكات (subscriptions)**
```typescript
interface UserSubscription {
  id: string;
  userId: string;
  planId: string;
  planType: 'customer' | 'merchant' | 'representative';
  status: 'active' | 'cancelled' | 'expired' | 'pending';
  startDate: Timestamp;
  endDate?: Timestamp;
  paymentId?: string;
  transactionId?: string;
  amount?: number;
  currency?: string;
  autoRenew: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### **تاريخ الاشتراكات (subscription_history)**
```typescript
interface SubscriptionHistory {
  id: string;
  userId: string;
  subscriptionId: string;
  action: 'created' | 'activated' | 'cancelled' | 'renewed' | 'upgraded' | 'downgraded';
  fromPlanId?: string;
  toPlanId: string;
  amount?: number;
  paymentId?: string;
  reason?: string;
  createdAt: Timestamp;
}
```

## 🔧 كيفية الاستخدام

### 👨‍💻 **للمطورين**

#### **استخدام Hook الاشتراكات**
```typescript
import { useSubscription } from '@/hooks/useSubscription';

function MyComponent() {
  const {
    activeSubscription,
    currentPlan,
    loading,
    createSubscription,
    activateSubscription,
    cancelSubscription
  } = useSubscription();

  // إنشاء اشتراك جديد
  const handleSubscribe = async (planId: string) => {
    const subscriptionId = await createSubscription(planId);
    if (subscriptionId) {
      await activateSubscription(subscriptionId);
    }
  };

  return (
    <div>
      {currentPlan ? (
        <p>الخطة الحالية: {currentPlan.nameKey}</p>
      ) : (
        <p>لا يوجد اشتراك نشط</p>
      )}
    </div>
  );
}
```

#### **استخدام خدمة الاشتراكات مباشرة**
```typescript
import { subscriptionService } from '@/services/subscriptionService';

// إنشاء اشتراك
const subscriptionId = await subscriptionService.createSubscription(
  userId,
  planId,
  planType,
  paymentId,
  transactionId,
  amount
);

// جلب الاشتراك النشط
const activeSubscription = await subscriptionService.getUserActiveSubscription(userId);

// إلغاء اشتراك
await subscriptionService.cancelSubscription(subscriptionId, reason);
```

### 👤 **للمستخدمين**

#### **الاشتراك في خطة مجانية**
1. اذهب لصفحة الأسعار (`/pricing`)
2. اختر التبويب المناسب (عملاء/تجار/مندوبين)
3. انقر على "البدء" في الخطة المجانية
4. سجل دخول إذا لم تكن مسجلاً
5. انقر على "تفعيل الآن" في صفحة التفعيل

#### **الاشتراك في خطة مدفوعة**
1. اذهب لصفحة الأسعار (`/pricing`)
2. اختر التبويب المناسب
3. انقر على "اختيار الخطة" في الخطة المدفوعة
4. سجل دخول إذا لم تكن مسجلاً
5. أكمل عملية الدفع في صفحة الاشتراك
6. ستتم إعادة توجيهك لصفحة النجاح

## 🔒 الأمان والخصوصية

### 🛡️ **حماية البيانات**
- تشفير جميع معلومات الدفع
- عدم تخزين بيانات البطاقات الائتمانية
- استخدام HTTPS لجميع المعاملات
- التحقق من صحة جميع المدخلات

### 🔐 **التحقق من الصلاحيات**
- التحقق من تسجيل الدخول قبل الاشتراك
- التحقق من صحة الخطة المختارة
- منع الاشتراك المتكرر في نفس الخطة
- تسجيل جميع العمليات في التاريخ

## 🧪 الاختبارات

### 📝 **اختبارات Cypress**
```bash
# تشغيل اختبارات الاشتراكات
bun run test --spec "cypress/e2e/subscription-flow.cy.ts"
```

### ✅ **السيناريوهات المختبرة**
- عرض صفحة الأسعار بشكل صحيح
- عمل أزرار الاشتراك للخطط المجانية والمدفوعة
- تدفق الاشتراك المجاني الكامل
- تدفق الاشتراك المدفوع الكامل
- صفحات النجاح والإلغاء
- جميع أنواع الخطط (تجار، عملاء، مندوبين)
- الروابط والتنقل
- عرض الميزات في كل خطة

## 🌐 الترجمات

### 🔤 **المفاتيح الجديدة**
```json
{
  "subscriptionCheckout": "إتمام الاشتراك",
  "completeSubscription": "أكمل عملية الاشتراك في الخطة المختارة",
  "includedFeatures": "الميزات المتضمنة",
  "activatePlan": "تفعيل الخطة",
  "paymentDetails": "تفاصيل الدفع",
  "freeActivationMessage": "ستتم تفعيل الخطة المجانية فوراً بدون أي رسوم",
  "activating": "جاري التفعيل...",
  "activateFreePlan": "تفعيل الخطة المجانية",
  "subscriptionFor": "اشتراك في",
  "loadingPaymentForm": "جاري تحميل نموذج الدفع..."
}
```

## 🚀 التطوير المستقبلي

### 📈 **الميزات المخططة**
- نظام الترقية والتراجع بين الخطط
- الفواتير التلقائية والتذكيرات
- نظام الخصومات والكوبونات للاشتراكات
- تقارير مفصلة لاستخدام الميزات
- API للتكامل مع أنظمة خارجية

### 🔧 **التحسينات التقنية**
- تحسين أداء قاعدة البيانات
- إضافة المزيد من طرق الدفع
- تحسين واجهة المستخدم
- إضافة إشعارات فورية للاشتراكات
- نظام النسخ الاحتياطي للاشتراكات

## 📞 الدعم الفني

### 🆘 **المشاكل الشائعة**
- **الزر لا يعمل**: تأكد من تسجيل الدخول
- **فشل الدفع**: تحقق من طريقة الدفع
- **عدم ظهور الميزات**: تحقق من حالة الاشتراك
- **مشاكل التوجيه**: امسح الكاش وأعد المحاولة

### 📧 **التواصل**
- البريد الإلكتروني: <EMAIL>
- الدعم الفني: متاح 24/7
- الوثائق: docs.mikhla.com
- GitHub Issues: للمشاكل التقنية
