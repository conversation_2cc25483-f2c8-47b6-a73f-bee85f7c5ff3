// src/app/api/ai/auto-approve-representative/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { RepresentativeAIApprovalService } from '@/services/representativeAIApprovalService';
import type { RepresentativeDocument } from '@/types/representative';

export async function POST(request: NextRequest) {
  try {
    const { representativeUid } = await request.json();

    // التحقق من صحة البيانات
    if (!representativeUid) {
      return NextResponse.json(
        { error: 'مطلوب معرف المندوب' },
        { status: 400 }
      );
    }

    // جلب بيانات المندوب
    const representativeDocRef = doc(db, 'representatives', representativeUid);
    const representativeDocSnap = await getDoc(representativeDocRef);

    if (!representativeDocSnap.exists()) {
      return NextResponse.json(
        { error: 'المندوب غير موجود' },
        { status: 404 }
      );
    }

    const representativeData = representativeDocSnap.data() as RepresentativeDocument;

    // التحقق من حالة المندوب
    if (representativeData.approvalStatus !== 'pending') {
      return NextResponse.json(
        { error: 'المندوب ليس في حالة انتظار الموافقة' },
        { status: 400 }
      );
    }

    // اتخاذ قرار الموافقة التلقائية
    const decision = await RepresentativeAIApprovalService.makeRepresentativeAutoApprovalDecision(
      representativeUid,
      representativeData
    );

    // تطبيق القرار
    const success = await RepresentativeAIApprovalService.applyRepresentativeAutoApprovalDecision(
      representativeUid,
      decision
    );

    if (!success) {
      return NextResponse.json(
        { error: 'فشل في تطبيق قرار الموافقة' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      decision: decision.decision,
      confidence: decision.confidence,
      reasons: decision.reasons,
      riskFactors: decision.riskFactors,
      recommendations: decision.recommendations,
      extractedData: decision.extractedData
    });

  } catch (error) {
    console.error('خطأ في الموافقة التلقائية للمندوب:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في النظام' },
      { status: 500 }
    );
  }
}

// GET endpoint لجلب إحصائيات الموافقة التلقائية للمندوبين
export async function GET(request: NextRequest) {
  try {
    // يمكن إضافة إحصائيات الموافقة التلقائية هنا
    const stats = {
      totalProcessed: 0,
      autoApproved: 0,
      autoRejected: 0,
      manualReview: 0,
      averageConfidence: 0,
      averageProcessingTime: 0,
      successRate: 0
    };

    // في التطبيق الحقيقي، ستجلب هذه البيانات من قاعدة البيانات
    // const statsQuery = query(
    //   collection(db, 'representatives'),
    //   where('aiAnalysis', '!=', null)
    // );
    // const statsSnapshot = await getDocs(statsQuery);
    // ... حساب الإحصائيات

    return NextResponse.json(stats);
  } catch (error) {
    console.error('خطأ في جلب إحصائيات الموافقة التلقائية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الإحصائيات' },
      { status: 500 }
    );
  }
}
