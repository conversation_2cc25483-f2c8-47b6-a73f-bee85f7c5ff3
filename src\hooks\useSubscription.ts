// src/hooks/useSubscription.ts
"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { subscriptionService, type UserSubscription, type SubscriptionHistory } from '@/services/subscriptionService';
import { merchantPlans, customerPlans, representativePlans } from '@/constants/plans';
import type { SubscriptionPlan } from '@/types';

interface UseSubscriptionReturn {
  // حالة الاشتراك
  activeSubscription: UserSubscription | null;
  subscriptionHistory: SubscriptionHistory[];
  currentPlan: SubscriptionPlan | null;
  loading: boolean;
  error: string | null;
  
  // وظائف الاشتراك
  createSubscription: (planId: string, paymentId?: string, transactionId?: string, amount?: number) => Promise<string | null>;
  activateSubscription: (subscriptionId: string, transactionId?: string) => Promise<boolean>;
  cancelSubscription: (subscriptionId: string, reason?: string) => Promise<boolean>;
  
  // وظائف التحقق
  hasActiveSubscription: boolean;
  isValidSubscription: (requiredPlanType?: string) => Promise<boolean>;
  
  // وظائف مساعدة
  refreshSubscription: () => Promise<void>;
}

export function useSubscription(): UseSubscriptionReturn {
  const { user } = useAuth();
  const [activeSubscription, setActiveSubscription] = useState<UserSubscription | null>(null);
  const [subscriptionHistory, setSubscriptionHistory] = useState<SubscriptionHistory[]>([]);
  const [currentPlan, setCurrentPlan] = useState<SubscriptionPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // جلب جميع الخطط
  const getAllPlans = useCallback((): SubscriptionPlan[] => {
    return [...merchantPlans, ...customerPlans, ...representativePlans];
  }, []);

  // جلب الخطة بواسطة ID
  const getPlanById = useCallback((planId: string): SubscriptionPlan | null => {
    const allPlans = getAllPlans();
    return allPlans.find(plan => plan.id === planId) || null;
  }, [getAllPlans]);

  // جلب الاشتراك النشط
  const fetchActiveSubscription = useCallback(async () => {
    if (!user) {
      setActiveSubscription(null);
      setCurrentPlan(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const subscription = await subscriptionService.getUserActiveSubscription(user.uid);
      setActiveSubscription(subscription);

      if (subscription) {
        const plan = getPlanById(subscription.planId);
        setCurrentPlan(plan);
      } else {
        setCurrentPlan(null);
      }
    } catch (err) {
      console.error('Error fetching active subscription:', err);
      setError('فشل في جلب معلومات الاشتراك');
    } finally {
      setLoading(false);
    }
  }, [user, getPlanById]);

  // جلب تاريخ الاشتراكات
  const fetchSubscriptionHistory = useCallback(async () => {
    if (!user) {
      setSubscriptionHistory([]);
      return;
    }

    try {
      const history = await subscriptionService.getUserSubscriptionHistory(user.uid);
      setSubscriptionHistory(history);
    } catch (err) {
      console.error('Error fetching subscription history:', err);
    }
  }, [user]);

  // إنشاء اشتراك جديد
  const createSubscription = useCallback(async (
    planId: string,
    paymentId?: string,
    transactionId?: string,
    amount?: number
  ): Promise<string | null> => {
    if (!user) {
      setError('يجب تسجيل الدخول أولاً');
      return null;
    }

    try {
      setError(null);
      
      // تحديد نوع الخطة
      const plan = getPlanById(planId);
      if (!plan) {
        throw new Error('الخطة غير موجودة');
      }

      let planType: 'customer' | 'merchant' | 'representative';
      if (plan.id.startsWith('customer')) {
        planType = 'customer';
      } else if (plan.id.startsWith('merchant')) {
        planType = 'merchant';
      } else if (plan.id.startsWith('representative')) {
        planType = 'representative';
      } else {
        throw new Error('نوع الخطة غير صحيح');
      }

      const subscriptionId = await subscriptionService.createSubscription(
        user.uid,
        planId,
        planType,
        paymentId,
        transactionId,
        amount
      );

      // تحديث البيانات المحلية
      await fetchActiveSubscription();
      await fetchSubscriptionHistory();

      return subscriptionId;
    } catch (err) {
      console.error('Error creating subscription:', err);
      setError(err instanceof Error ? err.message : 'فشل في إنشاء الاشتراك');
      return null;
    }
  }, [user, getPlanById, fetchActiveSubscription, fetchSubscriptionHistory]);

  // تفعيل اشتراك
  const activateSubscription = useCallback(async (
    subscriptionId: string,
    transactionId?: string
  ): Promise<boolean> => {
    try {
      setError(null);
      
      const success = await subscriptionService.activateSubscription(subscriptionId, transactionId);
      
      if (success) {
        // تحديث البيانات المحلية
        await fetchActiveSubscription();
        await fetchSubscriptionHistory();
      }

      return success;
    } catch (err) {
      console.error('Error activating subscription:', err);
      setError(err instanceof Error ? err.message : 'فشل في تفعيل الاشتراك');
      return false;
    }
  }, [fetchActiveSubscription, fetchSubscriptionHistory]);

  // إلغاء اشتراك
  const cancelSubscription = useCallback(async (
    subscriptionId: string,
    reason?: string
  ): Promise<boolean> => {
    try {
      setError(null);
      
      const success = await subscriptionService.cancelSubscription(subscriptionId, reason);
      
      if (success) {
        // تحديث البيانات المحلية
        await fetchActiveSubscription();
        await fetchSubscriptionHistory();
      }

      return success;
    } catch (err) {
      console.error('Error cancelling subscription:', err);
      setError(err instanceof Error ? err.message : 'فشل في إلغاء الاشتراك');
      return false;
    }
  }, [fetchActiveSubscription, fetchSubscriptionHistory]);

  // التحقق من صحة الاشتراك
  const isValidSubscription = useCallback(async (requiredPlanType?: string): Promise<boolean> => {
    if (!user) return false;
    
    try {
      return await subscriptionService.validateSubscription(user.uid, requiredPlanType);
    } catch (err) {
      console.error('Error validating subscription:', err);
      return false;
    }
  }, [user]);

  // تحديث الاشتراك
  const refreshSubscription = useCallback(async () => {
    await fetchActiveSubscription();
    await fetchSubscriptionHistory();
  }, [fetchActiveSubscription, fetchSubscriptionHistory]);

  // تحميل البيانات عند تغيير المستخدم
  useEffect(() => {
    fetchActiveSubscription();
    fetchSubscriptionHistory();
  }, [fetchActiveSubscription, fetchSubscriptionHistory]);

  // مراقبة الاشتراك النشط في الوقت الفعلي
  useEffect(() => {
    if (!user) return;

    const unsubscribe = subscriptionService.subscribeToUserActiveSubscription(
      user.uid,
      (subscription) => {
        setActiveSubscription(subscription);
        
        if (subscription) {
          const plan = getPlanById(subscription.planId);
          setCurrentPlan(plan);
        } else {
          setCurrentPlan(null);
        }
      }
    );

    return unsubscribe;
  }, [user, getPlanById]);

  return {
    // حالة الاشتراك
    activeSubscription,
    subscriptionHistory,
    currentPlan,
    loading,
    error,
    
    // وظائف الاشتراك
    createSubscription,
    activateSubscription,
    cancelSubscription,
    
    // وظائف التحقق
    hasActiveSubscription: !!activeSubscription,
    isValidSubscription,
    
    // وظائف مساعدة
    refreshSubscription,
  };
}
