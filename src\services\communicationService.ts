// src/services/communicationService.ts
"use client";

import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDocs, 
  query, 
  where, 
  orderBy,
  Timestamp,
  serverTimestamp,
  limit
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { 
  CustomerCommunication, 
  CustomerProfile,
  CustomerSegment
} from '@/types';

export class CommunicationService {
  private communicationsCollection = collection(db, 'customer_communications');
  private customersCollection = collection(db, 'customer_profiles');
  private segmentsCollection = collection(db, 'customer_segments');

  // ===== إدارة الحملات =====

  // إنشاء حملة تواصل جديدة
  async createCommunication(
    communicationData: Omit<CustomerCommunication, 'id' | 'createdAt' | 'updatedAt' | 'sentAt' | 'stats'>
  ): Promise<string> {
    try {
      const communication: Omit<CustomerCommunication, 'id'> = {
        ...communicationData,
        status: 'draft',
        stats: {
          targetCount: 0,
          sentCount: 0,
          deliveredCount: 0,
          openedCount: 0,
          clickedCount: 0,
          repliedCount: 0,
          unsubscribedCount: 0,
          failedCount: 0,
          deliveryRate: 0,
          openRate: 0,
          clickRate: 0,
          responseRate: 0,
          unsubscribeRate: 0
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      const communicationRef = await addDoc(this.communicationsCollection, communication);
      
      // حساب عدد المستهدفين
      await this.calculateTargetCount(communicationRef.id);

      return communicationRef.id;
    } catch (error) {
      console.error('Error creating communication:', error);
      throw new Error('فشل في إنشاء حملة التواصل');
    }
  }

  // تحديث حملة التواصل
  async updateCommunication(
    communicationId: string, 
    updates: Partial<CustomerCommunication>
  ): Promise<void> {
    try {
      const communicationRef = doc(this.communicationsCollection, communicationId);
      await updateDoc(communicationRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });

      // إعادة حساب المستهدفين إذا تم تغيير الاستهداف
      if (updates.targets) {
        await this.calculateTargetCount(communicationId);
      }
    } catch (error) {
      console.error('Error updating communication:', error);
      throw new Error('فشل في تحديث حملة التواصل');
    }
  }

  // جلب حملات التواصل للتاجر
  async getMerchantCommunications(
    merchantId: string,
    limitCount: number = 20
  ): Promise<CustomerCommunication[]> {
    try {
      const q = query(
        this.communicationsCollection,
        where('merchantId', '==', merchantId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CustomerCommunication[];
    } catch (error) {
      console.error('Error fetching merchant communications:', error);
      throw new Error('فشل في جلب حملات التواصل');
    }
  }

  // ===== حساب المستهدفين =====

  // حساب عدد العملاء المستهدفين
  async calculateTargetCount(communicationId: string): Promise<void> {
    try {
      const communicationRef = doc(this.communicationsCollection, communicationId);
      const communicationDoc = await communicationRef.get();
      
      if (!communicationDoc.exists()) {
        throw new Error('حملة التواصل غير موجودة');
      }

      const communication = communicationDoc.data() as CustomerCommunication;
      const targetCustomers = await this.getTargetCustomers(communication);

      await updateDoc(communicationRef, {
        'stats.targetCount': targetCustomers.length,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error calculating target count:', error);
      throw new Error('فشل في حساب عدد المستهدفين');
    }
  }

  // جلب العملاء المستهدفين
  async getTargetCustomers(communication: CustomerCommunication): Promise<CustomerProfile[]> {
    try {
      let targetCustomers: CustomerProfile[] = [];

      switch (communication.targets.type) {
        case 'all':
          targetCustomers = await this.getAllMerchantCustomers(communication.merchantId);
          break;

        case 'segment':
          if (communication.targets.segmentIds) {
            targetCustomers = await this.getSegmentCustomers(
              communication.merchantId, 
              communication.targets.segmentIds
            );
          }
          break;

        case 'individual':
          if (communication.targets.customerIds) {
            targetCustomers = await this.getSpecificCustomers(
              communication.merchantId,
              communication.targets.customerIds
            );
          }
          break;

        case 'custom':
          if (communication.targets.criteria) {
            targetCustomers = await this.getCustomCriteriaCustomers(
              communication.merchantId,
              communication.targets.criteria
            );
          }
          break;
      }

      // فلترة العملاء الذين وافقوا على التسويق
      return targetCustomers.filter(customer => 
        customer.preferences.marketingOptIn &&
        customer.preferences.communicationChannel === communication.content.type ||
        communication.content.type === 'push' // الإشعارات المباشرة دائماً مسموحة
      );
    } catch (error) {
      console.error('Error getting target customers:', error);
      throw new Error('فشل في جلب العملاء المستهدفين');
    }
  }

  // جلب جميع عملاء التاجر
  private async getAllMerchantCustomers(merchantId: string): Promise<CustomerProfile[]> {
    const q = query(
      this.customersCollection,
      where('merchantId', '==', merchantId)
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as CustomerProfile[];
  }

  // جلب عملاء التقسيمات المحددة
  private async getSegmentCustomers(
    merchantId: string, 
    segmentIds: string[]
  ): Promise<CustomerProfile[]> {
    const allCustomers = await this.getAllMerchantCustomers(merchantId);
    const targetCustomers: CustomerProfile[] = [];

    for (const segmentId of segmentIds) {
      const segmentRef = doc(this.segmentsCollection, segmentId);
      const segmentDoc = await segmentRef.get();
      
      if (segmentDoc.exists()) {
        const segment = segmentDoc.data() as CustomerSegment;
        // هنا نحتاج لاستخدام خدمة التقسيم لفلترة العملاء
        // const segmentCustomers = await customerSegmentationService.filterCustomersBySegmentCriteria(allCustomers, segment.criteria);
        // targetCustomers.push(...segmentCustomers);
      }
    }

    return targetCustomers;
  }

  // جلب عملاء محددين
  private async getSpecificCustomers(
    merchantId: string,
    customerIds: string[]
  ): Promise<CustomerProfile[]> {
    const customers: CustomerProfile[] = [];

    for (const customerId of customerIds) {
      const customerRef = doc(this.customersCollection, `${merchantId}_${customerId}`);
      const customerDoc = await customerRef.get();
      
      if (customerDoc.exists()) {
        customers.push({ id: customerDoc.id, ...customerDoc.data() } as CustomerProfile);
      }
    }

    return customers;
  }

  // جلب عملاء بمعايير مخصصة
  private async getCustomCriteriaCustomers(
    merchantId: string,
    criteria: any
  ): Promise<CustomerProfile[]> {
    // تطبيق معايير مخصصة للبحث
    const allCustomers = await this.getAllMerchantCustomers(merchantId);
    
    // هنا يمكن تطبيق فلاتر مخصصة بناءً على المعايير
    return allCustomers.filter(customer => {
      // مثال: فلترة بناءً على إجمالي الإنفاق
      if (criteria.minSpent && customer.shoppingBehavior.totalSpent < criteria.minSpent) {
        return false;
      }
      
      if (criteria.maxSpent && customer.shoppingBehavior.totalSpent > criteria.maxSpent) {
        return false;
      }

      // مثال: فلترة بناءً على آخر طلب
      if (criteria.lastOrderDays && customer.shoppingBehavior.lastOrderDate) {
        const daysSinceLastOrder = Math.floor(
          (Date.now() - customer.shoppingBehavior.lastOrderDate.toMillis()) / (1000 * 60 * 60 * 24)
        );
        if (daysSinceLastOrder > criteria.lastOrderDays) {
          return false;
        }
      }

      return true;
    });
  }

  // ===== إرسال الرسائل =====

  // إرسال حملة التواصل
  async sendCommunication(communicationId: string): Promise<void> {
    try {
      const communicationRef = doc(this.communicationsCollection, communicationId);
      const communicationDoc = await communicationRef.get();
      
      if (!communicationDoc.exists()) {
        throw new Error('حملة التواصل غير موجودة');
      }

      const communication = communicationDoc.data() as CustomerCommunication;
      
      if (communication.status !== 'draft' && communication.status !== 'scheduled') {
        throw new Error('لا يمكن إرسال هذه الحملة');
      }

      // تحديث الحالة إلى "جاري الإرسال"
      await updateDoc(communicationRef, {
        status: 'sending',
        updatedAt: serverTimestamp()
      });

      // جلب العملاء المستهدفين
      const targetCustomers = await this.getTargetCustomers(communication);

      // إرسال الرسائل
      const results = await this.sendMessagesToCustomers(communication, targetCustomers);

      // تحديث الإحصائيات
      await this.updateCommunicationStats(communicationId, results);

      // تحديث الحالة إلى "تم الإرسال"
      await updateDoc(communicationRef, {
        status: 'sent',
        sentAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

    } catch (error) {
      console.error('Error sending communication:', error);
      
      // تحديث الحالة إلى "فشل"
      const communicationRef = doc(this.communicationsCollection, communicationId);
      await updateDoc(communicationRef, {
        status: 'failed',
        updatedAt: serverTimestamp()
      });

      throw new Error('فشل في إرسال حملة التواصل');
    }
  }

  // إرسال الرسائل للعملاء
  private async sendMessagesToCustomers(
    communication: CustomerCommunication,
    customers: CustomerProfile[]
  ): Promise<{
    sent: number;
    delivered: number;
    failed: number;
  }> {
    let sent = 0;
    let delivered = 0;
    let failed = 0;

    for (const customer of customers) {
      try {
        const personalizedMessage = this.personalizeMessage(communication.content.message, customer);
        
        switch (communication.content.type) {
          case 'email':
            await this.sendEmail(customer, communication.content.subject || '', personalizedMessage);
            break;
          case 'sms':
            await this.sendSMS(customer, personalizedMessage);
            break;
          case 'push':
            await this.sendPushNotification(customer, communication.content.subject || '', personalizedMessage);
            break;
          case 'whatsapp':
            await this.sendWhatsApp(customer, personalizedMessage);
            break;
          case 'in_app':
            await this.sendInAppNotification(customer, communication.content.subject || '', personalizedMessage);
            break;
        }

        sent++;
        delivered++; // في التطبيق الحقيقي، نحتاج للتحقق من التسليم الفعلي
      } catch (error) {
        console.error(`Failed to send message to customer ${customer.id}:`, error);
        failed++;
      }
    }

    return { sent, delivered, failed };
  }

  // تخصيص الرسالة للعميل
  private personalizeMessage(template: string, customer: CustomerProfile): string {
    return template
      .replace(/\{name\}/g, customer.personalInfo.name)
      .replace(/\{email\}/g, customer.personalInfo.email)
      .replace(/\{phone\}/g, customer.personalInfo.phone || '')
      .replace(/\{totalOrders\}/g, customer.shoppingBehavior.totalOrders.toString())
      .replace(/\{totalSpent\}/g, customer.shoppingBehavior.totalSpent.toString())
      .replace(/\{tier\}/g, customer.segmentation.tier);
  }

  // إرسال بريد إلكتروني
  private async sendEmail(customer: CustomerProfile, subject: string, message: string): Promise<void> {
    try {
      // تكامل حقيقي مع Firebase Cloud Functions أو خدمة البريد الإلكتروني
      const emailData = {
        to: customer.personalInfo.email,
        subject,
        html: message,
        from: process.env.NEXT_PUBLIC_SENDER_EMAIL || '<EMAIL>'
      };

      // إرسال عبر Firebase Cloud Functions
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData)
      });

      if (!response.ok) {
        throw new Error(`فشل في إرسال البريد الإلكتروني: ${response.statusText}`);
      }

      // تسجيل نجاح الإرسال
      await this.logCommunicationEvent(customer.id, 'email', 'sent', subject);
    } catch (error) {
      console.error('خطأ في إرسال البريد الإلكتروني:', error);
      await this.logCommunicationEvent(customer.id, 'email', 'failed', subject);
      throw error;
    }
  }

  // إرسال رسالة نصية
  private async sendSMS(customer: CustomerProfile, message: string): Promise<void> {
    try {
      // تكامل حقيقي مع خدمة الرسائل النصية
      const smsData = {
        to: customer.personalInfo.phone,
        message,
        from: process.env.NEXT_PUBLIC_SMS_SENDER || 'Mikhla'
      };

      // إرسال عبر Firebase Cloud Functions
      const response = await fetch('/api/send-sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(smsData)
      });

      if (!response.ok) {
        throw new Error(`فشل في إرسال الرسالة النصية: ${response.statusText}`);
      }

      // تسجيل نجاح الإرسال
      await this.logCommunicationEvent(customer.id, 'sms', 'sent', message);
    } catch (error) {
      console.error('خطأ في إرسال الرسالة النصية:', error);
      await this.logCommunicationEvent(customer.id, 'sms', 'failed', message);
      throw error;
    }
  }

  // إرسال إشعار مباشر
  private async sendPushNotification(customer: CustomerProfile, title: string, message: string): Promise<void> {
    try {
      // تكامل حقيقي مع Firebase Cloud Messaging
      const { getMessaging } = await import('firebase/messaging');
      const { messaging } = await import('@/lib/firebase');

      if (!messaging) {
        throw new Error('Firebase Messaging غير متاح');
      }

      // الحصول على token المستخدم من قاعدة البيانات
      const userTokens = await this.getUserFCMTokens(customer.id);

      if (userTokens.length === 0) {
        console.warn(`لا توجد FCM tokens للمستخدم ${customer.id}`);
        return;
      }

      // إرسال الإشعار لجميع أجهزة المستخدم
      const notificationData = {
        title,
        body: message,
        icon: '/icons/notification-icon.png',
        badge: '/icons/badge-icon.png',
        data: {
          customerId: customer.id,
          timestamp: Date.now().toString()
        }
      };

      const response = await fetch('/api/send-push-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokens: userTokens,
          notification: notificationData
        })
      });

      if (!response.ok) {
        throw new Error(`فشل في إرسال الإشعار المباشر: ${response.statusText}`);
      }

      // تسجيل نجاح الإرسال
      await this.logCommunicationEvent(customer.id, 'push', 'sent', title);
    } catch (error) {
      console.error('خطأ في إرسال الإشعار المباشر:', error);
      await this.logCommunicationEvent(customer.id, 'push', 'failed', title);
      throw error;
    }
  }

  // إرسال رسالة واتساب
  private async sendWhatsApp(customer: CustomerProfile, message: string): Promise<void> {
    try {
      // تكامل حقيقي مع WhatsApp Business API
      const whatsappData = {
        to: customer.personalInfo.phone,
        message,
        type: 'text'
      };

      const response = await fetch('/api/send-whatsapp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(whatsappData)
      });

      if (!response.ok) {
        throw new Error(`فشل في إرسال رسالة واتساب: ${response.statusText}`);
      }

      // تسجيل نجاح الإرسال
      await this.logCommunicationEvent(customer.id, 'whatsapp', 'sent', message);
    } catch (error) {
      console.error('خطأ في إرسال رسالة واتساب:', error);
      await this.logCommunicationEvent(customer.id, 'whatsapp', 'failed', message);
      throw error;
    }
  }

  // إرسال إشعار داخل التطبيق
  private async sendInAppNotification(customer: CustomerProfile, title: string, message: string): Promise<void> {
    try {
      // إضافة إشعار حقيقي في قاعدة البيانات
      const { db } = await import('@/lib/firebase');
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

      const notificationData = {
        userId: customer.id,
        title,
        message,
        type: 'in_app',
        isRead: false,
        createdAt: serverTimestamp(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 يوم
      };

      const notificationsRef = collection(db, 'notifications');
      await addDoc(notificationsRef, notificationData);

      // تسجيل نجاح الإرسال
      await this.logCommunicationEvent(customer.id, 'in_app', 'sent', title);
    } catch (error) {
      console.error('خطأ في إرسال الإشعار داخل التطبيق:', error);
      await this.logCommunicationEvent(customer.id, 'in_app', 'failed', title);
      throw error;
    }
  }

  // الحصول على FCM tokens للمستخدم
  private async getUserFCMTokens(userId: string): Promise<string[]> {
    try {
      const { db } = await import('@/lib/firebase');
      const { collection, query, where, getDocs } = await import('firebase/firestore');

      const tokensRef = collection(db, 'user_fcm_tokens');
      const q = query(tokensRef, where('userId', '==', userId), where('isActive', '==', true));
      const snapshot = await getDocs(q);

      const tokens: string[] = [];
      snapshot.forEach(doc => {
        const data = doc.data();
        if (data.token) {
          tokens.push(data.token);
        }
      });

      return tokens;
    } catch (error) {
      console.error('خطأ في الحصول على FCM tokens:', error);
      return [];
    }
  }

  // تسجيل أحداث الاتصال
  private async logCommunicationEvent(
    userId: string,
    type: 'email' | 'sms' | 'push' | 'whatsapp' | 'in_app',
    status: 'sent' | 'failed',
    content: string
  ): Promise<void> {
    try {
      const { db } = await import('@/lib/firebase');
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

      const logData = {
        userId,
        type,
        status,
        content: content.substring(0, 100), // حفظ أول 100 حرف فقط
        timestamp: serverTimestamp(),
        metadata: {
          userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
          ip: 'server-side' // يمكن تحسينه لاحقاً
        }
      };

      const logsRef = collection(db, 'communication_logs');
      await addDoc(logsRef, logData);
    } catch (error) {
      console.error('خطأ في تسجيل حدث الاتصال:', error);
      // لا نرمي خطأ هنا لأن التسجيل ليس حرجاً
    }
  }

  // تحديث إحصائيات الحملة
  private async updateCommunicationStats(
    communicationId: string,
    results: { sent: number; delivered: number; failed: number }
  ): Promise<void> {
    const communicationRef = doc(this.communicationsCollection, communicationId);
    
    const deliveryRate = results.sent > 0 ? (results.delivered / results.sent) * 100 : 0;

    await updateDoc(communicationRef, {
      'stats.sentCount': results.sent,
      'stats.deliveredCount': results.delivered,
      'stats.failedCount': results.failed,
      'stats.deliveryRate': deliveryRate,
      updatedAt: serverTimestamp()
    });
  }

  // ===== تتبع التفاعل =====

  // تسجيل فتح الرسالة
  async trackMessageOpen(communicationId: string, customerId: string): Promise<void> {
    try {
      const communicationRef = doc(this.communicationsCollection, communicationId);
      await updateDoc(communicationRef, {
        'stats.openedCount': increment(1),
        updatedAt: serverTimestamp()
      });

      // حساب معدل الفتح
      await this.recalculateRates(communicationId);
    } catch (error) {
      console.error('Error tracking message open:', error);
    }
  }

  // تسجيل النقر على الرابط
  async trackMessageClick(communicationId: string, customerId: string): Promise<void> {
    try {
      const communicationRef = doc(this.communicationsCollection, communicationId);
      await updateDoc(communicationRef, {
        'stats.clickedCount': increment(1),
        updatedAt: serverTimestamp()
      });

      // حساب معدل النقر
      await this.recalculateRates(communicationId);
    } catch (error) {
      console.error('Error tracking message click:', error);
    }
  }

  // إعادة حساب المعدلات
  private async recalculateRates(communicationId: string): Promise<void> {
    const communicationRef = doc(this.communicationsCollection, communicationId);
    const communicationDoc = await communicationRef.get();
    
    if (communicationDoc.exists()) {
      const stats = communicationDoc.data().stats;
      
      const openRate = stats.deliveredCount > 0 ? (stats.openedCount / stats.deliveredCount) * 100 : 0;
      const clickRate = stats.openedCount > 0 ? (stats.clickedCount / stats.openedCount) * 100 : 0;
      const responseRate = stats.deliveredCount > 0 ? (stats.repliedCount / stats.deliveredCount) * 100 : 0;
      const unsubscribeRate = stats.deliveredCount > 0 ? (stats.unsubscribedCount / stats.deliveredCount) * 100 : 0;

      await updateDoc(communicationRef, {
        'stats.openRate': openRate,
        'stats.clickRate': clickRate,
        'stats.responseRate': responseRate,
        'stats.unsubscribeRate': unsubscribeRate,
        updatedAt: serverTimestamp()
      });
    }
  }
}

export const communicationService = new CommunicationService();
