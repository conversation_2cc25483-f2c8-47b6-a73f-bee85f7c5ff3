import type {NextConfig} from 'next';

const nextConfig: NextConfig = {
  /* تحسينات الأداء */
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
    ],
    // تحسين: إعدادات الصور للأداء
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // تحسين: ضغط وتحسين الأداء
  compress: true,
  poweredByHeader: false,
  experimental: {
    // تحسين: تحسينات تجريبية للأداء
    optimizeCss: true,
    scrollRestoration: true,
    // تحسين تحميل الترجمات
    optimizePackageImports: ['@/locales'],
  },
  // إعدادات SWC لتجنب التحذيرات (Next.js 15 compatible)
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
  },
  // تحسين معالجة الأخطاء
  serverExternalPackages: ['firebase'],
  // تحسين الأداء العام
  onDemandEntries: {
    // فترة الاحتفاظ بالصفحات في الذاكرة
    maxInactiveAge: 25 * 1000,
    // عدد الصفحات المحتفظ بها
    pagesBufferLength: 2,
  },

  // إعدادات PWA وmanifest
  async generateBuildId() {
    return 'mikhla-build';
  },

  reactStrictMode: true,
  // تحسين: إعدادات الـ headers للأداء
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
      {
        source: '/fonts/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
