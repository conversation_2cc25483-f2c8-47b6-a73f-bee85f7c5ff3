describe('اختبار إنشاء الحساب وتبديل اللغة - تفصيلي', () => {
  beforeEach(() => {
    // زيارة الصفحة الرئيسية
    cy.visit('http://localhost:9002')
    cy.wait(3000)
  })

  it('1. النقر على إنشاء حساب من الصفحة الرئيسية', () => {
    // البحث عن رابط أو زر إنشاء حساب
    cy.get('body').then(($body) => {
      if ($body.find('a[href*="signup"]').length > 0) {
        cy.get('a[href*="signup"]').first().click()
        cy.wait(3000)
        cy.url().should('include', '/signup')
      } else if ($body.find('button:contains("إنشاء حساب")').length > 0) {
        cy.get('button:contains("إنشاء حساب")').first().click()
        cy.wait(3000)
        cy.url().should('include', '/signup')
      } else {
        // الانتقال مباشرة لصفحة التسجيل
        cy.visit('http://localhost:9002/ar/signup')
        cy.wait(3000)
        cy.url().should('include', '/signup')
      }
    })
  })

  it('2. اختيار نوع المستخدم (تاجر)', () => {
    cy.visit('http://localhost:9002/ar/signup')
    cy.wait(5000)
    
    // البحث عن خيارات نوع المستخدم
    cy.get('body').then(($body) => {
      if ($body.find('input[value="merchant"]').length > 0) {
        cy.get('input[value="merchant"]').click()
        cy.wait(1000)
      } else if ($body.find('button:contains("تاجر")').length > 0) {
        cy.get('button:contains("تاجر")').click()
        cy.wait(1000)
      } else if ($body.find('[data-testid="user-type-merchant"]').length > 0) {
        cy.get('[data-testid="user-type-merchant"]').click()
        cy.wait(1000)
      } else {
        cy.log('لم يتم العثور على خيار التاجر')
      }
    })
  })

  it('3. تبديل اللغة إلى الإنجليزية', () => {
    cy.visit('http://localhost:9002/ar/signup')
    cy.wait(5000)
    
    // البحث عن زر تبديل اللغة
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("EN")').length > 0) {
        cy.get('button:contains("EN")').first().click()
        cy.wait(3000)
        cy.url().should('include', '/en/')
      } else if ($body.find('[data-testid="language-switcher"]').length > 0) {
        cy.get('[data-testid="language-switcher"]').click()
        cy.wait(3000)
        cy.url().should('include', '/en/')
      } else {
        cy.log('لم يتم العثور على زر تبديل اللغة')
      }
    })
  })

  it('4. ملء نموذج التسجيل', () => {
    cy.visit('http://localhost:9002/ar/signup')
    cy.wait(5000)
    
    // البحث عن حقول النموذج وملؤها
    cy.get('body').then(($body) => {
      if ($body.find('input[name="username"]').length > 0) {
        cy.get('input[name="username"]').type('تاجر تجريبي')
      }
      
      if ($body.find('input[name="email"]').length > 0) {
        cy.get('input[name="email"]').type('<EMAIL>')
      }
      
      if ($body.find('input[type="email"]').length > 0) {
        cy.get('input[type="email"]').first().type('<EMAIL>')
      }
      
      if ($body.find('input[name="password"]').length > 0) {
        cy.get('input[name="password"]').type('Password123!')
      }
      
      if ($body.find('input[type="password"]').length > 0) {
        cy.get('input[type="password"]').first().type('Password123!')
      }
    })
  })

  it('5. اختبار شامل - إنشاء حساب مع تبديل اللغة', () => {
    // الخطوة 1: زيارة صفحة التسجيل
    cy.visit('http://localhost:9002/ar/signup')
    cy.wait(5000)
    
    // الخطوة 2: تبديل اللغة إلى الإنجليزية
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("EN")').length > 0) {
        cy.get('button:contains("EN")').first().click()
        cy.wait(3000)
        cy.url().should('include', '/en/')
        cy.log('✅ تم تبديل اللغة إلى الإنجليزية')
      }
    })
    
    // الخطوة 3: العودة للعربية
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("ع")').length > 0) {
        cy.get('button:contains("ع")').first().click()
        cy.wait(3000)
        cy.url().should('include', '/ar/')
        cy.log('✅ تم العودة للعربية')
      }
    })
    
    // الخطوة 4: التحقق من وجود عناصر الصفحة
    cy.get('body').should('contain', 'إنشاء')
    cy.log('✅ تم التحقق من محتوى الصفحة')
  })

  it('6. اختبار التنقل بين الصفحات مع تبديل اللغة', () => {
    // زيارة صفحة اختيار نوع المستخدم
    cy.visit('http://localhost:9002/ar/user-type-selection')
    cy.wait(5000)
    
    // تبديل اللغة
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("EN")').length > 0) {
        cy.get('button:contains("EN")').first().click()
        cy.wait(3000)
        cy.url().should('include', '/en/')
      }
    })
    
    // العودة للعربية
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("ع")').length > 0) {
        cy.get('button:contains("ع")').first().click()
        cy.wait(3000)
        cy.url().should('include', '/ar/')
      }
    })
  })

  it('7. اختبار صفحة تسجيل الدخول مع تبديل اللغة', () => {
    cy.visit('http://localhost:9002/ar/login')
    cy.wait(5000)
    
    // التحقق من وجود نموذج تسجيل الدخول
    cy.get('body').then(($body) => {
      if ($body.find('input[type="email"]').length > 0) {
        cy.log('✅ تم العثور على حقل البريد الإلكتروني')
      }
      
      if ($body.find('input[type="password"]').length > 0) {
        cy.log('✅ تم العثور على حقل كلمة المرور')
      }
    })
    
    // تبديل اللغة
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("EN")').length > 0) {
        cy.get('button:contains("EN")').first().click()
        cy.wait(3000)
        cy.url().should('include', '/en/')
        cy.log('✅ تم تبديل اللغة في صفحة تسجيل الدخول')
      }
    })
  })
})
