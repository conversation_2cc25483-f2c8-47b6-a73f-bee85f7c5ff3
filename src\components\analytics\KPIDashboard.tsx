"use client";

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  TrendingUp,
  TrendingDown,
  Minus,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Star,
  Clock,
  Target,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';

interface KPIData {
  id: string;
  title: string;
  value: number;
  previousValue?: number;
  target?: number;
  unit: string;
  format: 'number' | 'currency' | 'percentage' | 'time';
  trend: 'up' | 'down' | 'neutral';
  changePercentage: number;
  status: 'good' | 'warning' | 'critical';
  description?: string;
  icon: React.ReactNode;
}

interface KPIDashboardProps {
  timeRange?: '7d' | '30d' | '90d' | '1y';
  onTimeRangeChange?: (range: string) => void;
  onRefresh?: () => void;
  loading?: boolean;
  className?: string;
}

export default function KPIDashboard({
  timeRange = '30d',
  onTimeRangeChange,
  onRefresh,
  loading = false,
  className
}: KPIDashboardProps) {
  const [kpiData, setKpiData] = useState<KPIData[]>([]);

  useEffect(() => {
    loadKPIData();
  }, [timeRange]);

  const loadKPIData = async () => {
    // محاكاة تحميل البيانات - في التطبيق الحقيقي، استدعي API
    const mockData: KPIData[] = [
      {
        id: 'total_revenue',
        title: 'إجمالي الإيرادات',
        value: 125000,
        previousValue: 98000,
        target: 150000,
        unit: 'ريال',
        format: 'currency',
        trend: 'up',
        changePercentage: 27.6,
        status: 'good',
        description: 'إيرادات المبيعات الإجمالية',
        icon: <DollarSign className="w-5 h-5" />
      },
      {
        id: 'total_orders',
        title: 'إجمالي الطلبات',
        value: 1250,
        previousValue: 1100,
        target: 1500,
        unit: 'طلب',
        format: 'number',
        trend: 'up',
        changePercentage: 13.6,
        status: 'good',
        description: 'عدد الطلبات المكتملة',
        icon: <ShoppingCart className="w-5 h-5" />
      },
      {
        id: 'active_customers',
        title: 'العملاء النشطون',
        value: 850,
        previousValue: 780,
        target: 1000,
        unit: 'عميل',
        format: 'number',
        trend: 'up',
        changePercentage: 9.0,
        status: 'good',
        description: 'العملاء الذين قاموا بطلبات',
        icon: <Users className="w-5 h-5" />
      },
      {
        id: 'conversion_rate',
        title: 'معدل التحويل',
        value: 3.2,
        previousValue: 2.8,
        target: 5.0,
        unit: '%',
        format: 'percentage',
        trend: 'up',
        changePercentage: 14.3,
        status: 'warning',
        description: 'نسبة الزوار الذين يقومون بطلبات',
        icon: <Target className="w-5 h-5" />
      },
      {
        id: 'avg_order_value',
        title: 'متوسط قيمة الطلب',
        value: 100,
        previousValue: 89,
        target: 120,
        unit: 'ريال',
        format: 'currency',
        trend: 'up',
        changePercentage: 12.4,
        status: 'good',
        description: 'متوسط قيمة الطلب الواحد',
        icon: <Package className="w-5 h-5" />
      },
      {
        id: 'customer_satisfaction',
        title: 'رضا العملاء',
        value: 4.3,
        previousValue: 4.1,
        target: 4.5,
        unit: '/5',
        format: 'number',
        trend: 'up',
        changePercentage: 4.9,
        status: 'good',
        description: 'متوسط تقييم العملاء',
        icon: <Star className="w-5 h-5" />
      },
      {
        id: 'avg_delivery_time',
        title: 'متوسط وقت التسليم',
        value: 45,
        previousValue: 52,
        target: 30,
        unit: 'دقيقة',
        format: 'time',
        trend: 'up',
        changePercentage: -13.5,
        status: 'warning',
        description: 'متوسط وقت التسليم بالدقائق',
        icon: <Clock className="w-5 h-5" />
      },
      {
        id: 'order_fulfillment_rate',
        title: 'معدل إنجاز الطلبات',
        value: 94.5,
        previousValue: 91.2,
        target: 98.0,
        unit: '%',
        format: 'percentage',
        trend: 'up',
        changePercentage: 3.6,
        status: 'good',
        description: 'نسبة الطلبات المنجزة بنجاح',
        icon: <CheckCircle className="w-5 h-5" />
      }
    ];

    setKpiData(mockData);
  };

  const formatValue = (value: number, format: string, unit: string) => {
    switch (format) {
      case 'currency':
        return `${value.toLocaleString()} ${unit}`;
      case 'percentage':
        return `${value.toFixed(1)}${unit}`;
      case 'time':
        return `${value} ${unit}`;
      default:
        return `${value.toLocaleString()} ${unit}`;
    }
  };

  const getTrendIcon = (trend: string, changePercentage: number) => {
    if (trend === 'up') {
      return <TrendingUp className="w-4 h-4 text-green-500" />;
    } else if (trend === 'down') {
      return <TrendingDown className="w-4 h-4 text-red-500" />;
    }
    return <Minus className="w-4 h-4 text-gray-500" />;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getProgressPercentage = (value: number, target?: number) => {
    if (!target) return 0;
    return Math.min((value / target) * 100, 100);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* أدوات التحكم */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">مؤشرات الأداء الرئيسية</h2>
        
        <div className="flex items-center gap-2">
          {onTimeRangeChange && (
            <Select value={timeRange} onValueChange={onTimeRangeChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7 أيام</SelectItem>
                <SelectItem value="30d">30 يوم</SelectItem>
                <SelectItem value="90d">90 يوم</SelectItem>
                <SelectItem value="1y">سنة</SelectItem>
              </SelectContent>
            </Select>
          )}

          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          )}
        </div>
      </div>

      {/* شبكة مؤشرات الأداء */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiData.map((kpi) => (
          <Card key={kpi.id} className={`border-l-4 ${getStatusColor(kpi.status)}`}>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {kpi.icon}
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {kpi.title}
                  </CardTitle>
                </div>
                {getTrendIcon(kpi.trend, kpi.changePercentage)}
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              {/* القيمة الرئيسية */}
              <div className="text-2xl font-bold">
                {formatValue(kpi.value, kpi.format, kpi.unit)}
              </div>

              {/* التغيير والاتجاه */}
              <div className="flex items-center gap-2">
                <Badge 
                  variant={kpi.trend === 'up' ? 'default' : kpi.trend === 'down' ? 'destructive' : 'secondary'}
                  className="text-xs"
                >
                  {kpi.changePercentage > 0 ? '+' : ''}{kpi.changePercentage.toFixed(1)}%
                </Badge>
                <span className="text-xs text-muted-foreground">
                  مقارنة بالفترة السابقة
                </span>
              </div>

              {/* شريط التقدم نحو الهدف */}
              {kpi.target && (
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span>التقدم نحو الهدف</span>
                    <span>{getProgressPercentage(kpi.value, kpi.target).toFixed(0)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        kpi.status === 'good' ? 'bg-green-500' :
                        kpi.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${getProgressPercentage(kpi.value, kpi.target)}%` }}
                    />
                  </div>
                  <div className="text-xs text-muted-foreground">
                    الهدف: {formatValue(kpi.target, kpi.format, kpi.unit)}
                  </div>
                </div>
              )}

              {/* الوصف */}
              {kpi.description && (
                <p className="text-xs text-muted-foreground">
                  {kpi.description}
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* ملخص الحالة */}
      <Card>
        <CardHeader>
          <CardTitle>ملخص الحالة</CardTitle>
          <CardDescription>
            نظرة عامة على أداء المؤشرات الرئيسية
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <div>
                <div className="font-semibold text-green-700">
                  {kpiData.filter(kpi => kpi.status === 'good').length} مؤشر جيد
                </div>
                <div className="text-sm text-green-600">
                  يحقق الأهداف المطلوبة
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-yellow-500" />
              <div>
                <div className="font-semibold text-yellow-700">
                  {kpiData.filter(kpi => kpi.status === 'warning').length} مؤشر تحذير
                </div>
                <div className="text-sm text-yellow-600">
                  يحتاج إلى تحسين
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <div>
                <div className="font-semibold text-red-700">
                  {kpiData.filter(kpi => kpi.status === 'critical').length} مؤشر حرج
                </div>
                <div className="text-sm text-red-600">
                  يتطلب تدخل فوري
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
