// src/app/[locale]/merchant/crm/customers/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Users, 
  Search, 
  Filter, 
  Download,
  Eye,
  Mail,
  Phone,
  MapPin,
  Calendar,
  ShoppingBag,
  TrendingUp,
  Star,
  Crown,
  AlertTriangle,
  ArrowLeft
} from 'lucide-react';
import { useCRMData, type CRMFilters } from '@/hooks/useCRMData';
import { useAuth } from '@/context/AuthContext';
import { CustomerProfileCard } from '@/components/merchant/crm/CustomerProfileCard';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface CustomersPageProps {
  params: {
    locale: string;
  };
}

export default function CustomersPage({ params: { locale } }: CustomersPageProps) {
  const { user, userType } = useAuth();
  const router = useRouter();
  const { 
    customers, 
    selectedCustomer,
    loading, 
    errors,
    actions 
  } = useCRMData();

  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<CRMFilters>({});
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'cards'>('list');

  // تحميل العملاء عند تحميل الصفحة
  useEffect(() => {
    if (user?.uid) {
      actions.loadCustomers(user.uid, filters);
    }
  }, [user?.uid, filters, actions]);

  // البحث في العملاء
  const handleSearch = async () => {
    if (user?.uid) {
      if (searchQuery.trim()) {
        await actions.searchCustomers(user.uid, searchQuery, filters);
      } else {
        await actions.loadCustomers(user.uid, filters);
      }
    }
  };

  // تطبيق الفلاتر
  const handleFilterChange = (key: keyof CRMFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
  };

  // عرض ملف العميل
  const handleViewCustomer = async (customerId: string, userId: string) => {
    if (user?.uid) {
      setSelectedCustomerId(customerId);
      await actions.loadCustomer(user.uid, userId);
    }
  };

  // الحصول على لون المستوى
  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'bronze': return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'silver': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'gold': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'platinum': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // تنسيق التاريخ
  const formatDate = (timestamp: any) => {
    return timestamp?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد';
  };

  // التحقق من الصلاحية
  if (userType !== 'merchant') {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6 text-center">
            <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">غير مصرح بالوصول</h2>
            <p className="text-muted-foreground mb-4">
              هذه الصفحة متاحة للتجار فقط
            </p>
            <Button asChild>
              <Link href={`/${locale}`}>العودة للرئيسية</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* العنوان والتنقل */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/${locale}/merchant/crm`}>
            <ArrowLeft className="h-4 w-4 me-2" />
            العودة لـ CRM
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">إدارة العملاء</h1>
          <p className="text-muted-foreground">
            عرض وإدارة جميع عملائك
          </p>
        </div>
      </div>

      {/* شريط البحث والفلاتر */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            البحث والفلترة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* شريط البحث */}
          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                placeholder="البحث بالاسم، البريد الإلكتروني، أو رقم الهاتف..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch} disabled={loading.customers}>
              <Search className="h-4 w-4" />
            </Button>
          </div>

          {/* الفلاتر */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              value={filters.tier || 'all'}
              onValueChange={(value) => handleFilterChange('tier', value === 'all' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="المستوى" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المستويات</SelectItem>
                <SelectItem value="bronze">برونزي</SelectItem>
                <SelectItem value="silver">فضي</SelectItem>
                <SelectItem value="gold">ذهبي</SelectItem>
                <SelectItem value="platinum">بلاتيني</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.status || 'all'}
              onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="active">نشط</SelectItem>
                <SelectItem value="inactive">غير نشط</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" className="justify-start">
              <Filter className="h-4 w-4 me-2" />
              فلاتر متقدمة
            </Button>

            <Button variant="outline" className="justify-start">
              <Download className="h-4 w-4 me-2" />
              تصدير
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* قائمة العملاء */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* قائمة العملاء */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    العملاء ({customers.length})
                  </CardTitle>
                  <CardDescription>
                    قائمة بجميع عملائك
                  </CardDescription>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                  >
                    قائمة
                  </Button>
                  <Button
                    variant={viewMode === 'cards' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('cards')}
                  >
                    بطاقات
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading.customers ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">جاري تحميل العملاء...</p>
                </div>
              ) : errors.customers ? (
                <div className="text-center py-8">
                  <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-4" />
                  <p className="text-destructive">{errors.customers}</p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-2"
                    onClick={() => user?.uid && actions.loadCustomers(user.uid, filters)}
                  >
                    إعادة المحاولة
                  </Button>
                </div>
              ) : customers.length > 0 ? (
                <div className="space-y-3">
                  {customers.map((customer) => (
                    <div
                      key={customer.id}
                      className={cn(
                        "p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",
                        selectedCustomerId === customer.id && "border-primary bg-primary/5"
                      )}
                      onClick={() => handleViewCustomer(customer.id, customer.userId)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={customer.personalInfo.avatar} />
                            <AvatarFallback>
                              {customer.personalInfo.name.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium">{customer.personalInfo.name}</h3>
                              <Badge className={getTierColor(customer.segmentation.tier)}>
                                {customer.segmentation.tier === 'platinum' && <Crown className="h-3 w-3 me-1" />}
                                {customer.segmentation.tier}
                              </Badge>
                              {customer.tags.includes('vip') && (
                                <Badge className="bg-purple-100 text-purple-800">VIP</Badge>
                              )}
                            </div>
                            
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                {customer.personalInfo.email}
                              </span>
                              {customer.personalInfo.phone && (
                                <span className="flex items-center gap-1">
                                  <Phone className="h-3 w-3" />
                                  {customer.personalInfo.phone}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="text-right">
                          <div className="flex items-center gap-4 text-sm">
                            <div className="text-center">
                              <div className="font-medium">{customer.shoppingBehavior.totalOrders}</div>
                              <div className="text-muted-foreground">طلب</div>
                            </div>
                            <div className="text-center">
                              <div className="font-medium">
                                {customer.shoppingBehavior.totalSpent.toLocaleString()} ر.س
                              </div>
                              <div className="text-muted-foreground">إجمالي الإنفاق</div>
                            </div>
                            <div className="text-center">
                              <div className="font-medium flex items-center gap-1">
                                <Star className="h-3 w-3 text-yellow-500" />
                                {customer.stats.averageRating.toFixed(1)}
                              </div>
                              <div className="text-muted-foreground">التقييم</div>
                            </div>
                          </div>
                          
                          <div className="text-xs text-muted-foreground mt-1">
                            آخر تفاعل: {formatDate(customer.lastInteractionDate)}
                          </div>
                        </div>
                      </div>

                      {/* مؤشرات التحذير */}
                      {(customer.segmentation.churnProbability > 0.7 || customer.stats.complaintsCount > 0) && (
                        <div className="flex gap-2 mt-2">
                          {customer.segmentation.churnProbability > 0.7 && (
                            <Badge variant="destructive" className="text-xs">
                              <AlertTriangle className="h-3 w-3 me-1" />
                              معرض للفقدان
                            </Badge>
                          )}
                          {customer.stats.complaintsCount > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {customer.stats.complaintsCount} شكوى
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">لا يوجد عملاء</p>
                  {searchQuery && (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => {
                        setSearchQuery('');
                        user?.uid && actions.loadCustomers(user.uid);
                      }}
                    >
                      مسح البحث
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* ملف العميل المحدد */}
        <div className="lg:col-span-1">
          {selectedCustomer ? (
            <CustomerProfileCard
              customer={selectedCustomer}
              onUpdateTags={async (tags) => {
                if (user?.uid) {
                  await actions.updateCustomerTags(user.uid, selectedCustomer.userId, tags);
                }
              }}
              onAddNote={async (note) => {
                if (user?.uid) {
                  await actions.addCustomerNote(user.uid, selectedCustomer.userId, note);
                }
              }}
            />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Eye className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    اختر عميلاً لعرض ملفه الشخصي
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
