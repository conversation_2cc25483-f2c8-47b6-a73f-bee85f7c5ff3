'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Star, 
  Gift, 
  TrendingUp, 
  Award, 
  Crown,
  Sparkles,
  Calendar,
  ShoppingBag
} from 'lucide-react';
import { loyaltyService } from '@/services/loyaltyService';
import { CustomerLoyalty, LoyaltyTier } from '@/types/loyalty';
import { useLocale } from '@/hooks/use-locale';
import { toast } from 'sonner';

interface LoyaltyCardProps {
  customerId: string;
  merchantId: string;
  onRewardsClick?: () => void;
}

export function LoyaltyCard({ customerId, merchantId, onRewardsClick }: LoyaltyCardProps) {
  const { t } = useLocale();
  const [loyaltyData, setLoyaltyData] = useState<CustomerLoyalty | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadLoyaltyData();
  }, [customerId, merchantId]);

  const loadLoyaltyData = async () => {
    try {
      setLoading(true);
      const data = await loyaltyService.getCustomerLoyalty(customerId, merchantId);
      setLoyaltyData(data);
    } catch (error) {
      console.error('Error loading loyalty data:', error);
      toast.error('فشل في تحميل بيانات الولاء');
    } finally {
      setLoading(false);
    }
  };

  const getTierIcon = (tier: LoyaltyTier) => {
    switch (tier) {
      case 'bronze':
        return <Award className="h-5 w-5 text-amber-600" />;
      case 'silver':
        return <Star className="h-5 w-5 text-gray-400" />;
      case 'gold':
        return <Crown className="h-5 w-5 text-yellow-500" />;
      case 'platinum':
        return <Sparkles className="h-5 w-5 text-purple-500" />;
      default:
        return <Award className="h-5 w-5" />;
    }
  };

  const getTierColor = (tier: LoyaltyTier) => {
    switch (tier) {
      case 'bronze':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'silver':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'gold':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'platinum':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTierName = (tier: LoyaltyTier) => {
    switch (tier) {
      case 'bronze':
        return 'البرونزي';
      case 'silver':
        return 'الفضي';
      case 'gold':
        return 'الذهبي';
      case 'platinum':
        return 'البلاتيني';
      default:
        return tier;
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('ar-SA');
  };

  const handleJoinProgram = async () => {
    try {
      await loyaltyService.joinLoyaltyProgram(customerId, merchantId);
      toast.success('تم الانضمام لبرنامج الولاء بنجاح!');
      loadLoyaltyData();
    } catch (error: any) {
      toast.error(error.message || 'فشل في الانضمام لبرنامج الولاء');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!loyaltyData) {
    return (
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5 text-blue-600" />
            برنامج الولاء
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <div className="mb-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Star className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                انضم لبرنامج الولاء
              </h3>
              <p className="text-blue-700 text-sm">
                احصل على نقاط مع كل عملية شراء واستبدلها بمكافآت رائعة
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">نقاط</div>
                <div className="text-sm text-blue-500">مع كل شراء</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">مكافآت</div>
                <div className="text-sm text-blue-500">حصرية</div>
              </div>
            </div>

            <Button onClick={handleJoinProgram} className="w-full">
              انضم الآن مجاناً
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Gift className="h-5 w-5 text-blue-600" />
            برنامج الولاء
          </div>
          <Badge className={`${getTierColor(loyaltyData.currentTier)} border`}>
            <div className="flex items-center gap-1">
              {getTierIcon(loyaltyData.currentTier)}
              {getTierName(loyaltyData.currentTier)}
            </div>
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* النقاط المتاحة */}
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-600 mb-1">
            {loyaltyData.availablePoints.toLocaleString()}
          </div>
          <div className="text-sm text-blue-500">نقطة متاحة</div>
        </div>

        {/* تقدم المستوى التالي */}
        {loyaltyData.nextTierProgress.nextTier && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>التقدم للمستوى التالي</span>
              <span className="font-medium">
                {getTierName(loyaltyData.nextTierProgress.nextTier)}
              </span>
            </div>
            <Progress 
              value={loyaltyData.nextTierProgress.progressPercentage} 
              className="h-2"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>
                {loyaltyData.nextTierProgress.currentSpent.toLocaleString()} ريال
              </span>
              <span>
                {loyaltyData.nextTierProgress.requiredSpent.toLocaleString()} ريال
              </span>
            </div>
          </div>
        )}

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-white rounded-lg border">
            <div className="flex items-center justify-center mb-2">
              <ShoppingBag className="h-4 w-4 text-gray-500" />
            </div>
            <div className="text-lg font-semibold text-gray-900">
              {loyaltyData.lifetimeOrders}
            </div>
            <div className="text-xs text-gray-500">طلب</div>
          </div>
          
          <div className="text-center p-3 bg-white rounded-lg border">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="h-4 w-4 text-gray-500" />
            </div>
            <div className="text-lg font-semibold text-gray-900">
              {loyaltyData.lifetimeSpent.toLocaleString()}
            </div>
            <div className="text-xs text-gray-500">ريال</div>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              تاريخ الانضمام:
            </span>
            <span>{formatDate(loyaltyData.joinedAt)}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span>إجمالي النقاط المكتسبة:</span>
            <span className="font-medium">
              {loyaltyData.statistics.totalPointsEarned.toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span>النقاط المستبدلة:</span>
            <span className="font-medium">
              {loyaltyData.statistics.totalPointsRedeemed.toLocaleString()}
            </span>
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1"
            onClick={onRewardsClick}
          >
            <Gift className="h-4 w-4 mr-1" />
            المكافآت
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1"
            onClick={() => {/* فتح تاريخ النقاط */}}
          >
            <Calendar className="h-4 w-4 mr-1" />
            التاريخ
          </Button>
        </div>

        {/* نصائح للحصول على نقاط */}
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <div className="text-sm font-medium text-blue-800 mb-1">
            💡 نصائح لكسب المزيد من النقاط:
          </div>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• تسوق بانتظام لكسب نقاط إضافية</li>
            <li>• ادع أصدقاءك للحصول على نقاط إحالة</li>
            <li>• تابع العروض الخاصة للنقاط المضاعفة</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
