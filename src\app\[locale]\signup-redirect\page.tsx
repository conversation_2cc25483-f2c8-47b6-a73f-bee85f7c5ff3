// src/app/[locale]/signup-redirect/page.tsx
import type { Locale } from '@/lib/i18n';
import SimpleRedirect from '@/components/auth/SimpleRedirect';

export default async function SignupRedirectPage({
  params,
  searchParams
}: {
  params: { locale: Locale };
  searchParams?: { userType?: string };
}) {
  const paramsData = await Promise.resolve(params);
  const searchParamsData = await Promise.resolve(searchParams);
  const locale = paramsData.locale;
  const userType = searchParamsData?.userType || 'customer';

  return <SimpleRedirect locale={locale} userType={userType} />;
}
