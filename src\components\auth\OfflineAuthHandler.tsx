// src/components/auth/OfflineAuthHandler.tsx
"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Loader2, Wifi, WifiOff, CheckCircle } from 'lucide-react';
import type { Locale } from '@/lib/i18n';

interface OfflineAuthHandlerProps {
  locale: Locale;
  userType: string;
  fallbackDelay?: number;
}

export default function OfflineAuthHandler({ 
  locale, 
  userType, 
  fallbackDelay = 5000 
}: OfflineAuthHandlerProps) {
  const router = useRouter();
  const [isOnline, setIsOnline] = useState(true);
  const [timeLeft, setTimeLeft] = useState(fallbackDelay / 1000);
  const [hasRedirected, setHasRedirected] = useState(false);

  // مراقبة حالة الاتصال
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // العد التنازلي والتوجيه التلقائي
  useEffect(() => {
    if (hasRedirected) return;

    const interval = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(interval);
          handleRedirect();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [hasRedirected]);

  const handleRedirect = () => {
    if (hasRedirected) return;
    
    setHasRedirected(true);
    
    // تحديد مسار التوجيه
    let targetPath = `/${locale}/dashboard`;
    
    if (userType === 'merchant') {
      targetPath = `/${locale}/merchant/pending-approval`;
    } else if (userType === 'representative') {
      targetPath = `/${locale}/representative/signup`;
    }

    console.log('🔄 Offline redirect to:', targetPath);
    
    // حفظ بيانات التسجيل محلياً
    try {
      localStorage.setItem('userSignedUp', 'true');
      localStorage.setItem('userType', userType);
      localStorage.setItem('signupTime', Date.now().toString());
    } catch (error) {
      console.warn('Storage error:', error);
    }

    // التوجيه المباشر
    window.location.href = targetPath;
  };

  const getTargetPageName = () => {
    switch (userType) {
      case 'merchant': return 'صفحة انتظار الموافقة';
      case 'representative': return 'صفحة إكمال التسجيل';
      default: return 'لوحة التحكم';
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <div className="flex flex-col items-center justify-center space-y-6 text-center max-w-md">
        {/* أيقونة الحالة */}
        <div className="relative">
          {hasRedirected ? (
            <CheckCircle className="h-16 w-16 text-green-500" />
          ) : isOnline ? (
            <Loader2 className="h-16 w-16 animate-spin text-primary" />
          ) : (
            <WifiOff className="h-16 w-16 text-orange-500" />
          )}
          
          {/* مؤشر الاتصال */}
          <div className="absolute -top-2 -right-2">
            {isOnline ? (
              <Wifi className="h-6 w-6 text-green-500" />
            ) : (
              <WifiOff className="h-6 w-6 text-red-500" />
            )}
          </div>
        </div>

        {/* العنوان والوصف */}
        <div className="space-y-2">
          <h2 className="text-xl font-semibold text-foreground">
            {hasRedirected 
              ? 'تم إنشاء حسابك بنجاح!' 
              : 'مشكلة في الاتصال بالخادم'
            }
          </h2>
          <p className="text-muted-foreground">
            {hasRedirected 
              ? 'جاري توجيهك للصفحة المناسبة...'
              : isOnline 
              ? 'يبدو أن هناك بطء في الاتصال. سيتم توجيهك تلقائياً.'
              : 'لا يوجد اتصال بالإنترنت. سيتم توجيهك عند عودة الاتصال.'
            }
          </p>
        </div>

        {/* معلومات إضافية */}
        <div className="text-sm text-muted-foreground space-y-1">
          <div className="text-blue-600">
            👤 نوع الحساب: {userType === 'merchant' ? 'تاجر' : userType === 'representative' ? 'مندوب' : 'عميل'}
          </div>
          <div className="text-purple-600">
            🎯 الوجهة: {getTargetPageName()}
          </div>
          {!hasRedirected && (
            <div className="text-orange-600">
              ⏰ التوجيه التلقائي خلال: {timeLeft} ثانية
            </div>
          )}
        </div>

        {/* أزرار التحكم */}
        {!hasRedirected && (
          <div className="flex flex-col space-y-2 w-full">
            <Button 
              onClick={handleRedirect}
              className="w-full"
              variant="default"
            >
              الانتقال الآن
            </Button>
            
            <Button 
              onClick={() => window.location.reload()}
              variant="outline"
              className="w-full"
            >
              إعادة المحاولة
            </Button>
          </div>
        )}

        {/* معلومات تقنية */}
        <div className="text-xs text-muted-foreground bg-muted p-3 rounded-lg">
          <div className="font-semibold mb-1">معلومات تقنية:</div>
          <div>حالة الاتصال: {isOnline ? '🟢 متصل' : '🔴 غير متصل'}</div>
          <div>نوع المستخدم: {userType}</div>
          <div>اللغة: {locale}</div>
        </div>
      </div>
    </div>
  );
}
