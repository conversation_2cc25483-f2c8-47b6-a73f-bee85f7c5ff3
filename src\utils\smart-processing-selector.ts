/**
 * 🧠 نظام اختيار المعالجة الذكي
 * يختار أفضل طريقة لمعالجة المستندات حسب قدرات الجهاز
 */

import { detectDeviceCapabilities, type DeviceCapabilities } from './device-capabilities';

export type ProcessingMode = 'local' | 'cloud' | 'hybrid';

export interface ProcessingOptions {
  mode: ProcessingMode;
  useProgressiveLoading: boolean;
  enableBatching: boolean;
  maxConcurrentProcessing: number;
  showPerformanceWarning: boolean;
  estimatedTime: number; // بالثواني
  estimatedBatteryUsage: number; // بالنسبة المئوية
}

/**
 * اختيار أفضل طريقة معالجة
 */
export async function selectOptimalProcessing(
  documentCount: number = 1,
  documentSizes: number[] = [1] // بالميجابايت
): Promise<ProcessingOptions> {
  
  const capabilities = await detectDeviceCapabilities();
  const totalSize = documentSizes.reduce((sum, size) => sum + size, 0);
  
  const options: ProcessingOptions = {
    mode: capabilities.recommendedMode,
    useProgressiveLoading: false,
    enableBatching: false,
    maxConcurrentProcessing: 1,
    showPerformanceWarning: false,
    estimatedTime: 0,
    estimatedBatteryUsage: 0
  };

  // تحديد الإعدادات حسب قدرات الجهاز
  if (capabilities.isLowEnd) {
    options.mode = 'cloud';
    options.showPerformanceWarning = true;
    options.estimatedTime = documentCount * 5; // 5 ثواني لكل مستند (سحابي)
    options.estimatedBatteryUsage = documentCount * 1; // 1% لكل مستند
  } else if (capabilities.ram >= 6144) {
    options.mode = 'local';
    options.enableBatching = documentCount > 3;
    options.maxConcurrentProcessing = Math.min(capabilities.cores / 2, 3);
    options.estimatedTime = documentCount * 15; // 15 ثانية لكل مستند (محلي)
    options.estimatedBatteryUsage = documentCount * 3; // 3% لكل مستند
  } else {
    options.mode = 'hybrid';
    options.useProgressiveLoading = true;
    options.maxConcurrentProcessing = 2;
    options.estimatedTime = documentCount * 10; // 10 ثواني لكل مستند (هجين)
    options.estimatedBatteryUsage = documentCount * 2; // 2% لكل مستند
  }

  // تعديلات خاصة للمستندات الكبيرة
  if (totalSize > 10) { // أكبر من 10 ميجابايت
    options.useProgressiveLoading = true;
    options.estimatedTime *= 1.5;
    options.estimatedBatteryUsage *= 1.3;
  }

  // تحذير للبطارية المنخفضة
  if (capabilities.batteryLevel && capabilities.batteryLevel < 30) {
    options.mode = 'cloud';
    options.showPerformanceWarning = true;
  }

  return options;
}

/**
 * رسائل تحذيرية للمستخدم
 */
export function getPerformanceWarnings(
  capabilities: DeviceCapabilities,
  options: ProcessingOptions
): string[] {
  const warnings: string[] = [];

  if (capabilities.isLowEnd) {
    warnings.push('جهازك قد يواجه بطء أثناء معالجة المستندات');
  }

  if (capabilities.batteryLevel && capabilities.batteryLevel < 20) {
    warnings.push('مستوى البطارية منخفض - يُنصح بالشحن قبل المعالجة');
  }

  if (capabilities.connectionSpeed === 'slow' && options.mode === 'cloud') {
    warnings.push('اتصال الإنترنت بطيء - قد يستغرق وقتاً أطول');
  }

  if (options.estimatedBatteryUsage > 10) {
    warnings.push(`المعالجة قد تستهلك حوالي ${options.estimatedBatteryUsage}% من البطارية`);
  }

  if (options.estimatedTime > 60) {
    warnings.push(`المعالجة قد تستغرق حوالي ${Math.round(options.estimatedTime / 60)} دقيقة`);
  }

  return warnings;
}

/**
 * نصائح لتحسين الأداء
 */
export function getPerformanceTips(capabilities: DeviceCapabilities): string[] {
  const tips: string[] = [];

  if (capabilities.isLowEnd) {
    tips.push('أغلق التطبيقات الأخرى لتحسين الأداء');
    tips.push('تأكد من شحن الجهاز بنسبة 50% على الأقل');
  }

  if (capabilities.connectionSpeed === 'slow') {
    tips.push('استخدم شبكة Wi-Fi سريعة إن أمكن');
  }

  tips.push('تأكد من جودة الصور قبل الرفع لتسريع المعالجة');
  tips.push('يمكنك معالجة مستند واحد في كل مرة لتوفير الطاقة');

  return tips;
}

/**
 * مراقب الأداء أثناء المعالجة
 */
export class ProcessingMonitor {
  private startTime: number = 0;
  private initialBattery: number = 0;

  async start() {
    this.startTime = performance.now();
    
    if ('getBattery' in navigator) {
      const battery = await (navigator as any).getBattery();
      this.initialBattery = battery.level * 100;
    }
  }

  async getStats() {
    const currentTime = performance.now();
    const elapsedTime = (currentTime - this.startTime) / 1000; // بالثواني

    let batteryUsed = 0;
    if ('getBattery' in navigator) {
      const battery = await (navigator as any).getBattery();
      const currentBattery = battery.level * 100;
      batteryUsed = this.initialBattery - currentBattery;
    }

    return {
      elapsedTime: Math.round(elapsedTime),
      batteryUsed: Math.max(0, Math.round(batteryUsed)),
      memoryUsage: this.getMemoryUsage()
    };
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return Math.round(memory.usedJSHeapSize / 1024 / 1024); // MB
    }
    return 0;
  }
}
