describe('اختبار شامل لنظام الترجمات باستخدام MCP', () => {
  beforeEach(() => {
    // إعداد MCP servers للاختبار
    cy.intercept('GET', '**/api/translations/**', { fixture: 'translations/translations.json' }).as('translationsAPI')
    cy.intercept('GET', '**/locales/**', { fixture: 'translations/translations.json' }).as('localesAPI')
    
    // زيارة الصفحة الرئيسية
    cy.visit('/')
    cy.waitForLoadingToFinish()
  })

  context('🌍 اختبار تبديل اللغة الأساسي', () => {
    it('يجب أن تعرض الصفحة باللغة العربية افتراضياً', () => {
      // التحقق من اللغة الافتراضية
      cy.url().should('include', '/ar/')
      
      // التحقق من وجود النصوص العربية
      cy.get('body').should('contain.text', 'مِخْلاة')
      cy.get('body').should('contain.text', 'الرئيسية')
      
      // التحقق من اتجاه النص
      cy.get('html').should('have.attr', 'dir', 'rtl')
      cy.get('html').should('have.attr', 'lang', 'ar')
    })

    it('يجب أن يعمل تبديل اللغة للإنجليزية', () => {
      // النقر على زر تبديل اللغة
      cy.get('[data-testid="language-switcher"]').click()
      
      // انتظار التحميل
      cy.wait(2000)
      
      // التحقق من التبديل للإنجليزية
      cy.url().should('include', '/en/')
      cy.get('body').should('contain.text', 'Mikhla')
      cy.get('body').should('contain.text', 'Home')
      
      // التحقق من اتجاه النص
      cy.get('html').should('have.attr', 'dir', 'ltr')
      cy.get('html').should('have.attr', 'lang', 'en')
    })

    it('يجب أن يحتفظ بالمسار عند تبديل اللغة', () => {
      // الانتقال لصفحة تسجيل الدخول
      cy.visit('/ar/login')
      cy.waitForLoadingToFinish()
      
      // تبديل اللغة
      cy.get('[data-testid="language-switcher"]').click()
      cy.wait(2000)
      
      // التحقق من الاحتفاظ بالمسار
      cy.url().should('include', '/en/login')
    })
  })

  context('🔍 اختبار تحميل الترجمات عبر MCP', () => {
    it('يجب أن تحمل الترجمات من MCP servers بنجاح', () => {
      // التحقق من استدعاء API الترجمات
      cy.wait('@translationsAPI')
      
      // التحقق من البيانات المحملة
      cy.get('@translationsAPI').should((interception) => {
        expect(interception.response?.statusCode).to.eq(200)
        expect(interception.response?.body).to.have.property('ar')
        expect(interception.response?.body).to.have.property('en')
      })
    })

    it('يجب أن تتعامل مع أخطاء تحميل الترجمات', () => {
      // محاكاة خطأ في تحميل الترجمات
      cy.intercept('GET', '**/api/translations/**', { statusCode: 500 }).as('translationsError')
      
      // إعادة تحميل الصفحة
      cy.reload()
      
      // التحقق من التعامل مع الخطأ
      cy.wait('@translationsError')
      
      // يجب أن تعرض fallback أو رسالة خطأ
      cy.get('body').should('be.visible')
    })
  })

  context('📝 اختبار صحة الترجمات', () => {
    it('يجب ألا تحتوي الصفحة على مفاتيح ترجمة غير محلولة', () => {
      // التحقق من عدم وجود مفاتيح غير محلولة في العربية
      cy.visit('/ar/')
      cy.waitForLoadingToFinish()
      
      cy.get('body').should('not.contain.text', 'loginTitle')
      cy.get('body').should('not.contain.text', 'emailAddress')
      cy.get('body').should('not.contain.text', 'password')
      
      // التحقق من عدم وجود مفاتيح غير محلولة في الإنجليزية
      cy.visit('/en/')
      cy.waitForLoadingToFinish()
      
      cy.get('body').should('not.contain.text', 'loginTitle')
      cy.get('body').should('not.contain.text', 'emailAddress')
      cy.get('body').should('not.contain.text', 'password')
    })

    it('يجب أن تعرض النصوص الصحيحة لكل لغة', () => {
      // اختبار النصوص العربية
      cy.visit('/ar/login')
      cy.waitForLoadingToFinish()
      
      cy.contains('تسجيل الدخول إلى حسابك').should('be.visible')
      cy.contains('البريد الإلكتروني').should('be.visible')
      cy.contains('كلمة المرور').should('be.visible')
      cy.contains('تذكرني').should('be.visible')
      
      // اختبار النصوص الإنجليزية
      cy.visit('/en/login')
      cy.waitForLoadingToFinish()
      
      cy.contains('Login to your account').should('be.visible')
      cy.contains('Email Address').should('be.visible')
      cy.contains('Password').should('be.visible')
      cy.contains('Remember me').should('be.visible')
    })
  })

  context('🔄 اختبار ديناميكية الترجمات', () => {
    it('يجب أن تتحدث الترجمات فوراً عند تبديل اللغة', () => {
      // بدء بالعربية
      cy.visit('/ar/login')
      cy.waitForLoadingToFinish()
      
      // التحقق من النص العربي
      cy.contains('تسجيل الدخول').should('be.visible')
      
      // تبديل للإنجليزية
      cy.get('[data-testid="language-switcher"]').click()
      cy.wait(2000)
      
      // التحقق من تحديث النص فوراً
      cy.contains('Login').should('be.visible')
      cy.contains('تسجيل الدخول').should('not.exist')
    })

    it('يجب أن تعمل الترجمات مع المعاملات', () => {
      // اختبار ترجمة مع معاملات
      cy.visit('/ar/dashboard')
      cy.waitForLoadingToFinish()
      
      // البحث عن نص يحتوي على معامل
      cy.get('body').then(($body) => {
        const welcomeText = $body.text()
        if (welcomeText.includes('مرحباً')) {
          // التحقق من وجود اسم المستخدم في الترجمة
          expect(welcomeText).to.match(/مرحباً.*!/)
        }
      })
    })
  })

  context('💾 اختبار حفظ تفضيلات اللغة', () => {
    it('يجب أن تحفظ اللغة المختارة في localStorage', () => {
      // تبديل للإنجليزية
      cy.get('[data-testid="language-switcher"]').click()
      cy.wait(2000)
      
      // التحقق من حفظ التفضيل
      cy.window().then((win) => {
        const savedLanguage = win.localStorage.getItem('preferred-language')
        expect(savedLanguage).to.eq('en')
      })
    })

    it('يجب أن تستخدم اللغة المحفوظة عند إعادة التحميل', () => {
      // تعيين لغة محفوظة
      cy.window().then((win) => {
        win.localStorage.setItem('preferred-language', 'en')
      })
      
      // إعادة تحميل الصفحة
      cy.reload()
      cy.waitForLoadingToFinish()
      
      // التحقق من استخدام اللغة المحفوظة
      cy.url().should('include', '/en/')
    })
  })

  context('🎯 اختبار الترجمات في مكونات محددة', () => {
    it('يجب أن تعمل الترجمات في نموذج تسجيل الدخول', () => {
      cy.visit('/ar/login')
      cy.waitForLoadingToFinish()
      
      // التحقق من ترجمة العناصر
      cy.get('input[type="email"]').should('have.attr', 'placeholder').and('include', 'البريد')
      cy.get('input[type="password"]').should('have.attr', 'placeholder').and('include', 'كلمة المرور')
      cy.get('button[type="submit"]').should('contain.text', 'تسجيل الدخول')
      
      // تبديل للإنجليزية
      cy.get('[data-testid="language-switcher"]').click()
      cy.wait(2000)
      
      // التحقق من الترجمة الإنجليزية
      cy.get('input[type="email"]').should('have.attr', 'placeholder').and('include', 'email')
      cy.get('input[type="password"]').should('have.attr', 'placeholder').and('include', 'password')
      cy.get('button[type="submit"]').should('contain.text', 'Login')
    })

    it('يجب أن تعمل الترجمات في رسائل الخطأ', () => {
      cy.visit('/ar/login')
      cy.waitForLoadingToFinish()
      
      // محاولة تسجيل دخول خاطئة
      cy.get('input[type="email"]').type('<EMAIL>')
      cy.get('input[type="password"]').type('wrongpassword')
      cy.get('button[type="submit"]').click()
      
      // التحقق من رسالة الخطأ بالعربية
      cy.contains('البريد الإلكتروني أو كلمة المرور غير صحيحة').should('be.visible')
    })
  })

  context('🔧 اختبار الأداء والتحسين', () => {
    it('يجب أن تحمل الترجمات بسرعة', () => {
      const startTime = Date.now()
      
      cy.visit('/ar/')
      cy.waitForLoadingToFinish()
      
      cy.then(() => {
        const loadTime = Date.now() - startTime
        expect(loadTime).to.be.lessThan(3000) // أقل من 3 ثوان
      })
    })

    it('يجب أن تستخدم cache للترجمات', () => {
      // زيارة الصفحة الأولى
      cy.visit('/ar/')
      cy.wait('@translationsAPI')
      
      // الانتقال لصفحة أخرى
      cy.visit('/ar/login')
      
      // التحقق من عدم إعادة تحميل الترجمات
      cy.get('@translationsAPI.all').should('have.length', 1)
    })
  })

  context('♿ اختبار إمكانية الوصول مع الترجمات', () => {
    it('يجب أن تحتوي العناصر على تسميات مترجمة', () => {
      cy.visit('/ar/login')
      cy.waitForLoadingToFinish()
      
      // التحقق من وجود aria-label مترجمة
      cy.get('input[type="email"]').should('have.attr', 'aria-label')
      cy.get('input[type="password"]').should('have.attr', 'aria-label')
      cy.get('button[type="submit"]').should('have.attr', 'aria-label')
    })

    it('يجب أن تعمل قارئات الشاشة مع النصوص المترجمة', () => {
      cy.visit('/ar/login')
      cy.waitForLoadingToFinish()
      
      // التحقق من وجود نصوص يمكن قراءتها
      cy.get('h1, h2, h3').should('have.length.greaterThan', 0)
      cy.get('label').should('have.length.greaterThan', 0)
    })
  })

  afterEach(() => {
    // تنظيف البيانات
    cy.clearLocalStorage()
    cy.clearCookies()
  })
})
