/// <reference types="cypress" />

/**
 * اختبارات نظام الولاء
 * تختبر جميع وظائف نقاط الولاء والمكافآت
 */

describe('⭐ نظام الولاء والمكافآت', () => {
  beforeEach(() => {
    // إعداد البيانات الوهمية
    cy.mockLogin('merchant')
    cy.mockFirebaseAuth()
    cy.mockLoyaltyProgram()
    
    // زيارة صفحة نظام الولاء
    cy.visitWithLocale('/merchant/loyalty')
    cy.waitForLoadingToFinish()
  })

  afterEach(() => {
    cy.mockLogout()
  })

  describe('📋 لوحة تحكم نظام الولاء', () => {
    it('يجب أن تعرض لوحة تحكم نظام الولاء بشكل صحيح', () => {
      // التحقق من وجود العناصر الأساسية
      cy.get('[data-testid="loyalty-dashboard"]').should('be.visible')
      cy.shouldContainArabicText('نظام الولاء والمكافآت')
      cy.shouldContainArabicText('إعدادات البرنامج')
      cy.shouldContainArabicText('العملاء المشتركون')
      cy.shouldContainArabicText('المكافآت المتاحة')
      
      // التحقق من وجود الإحصائيات
      cy.get('[data-testid="total-members"]').should('be.visible')
      cy.get('[data-testid="points-issued"]').should('be.visible')
      cy.get('[data-testid="points-redeemed"]').should('be.visible')
      cy.get('[data-testid="program-engagement"]').should('be.visible')
    })

    it('يجب أن تعرض الرسوم البيانية للتحليلات', () => {
      // التحقق من وجود الرسوم البيانية
      cy.get('[data-testid="points-trends-chart"]').should('be.visible')
      cy.get('[data-testid="tier-distribution-chart"]').should('be.visible')
      cy.get('[data-testid="redemption-patterns-chart"]').should('be.visible')
      cy.get('[data-testid="engagement-metrics-chart"]').should('be.visible')
    })

    it('يجب أن تعرض أحدث المعاملات', () => {
      // التحقق من قائمة المعاملات الأحدث
      cy.get('[data-testid="recent-transactions"]').should('be.visible')
      cy.shouldContainArabicText('أحدث معاملات النقاط')
      
      // التحقق من وجود بيانات المعاملات
      cy.get('[data-testid="transaction-item"]').should('have.length.at.least', 1)
      cy.get('[data-testid="customer-name"]').should('be.visible')
      cy.get('[data-testid="transaction-type"]').should('be.visible')
      cy.get('[data-testid="points-amount"]').should('be.visible')
    })
  })

  describe('⚙️ إعدادات برنامج الولاء', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة الإعدادات
      cy.get('[data-testid="program-settings"]').click()
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض إعدادات البرنامج', () => {
      // التحقق من وجود إعدادات البرنامج
      cy.get('[data-testid="loyalty-settings"]').should('be.visible')
      cy.shouldContainArabicText('إعدادات برنامج الولاء')
      
      // التحقق من الحقول الأساسية
      cy.get('[data-testid="program-name"]').should('be.visible')
      cy.get('[data-testid="points-per-riyal"]').should('be.visible')
      cy.get('[data-testid="minimum-redemption"]').should('be.visible')
      cy.get('[data-testid="points-expiry"]').should('be.visible')
    })

    it('يجب أن يحدث إعدادات البرنامج', () => {
      // تحديث الإعدادات
      cy.fillForm({
        'program-name': 'برنامج الولاء المميز',
        'points-per-riyal': '2',
        'minimum-redemption': '100',
        'points-expiry': '365'
      })
      
      // حفظ التغييرات
      cy.get('[data-testid="save-settings"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث إعدادات البرنامج بنجاح')
    })

    it('يجب أن يضيف مستوى ولاء جديد', () => {
      // إضافة مستوى جديد
      cy.get('[data-testid="add-tier"]').click()
      
      // ملء بيانات المستوى
      cy.fillForm({
        'tier-name': 'البلاتيني',
        'tier-threshold': '5000',
        'tier-multiplier': '3',
        'tier-benefits': 'خصم 15% + شحن مجاني + دعم أولوية'
      })
      
      // حفظ المستوى
      cy.get('[data-testid="save-tier"]').click()
      
      // التحقق من إضافة المستوى
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة المستوى بنجاح')
    })

    it('يجب أن يضيف مكافأة جديدة', () => {
      // إضافة مكافأة جديدة
      cy.get('[data-testid="add-reward"]').click()
      
      // ملء بيانات المكافأة
      cy.fillForm({
        'reward-name': 'خصم 10%',
        'reward-description': 'خصم 10% على الطلب التالي',
        'reward-cost': '500',
        'reward-type': 'discount'
      })
      
      // تحديد قيمة المكافأة
      cy.get('[data-testid="reward-value"]').type('10')
      
      // حفظ المكافأة
      cy.get('[data-testid="save-reward"]').click()
      
      // التحقق من إضافة المكافأة
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة المكافأة بنجاح')
    })
  })

  describe('👥 إدارة أعضاء البرنامج', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة الأعضاء
      cy.get('[data-testid="program-members"]').click()
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض قائمة الأعضاء', () => {
      // التحقق من وجود جدول الأعضاء
      cy.get('[data-testid="members-table"]').should('be.visible')
      
      // التحقق من أعمدة الجدول
      cy.shouldContainArabicText('العضو')
      cy.shouldContainArabicText('المستوى')
      cy.shouldContainArabicText('النقاط المتاحة')
      cy.shouldContainArabicText('إجمالي النقاط')
      cy.shouldContainArabicText('تاريخ الانضمام')
      cy.shouldContainArabicText('آخر نشاط')
    })

    it('يجب أن يبحث في الأعضاء', () => {
      // البحث بالاسم
      cy.get('[data-testid="member-search"]').type('أحمد')
      cy.get('[data-testid="search-btn"]').click()
      
      // التحقق من نتائج البحث
      cy.get('[data-testid="member-row"]').should('contain.text', 'أحمد')
    })

    it('يجب أن يفلتر الأعضاء حسب المستوى', () => {
      // فلترة حسب المستوى
      cy.get('[data-testid="tier-filter"]').select('gold')
      
      // التحقق من الفلترة
      cy.get('[data-testid="member-row"]').each(($row) => {
        cy.wrap($row).find('[data-testid="member-tier"]')
          .should('contain.text', 'ذهبي')
      })
    })

    it('يجب أن يعرض تفاصيل العضو', () => {
      // النقر على عضو
      cy.get('[data-testid="member-row"]').first().click()
      
      // التحقق من فتح صفحة تفاصيل العضو
      cy.get('[data-testid="member-profile"]').should('be.visible')
      cy.shouldContainArabicText('ملف العضو')
      
      // التحقق من وجود المعلومات
      cy.get('[data-testid="member-info"]').should('be.visible')
      cy.get('[data-testid="points-balance"]').should('be.visible')
      cy.get('[data-testid="points-history"]').should('be.visible')
      cy.get('[data-testid="redemption-history"]').should('be.visible')
    })

    it('يجب أن يضيف نقاط يدوياً للعضو', () => {
      // فتح ملف العضو
      cy.get('[data-testid="member-row"]').first().click()
      
      // إضافة نقاط
      cy.get('[data-testid="add-points-btn"]').click()
      cy.fillForm({
        'points-amount': '100',
        'points-reason': 'مكافأة خاصة للعميل المميز'
      })
      
      // تأكيد الإضافة
      cy.get('[data-testid="confirm-add-points"]').click()
      
      // التحقق من إضافة النقاط
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة النقاط بنجاح')
    })

    it('يجب أن يخصم نقاط من العضو', () => {
      // فتح ملف العضو
      cy.get('[data-testid="member-row"]').first().click()
      
      // خصم نقاط
      cy.get('[data-testid="deduct-points-btn"]').click()
      cy.fillForm({
        'points-amount': '50',
        'deduction-reason': 'تصحيح خطأ في النقاط'
      })
      
      // تأكيد الخصم
      cy.get('[data-testid="confirm-deduct-points"]').click()
      
      // التحقق من خصم النقاط
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم خصم النقاط بنجاح')
    })
  })

  describe('🎁 إدارة المكافآت', () => {
    beforeEach(() => {
      // الانتقال إلى صفحة المكافآت
      cy.get('[data-testid="rewards-management"]').click()
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض قائمة المكافآت', () => {
      // التحقق من وجود جدول المكافآت
      cy.get('[data-testid="rewards-table"]').should('be.visible')
      
      // التحقق من أعمدة الجدول
      cy.shouldContainArabicText('المكافأة')
      cy.shouldContainArabicText('التكلفة بالنقاط')
      cy.shouldContainArabicText('النوع')
      cy.shouldContainArabicText('عدد الاستبدالات')
      cy.shouldContainArabicText('الحالة')
    })

    it('يجب أن يحرر مكافأة موجودة', () => {
      // فتح نموذج التحرير
      cy.get('[data-testid="reward-row"]').first().within(() => {
        cy.get('[data-testid="edit-reward"]').click()
      })
      
      // تعديل المكافأة
      cy.get('[data-testid="reward-cost"]').clear().type('600')
      
      // حفظ التغييرات
      cy.get('[data-testid="save-changes"]').click()
      
      // التحقق من نجاح التحديث
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم تحديث المكافأة بنجاح')
    })

    it('يجب أن يوقف/يفعل مكافأة', () => {
      // إيقاف المكافأة
      cy.get('[data-testid="reward-row"]').first().within(() => {
        cy.get('[data-testid="toggle-reward"]').click()
      })
      
      // تأكيد الإيقاف
      cy.get('[data-testid="confirm-toggle"]').click()
      
      // التحقق من تغيير الحالة
      cy.get('[data-testid="reward-status"]').should('contain.text', 'معطل')
    })

    it('يجب أن يعرض تفاصيل استبدال المكافأة', () => {
      // عرض تفاصيل الاستبدال
      cy.get('[data-testid="reward-row"]').first().within(() => {
        cy.get('[data-testid="view-redemptions"]').click()
      })
      
      // التحقق من فتح نافذة التفاصيل
      cy.get('[data-testid="redemption-details-modal"]').should('be.visible')
      cy.shouldContainArabicText('تفاصيل استبدال المكافأة')
      
      // التحقق من وجود بيانات الاستبدال
      cy.get('[data-testid="redemption-stats"]').should('be.visible')
      cy.get('[data-testid="redemption-history"]').should('be.visible')
    })
  })

  describe('🛒 استبدال المكافآت (من جانب العميل)', () => {
    beforeEach(() => {
      // تسجيل دخول كعميل
      cy.mockLogout()
      cy.mockLogin('customer')
      
      // الانتقال إلى صفحة الولاء
      cy.visitWithLocale('/loyalty')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض رصيد النقاط للعميل', () => {
      // التحقق من عرض رصيد النقاط
      cy.get('[data-testid="points-balance"]').should('be.visible')
      cy.get('[data-testid="available-points"]').should('contain.text', '1000')
      cy.get('[data-testid="current-tier"]').should('contain.text', 'ذهبي')
      cy.get('[data-testid="next-tier-progress"]').should('be.visible')
    })

    it('يجب أن تعرض المكافآت المتاحة', () => {
      // التحقق من قائمة المكافآت
      cy.get('[data-testid="available-rewards"]').should('be.visible')
      cy.shouldContainArabicText('المكافآت المتاحة')
      
      // التحقق من وجود مكافآت
      cy.get('[data-testid="reward-card"]').should('have.length.at.least', 1)
      cy.get('[data-testid="reward-name"]').should('be.visible')
      cy.get('[data-testid="reward-cost"]').should('be.visible')
      cy.get('[data-testid="redeem-btn"]').should('be.visible')
    })

    it('يجب أن يستبدل مكافأة بنجاح', () => {
      // استبدال مكافأة
      cy.get('[data-testid="reward-card"]').first().within(() => {
        cy.get('[data-testid="redeem-btn"]').click()
      })
      
      // تأكيد الاستبدال
      cy.get('[data-testid="confirm-redemption"]').should('be.visible')
      cy.shouldContainArabicText('هل تريد استبدال هذه المكافأة؟')
      cy.get('[data-testid="confirm-redeem-btn"]').click()
      
      // التحقق من نجاح الاستبدال
      cy.get('[data-testid="success-message"]').should('be.visible')
      cy.shouldContainArabicText('تم استبدال المكافأة بنجاح')
    })

    it('يجب أن يرفض استبدال مكافأة بنقاط غير كافية', () => {
      // محاولة استبدال مكافأة تكلف أكثر من النقاط المتاحة
      cy.get('[data-testid="expensive-reward"]').within(() => {
        cy.get('[data-testid="redeem-btn"]').should('be.disabled')
      })
      
      // التحقق من رسالة النقاط غير الكافية
      cy.get('[data-testid="insufficient-points"]').should('be.visible')
      cy.shouldContainArabicText('نقاط غير كافية')
    })

    it('يجب أن يعرض تاريخ النقاط', () => {
      // عرض تاريخ النقاط
      cy.get('[data-testid="points-history-tab"]').click()
      
      // التحقق من عرض التاريخ
      cy.get('[data-testid="points-history"]').should('be.visible')
      cy.get('[data-testid="transaction-item"]').should('have.length.at.least', 1)
      
      // التحقق من تفاصيل المعاملة
      cy.get('[data-testid="transaction-date"]').should('be.visible')
      cy.get('[data-testid="transaction-type"]').should('be.visible')
      cy.get('[data-testid="transaction-points"]').should('be.visible')
      cy.get('[data-testid="transaction-description"]').should('be.visible')
    })

    it('يجب أن يعرض تاريخ الاستبدالات', () => {
      // عرض تاريخ الاستبدالات
      cy.get('[data-testid="redemption-history-tab"]').click()
      
      // التحقق من عرض التاريخ
      cy.get('[data-testid="redemption-history"]').should('be.visible')
      cy.get('[data-testid="redemption-item"]').should('have.length.at.least', 1)
      
      // التحقق من تفاصيل الاستبدال
      cy.get('[data-testid="redemption-date"]').should('be.visible')
      cy.get('[data-testid="redemption-reward"]').should('be.visible')
      cy.get('[data-testid="redemption-points"]').should('be.visible')
      cy.get('[data-testid="redemption-status"]').should('be.visible')
    })
  })

  describe('📊 تحليلات وتقارير الولاء', () => {
    beforeEach(() => {
      // العودة لتسجيل دخول التاجر
      cy.mockLogout()
      cy.mockLogin('merchant')
      cy.visitWithLocale('/merchant/loyalty')
      cy.waitForLoadingToFinish()
    })

    it('يجب أن تعرض تحليلات شاملة لبرنامج الولاء', () => {
      // الانتقال إلى صفحة التحليلات
      cy.get('[data-testid="loyalty-analytics"]').click()
      
      // التحقق من وجود المقاييس
      cy.get('[data-testid="program-roi"]').should('be.visible')
      cy.get('[data-testid="member-retention"]').should('be.visible')
      cy.get('[data-testid="average-points-per-member"]').should('be.visible')
      cy.get('[data-testid="redemption-rate"]').should('be.visible')
      
      // التحقق من الرسوم البيانية
      cy.get('[data-testid="member-growth-chart"]').should('be.visible')
      cy.get('[data-testid="points-flow-chart"]').should('be.visible')
      cy.get('[data-testid="tier-performance-chart"]').should('be.visible')
    })

    it('يجب أن يفلتر التحليلات حسب الفترة الزمنية', () => {
      cy.get('[data-testid="loyalty-analytics"]').click()
      
      // تغيير الفترة الزمنية
      cy.get('[data-testid="date-range-filter"]').select('last_quarter')
      
      // التحقق من تحديث البيانات
      cy.get('[data-testid="analytics-loading"]').should('be.visible')
      cy.get('[data-testid="analytics-loading"]').should('not.exist')
      cy.get('[data-testid="program-roi"]').should('be.visible')
    })

    it('يجب أن يصدر تقرير برنامج الولاء', () => {
      cy.get('[data-testid="loyalty-analytics"]').click()
      
      // تصدير التقرير
      cy.get('[data-testid="export-report"]').click()
      
      // اختيار نوع التصدير
      cy.get('[data-testid="export-format"]').select('excel')
      cy.get('[data-testid="confirm-export"]').click()
      
      // التحقق من بدء التصدير
      cy.get('[data-testid="export-status"]').should('contain.text', 'جاري التصدير')
    })

    it('يجب أن يعرض تقرير أداء المكافآت', () => {
      cy.get('[data-testid="loyalty-analytics"]').click()
      
      // عرض تقرير المكافآت
      cy.get('[data-testid="rewards-performance"]').click()
      
      // التحقق من عرض التقرير
      cy.get('[data-testid="rewards-report"]').should('be.visible')
      cy.get('[data-testid="most-popular-rewards"]').should('be.visible')
      cy.get('[data-testid="least-popular-rewards"]').should('be.visible')
      cy.get('[data-testid="redemption-trends"]').should('be.visible')
    })
  })

  describe('🔔 إشعارات برنامج الولاء', () => {
    it('يجب أن يرسل إشعار عند كسب نقاط', () => {
      // محاكاة كسب نقاط من طلب
      cy.window().then((win) => {
        win.localStorage.setItem('mockOrderComplete', JSON.stringify({
          orderId: 'order-123',
          pointsEarned: 50
        }))
      })
      
      // التحقق من ظهور إشعار النقاط
      cy.get('[data-testid="points-notification"]').should('be.visible')
      cy.shouldContainArabicText('تم إضافة 50 نقطة إلى رصيدك')
    })

    it('يجب أن يرسل إشعار عند الترقية لمستوى جديد', () => {
      // محاكاة ترقية المستوى
      cy.window().then((win) => {
        win.localStorage.setItem('mockTierUpgrade', JSON.stringify({
          newTier: 'platinum',
          tierName: 'البلاتيني'
        }))
      })
      
      // التحقق من ظهور إشعار الترقية
      cy.get('[data-testid="tier-upgrade-notification"]').should('be.visible')
      cy.shouldContainArabicText('تهانينا! تم ترقيتك إلى مستوى البلاتيني')
    })
  })
})
