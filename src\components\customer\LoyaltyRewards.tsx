'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Gift, 
  Star, 
  Percent, 
  Truck, 
  CreditCard,
  Sparkles,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { loyaltyService } from '@/services/loyaltyService';
import { LoyaltyProgram, LoyaltyReward, CustomerLoyalty, RewardRedemption } from '@/types/loyalty';
import { useLocale } from '@/hooks/use-locale';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface LoyaltyRewardsProps {
  customerId: string;
  merchantId: string;
}

export function LoyaltyRewards({ customerId, merchantId }: LoyaltyRewardsProps) {
  const { t } = useLocale();
  const [program, setProgram] = useState<LoyaltyProgram | null>(null);
  const [customerLoyalty, setCustomerLoyalty] = useState<CustomerLoyalty | null>(null);
  const [loading, setLoading] = useState(true);
  const [redeeming, setRedeeming] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, [customerId, merchantId]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [programData, loyaltyData] = await Promise.all([
        loyaltyService.getMerchantLoyaltyProgram(merchantId),
        loyaltyService.getCustomerLoyalty(customerId, merchantId)
      ]);
      
      setProgram(programData);
      setCustomerLoyalty(loyaltyData);
    } catch (error) {
      console.error('Error loading rewards data:', error);
      toast.error('فشل في تحميل بيانات المكافآت');
    } finally {
      setLoading(false);
    }
  };

  const getRewardIcon = (type: string) => {
    switch (type) {
      case 'discount':
        return <Percent className="h-5 w-5" />;
      case 'free_shipping':
        return <Truck className="h-5 w-5" />;
      case 'gift_card':
        return <CreditCard className="h-5 w-5" />;
      case 'experience':
        return <Sparkles className="h-5 w-5" />;
      default:
        return <Gift className="h-5 w-5" />;
    }
  };

  const getRewardTypeLabel = (type: string) => {
    switch (type) {
      case 'discount':
        return 'خصم';
      case 'free_product':
        return 'منتج مجاني';
      case 'free_shipping':
        return 'شحن مجاني';
      case 'gift_card':
        return 'بطاقة هدية';
      case 'experience':
        return 'تجربة خاصة';
      default:
        return type;
    }
  };

  const formatRewardValue = (reward: LoyaltyReward) => {
    switch (reward.type) {
      case 'discount':
        return `${reward.value}% خصم`;
      case 'gift_card':
        return `${reward.value} ريال`;
      case 'free_shipping':
        return 'شحن مجاني';
      default:
        return reward.name;
    }
  };

  const canRedeemReward = (reward: LoyaltyReward) => {
    if (!customerLoyalty) return false;
    
    // التحقق من النقاط المتاحة
    if (customerLoyalty.availablePoints < reward.pointsCost) {
      return false;
    }

    // التحقق من قيود المستوى
    if (reward.tierRestrictions && !reward.tierRestrictions.includes(customerLoyalty.currentTier)) {
      return false;
    }

    // التحقق من الكمية المتاحة
    if (reward.availableQuantity !== undefined && reward.usedQuantity >= reward.availableQuantity) {
      return false;
    }

    // التحقق من تاريخ الصلاحية
    const now = new Date();
    if (now < reward.validFrom.toDate() || now > reward.validUntil.toDate()) {
      return false;
    }

    return true;
  };

  const getRewardStatus = (reward: LoyaltyReward) => {
    if (!customerLoyalty) return 'unavailable';
    
    if (!reward.isActive) return 'inactive';
    
    const now = new Date();
    if (now < reward.validFrom.toDate()) return 'not_started';
    if (now > reward.validUntil.toDate()) return 'expired';
    
    if (reward.availableQuantity !== undefined && reward.usedQuantity >= reward.availableQuantity) {
      return 'sold_out';
    }
    
    if (customerLoyalty.availablePoints < reward.pointsCost) return 'insufficient_points';
    
    if (reward.tierRestrictions && !reward.tierRestrictions.includes(customerLoyalty.currentTier)) {
      return 'tier_restricted';
    }
    
    return 'available';
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge variant="default" className="bg-green-500">متاح</Badge>;
      case 'insufficient_points':
        return <Badge variant="secondary">نقاط غير كافية</Badge>;
      case 'tier_restricted':
        return <Badge variant="outline">مقيد بالمستوى</Badge>;
      case 'sold_out':
        return <Badge variant="destructive">نفدت الكمية</Badge>;
      case 'expired':
        return <Badge variant="destructive">منتهي الصلاحية</Badge>;
      case 'inactive':
        return <Badge variant="secondary">غير نشط</Badge>;
      default:
        return <Badge variant="secondary">غير متاح</Badge>;
    }
  };

  const handleRedeemReward = async (rewardId: string) => {
    if (!customerLoyalty) return;

    try {
      setRedeeming(rewardId);
      const redemption = await loyaltyService.redeemReward(customerId, merchantId, rewardId);
      
      toast.success(`تم استبدال المكافأة بنجاح! كود الاستبدال: ${redemption.redemptionCode}`);
      
      // إعادة تحميل البيانات
      await loadData();
    } catch (error: any) {
      toast.error(error.message || 'فشل في استبدال المكافأة');
    } finally {
      setRedeeming(null);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!program || !customerLoyalty) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">
            لا توجد مكافآت متاحة
          </h3>
          <p className="text-gray-500">
            انضم لبرنامج الولاء للحصول على مكافآت رائعة
          </p>
        </CardContent>
      </Card>
    );
  }

  const availableRewards = program.rewards.filter(reward => reward.isActive);

  return (
    <div className="space-y-6">
      {/* معلومات النقاط */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-blue-900">نقاطك المتاحة</h3>
              <p className="text-blue-700">يمكنك استبدالها بالمكافآت أدناه</p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-blue-600">
                {customerLoyalty.availablePoints.toLocaleString()}
              </div>
              <div className="text-sm text-blue-500">نقطة</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* قائمة المكافآت */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {availableRewards.map((reward) => {
          const status = getRewardStatus(reward);
          const canRedeem = canRedeemReward(reward);
          
          return (
            <Card key={reward.id} className={`${canRedeem ? 'border-green-200' : 'border-gray-200'}`}>
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`p-2 rounded-full ${canRedeem ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
                      {getRewardIcon(reward.type)}
                    </div>
                    <div>
                      <CardTitle className="text-sm">{reward.name}</CardTitle>
                      <p className="text-xs text-gray-500">{getRewardTypeLabel(reward.type)}</p>
                    </div>
                  </div>
                  {getStatusBadge(status)}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* وصف المكافأة */}
                <p className="text-sm text-gray-600">{reward.description}</p>
                
                {/* قيمة المكافأة */}
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-lg font-bold text-gray-900">
                    {formatRewardValue(reward)}
                  </div>
                </div>

                {/* تكلفة النقاط */}
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">التكلفة:</span>
                  <span className="font-semibold text-blue-600">
                    {reward.pointsCost.toLocaleString()} نقطة
                  </span>
                </div>

                {/* معلومات إضافية */}
                <div className="space-y-2 text-xs text-gray-500">
                  {reward.availableQuantity !== undefined && (
                    <div className="flex items-center justify-between">
                      <span>الكمية المتاحة:</span>
                      <span>{reward.availableQuantity - reward.usedQuantity}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>صالح حتى: {format(reward.validUntil.toDate(), 'dd/MM/yyyy', { locale: ar })}</span>
                  </div>

                  {reward.tierRestrictions && (
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3" />
                      <span>للمستويات: {reward.tierRestrictions.join(', ')}</span>
                    </div>
                  )}
                </div>

                {/* زر الاستبدال */}
                <Button
                  onClick={() => handleRedeemReward(reward.id)}
                  disabled={!canRedeem || redeeming === reward.id}
                  className="w-full"
                  variant={canRedeem ? 'default' : 'secondary'}
                >
                  {redeeming === reward.id ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      جاري الاستبدال...
                    </div>
                  ) : canRedeem ? (
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      استبدال الآن
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      غير متاح
                    </div>
                  )}
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {availableRewards.length === 0 && (
        <Card>
          <CardContent className="p-6 text-center">
            <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">
              لا توجد مكافآت متاحة حالياً
            </h3>
            <p className="text-gray-500">
              تابع التسوق لكسب المزيد من النقاط والحصول على مكافآت جديدة
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
