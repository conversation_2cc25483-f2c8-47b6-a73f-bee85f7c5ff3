/**
 * خدمة الإشعارات المتقدمة - نظام ذكي للإشعارات المخصصة
 * يدعم الإشعارات المجدولة، التحليلات، والتخصيص حسب السلوك
 */

import { db } from '@/lib/firebase';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs,
  updateDoc,
  doc,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';

interface NotificationTemplate {
  id: string;
  name: string;
  title: string;
  body: string;
  type: string;
  category: 'marketing' | 'transactional' | 'system' | 'behavioral';
  variables: string[];
  isActive: boolean;
  createdAt: Date;
}

interface ScheduledNotification {
  id?: string;
  userId: string;
  templateId: string;
  scheduledFor: Date;
  variables: Record<string, any>;
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  channels: ('push' | 'email' | 'sms' | 'in_app')[];
  createdAt: Date;
}

interface NotificationAnalytics {
  notificationId: string;
  userId: string;
  action: 'sent' | 'delivered' | 'opened' | 'clicked' | 'dismissed';
  timestamp: Date;
  channel: string;
  metadata?: Record<string, any>;
}

interface UserNotificationPreferences {
  userId: string;
  pushEnabled: boolean;
  emailEnabled: boolean;
  smsEnabled: boolean;
  marketingEnabled: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:mm
    endTime: string; // HH:mm
  };
  frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
  categories: Record<string, boolean>;
}

interface BehavioralTrigger {
  id: string;
  name: string;
  description: string;
  conditions: {
    event: string;
    operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
    value: any;
  }[];
  templateId: string;
  delay: number; // minutes
  isActive: boolean;
}

class AdvancedNotificationService {
  /**
   * إنشاء قالب إشعار جديد
   */
  static async createNotificationTemplate(template: Omit<NotificationTemplate, 'id' | 'createdAt'>): Promise<string> {
    try {
      const templatesRef = collection(db, 'notification_templates');
      const docRef = await addDoc(templatesRef, {
        ...template,
        createdAt: serverTimestamp()
      });

      console.log('تم إنشاء قالب إشعار جديد:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('خطأ في إنشاء قالب الإشعار:', error);
      throw new Error('فشل في إنشاء قالب الإشعار');
    }
  }

  /**
   * جدولة إشعار للإرسال لاحقاً
   */
  static async scheduleNotification(notification: Omit<ScheduledNotification, 'id' | 'createdAt'>): Promise<string> {
    try {
      // التحقق من تفضيلات المستخدم
      const userPrefs = await this.getUserNotificationPreferences(notification.userId);
      
      if (!this.shouldSendNotification(notification, userPrefs)) {
        throw new Error('الإشعار مرفوض حسب تفضيلات المستخدم');
      }

      const scheduledRef = collection(db, 'scheduled_notifications');
      const docRef = await addDoc(scheduledRef, {
        ...notification,
        createdAt: serverTimestamp(),
        status: 'pending'
      });

      console.log('تم جدولة الإشعار:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('خطأ في جدولة الإشعار:', error);
      throw new Error('فشل في جدولة الإشعار');
    }
  }

  /**
   * إرسال إشعار فوري مخصص
   */
  static async sendPersonalizedNotification(
    userId: string,
    templateId: string,
    variables: Record<string, any>,
    options: {
      priority?: 'low' | 'medium' | 'high' | 'urgent';
      channels?: ('push' | 'email' | 'sms' | 'in_app')[];
      delay?: number; // minutes
    } = {}
  ): Promise<void> {
    try {
      // الحصول على القالب
      const template = await this.getNotificationTemplate(templateId);
      if (!template) {
        throw new Error('قالب الإشعار غير موجود');
      }

      // تخصيص المحتوى
      const personalizedContent = this.personalizeContent(template, variables, userId);

      // الحصول على تفضيلات المستخدم
      const userPrefs = await this.getUserNotificationPreferences(userId);

      // تحديد القنوات المناسبة
      const channels = this.selectOptimalChannels(
        options.channels || ['push', 'in_app'],
        userPrefs,
        options.priority || 'medium'
      );

      // إرسال الإشعار
      for (const channel of channels) {
        await this.sendViaChannel(channel, userId, personalizedContent);
      }

      // تسجيل التحليلات
      await this.logNotificationAnalytics({
        notificationId: `custom-${Date.now()}`,
        userId,
        action: 'sent',
        timestamp: new Date(),
        channel: channels.join(','),
        metadata: { templateId, priority: options.priority }
      });

    } catch (error) {
      console.error('خطأ في إرسال الإشعار المخصص:', error);
      throw new Error('فشل في إرسال الإشعار المخصص');
    }
  }

  /**
   * إنشاء مشغل سلوكي للإشعارات
   */
  static async createBehavioralTrigger(trigger: Omit<BehavioralTrigger, 'id'>): Promise<string> {
    try {
      const triggersRef = collection(db, 'behavioral_triggers');
      const docRef = await addDoc(triggersRef, {
        ...trigger,
        createdAt: serverTimestamp()
      });

      console.log('تم إنشاء مشغل سلوكي:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('خطأ في إنشاء المشغل السلوكي:', error);
      throw new Error('فشل في إنشاء المشغل السلوكي');
    }
  }

  /**
   * معالجة حدث سلوكي وتشغيل الإشعارات المناسبة
   */
  static async processBehavioralEvent(
    userId: string,
    event: string,
    eventData: Record<string, any>
  ): Promise<void> {
    try {
      // الحصول على المشغلات النشطة
      const triggers = await this.getActiveBehavioralTriggers();

      for (const trigger of triggers) {
        if (this.evaluateTriggerConditions(trigger, event, eventData)) {
          // جدولة الإشعار مع التأخير المحدد
          const scheduledFor = new Date(Date.now() + trigger.delay * 60 * 1000);
          
          await this.scheduleNotification({
            userId,
            templateId: trigger.templateId,
            scheduledFor,
            variables: eventData,
            status: 'pending',
            priority: 'medium',
            channels: ['push', 'in_app']
          });

          console.log(`تم تشغيل المشغل السلوكي: ${trigger.name} للمستخدم: ${userId}`);
        }
      }
    } catch (error) {
      console.error('خطأ في معالجة الحدث السلوكي:', error);
    }
  }

  /**
   * تحديث تفضيلات الإشعارات للمستخدم
   */
  static async updateUserNotificationPreferences(
    userId: string,
    preferences: Partial<UserNotificationPreferences>
  ): Promise<void> {
    try {
      const prefsRef = doc(db, 'user_notification_preferences', userId);
      await updateDoc(prefsRef, {
        ...preferences,
        updatedAt: serverTimestamp()
      });

      console.log('تم تحديث تفضيلات الإشعارات للمستخدم:', userId);
    } catch (error) {
      console.error('خطأ في تحديث تفضيلات الإشعارات:', error);
      throw new Error('فشل في تحديث تفضيلات الإشعارات');
    }
  }

  /**
   * الحصول على تحليلات الإشعارات
   */
  static async getNotificationAnalytics(
    userId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalSent: number;
    totalDelivered: number;
    totalOpened: number;
    totalClicked: number;
    openRate: number;
    clickRate: number;
    channelPerformance: Record<string, any>;
  }> {
    try {
      const analyticsRef = collection(db, 'notification_analytics');
      let q = query(analyticsRef);

      if (userId) {
        q = query(q, where('userId', '==', userId));
      }

      if (startDate) {
        q = query(q, where('timestamp', '>=', Timestamp.fromDate(startDate)));
      }

      if (endDate) {
        q = query(q, where('timestamp', '<=', Timestamp.fromDate(endDate)));
      }

      const snapshot = await getDocs(q);
      const analytics = snapshot.docs.map(doc => doc.data() as NotificationAnalytics);

      // حساب الإحصائيات
      const totalSent = analytics.filter(a => a.action === 'sent').length;
      const totalDelivered = analytics.filter(a => a.action === 'delivered').length;
      const totalOpened = analytics.filter(a => a.action === 'opened').length;
      const totalClicked = analytics.filter(a => a.action === 'clicked').length;

      const openRate = totalDelivered > 0 ? (totalOpened / totalDelivered) * 100 : 0;
      const clickRate = totalOpened > 0 ? (totalClicked / totalOpened) * 100 : 0;

      // أداء القنوات
      const channelPerformance: Record<string, any> = {};
      analytics.forEach(a => {
        if (!channelPerformance[a.channel]) {
          channelPerformance[a.channel] = { sent: 0, delivered: 0, opened: 0, clicked: 0 };
        }
        channelPerformance[a.channel][a.action]++;
      });

      return {
        totalSent,
        totalDelivered,
        totalOpened,
        totalClicked,
        openRate: Math.round(openRate * 100) / 100,
        clickRate: Math.round(clickRate * 100) / 100,
        channelPerformance
      };

    } catch (error) {
      console.error('خطأ في الحصول على تحليلات الإشعارات:', error);
      throw new Error('فشل في الحصول على تحليلات الإشعارات');
    }
  }

  // ===== PRIVATE METHODS =====

  private static async getNotificationTemplate(templateId: string): Promise<NotificationTemplate | null> {
    try {
      const templateRef = doc(db, 'notification_templates', templateId);
      const templateSnap = await getDocs(query(collection(db, 'notification_templates'), where('__name__', '==', templateId)));
      
      if (templateSnap.empty) return null;
      
      const data = templateSnap.docs[0].data();
      return {
        id: templateSnap.docs[0].id,
        ...data,
        createdAt: data.createdAt.toDate()
      } as NotificationTemplate;
    } catch (error) {
      console.error('خطأ في الحصول على قالب الإشعار:', error);
      return null;
    }
  }

  private static async getUserNotificationPreferences(userId: string): Promise<UserNotificationPreferences> {
    try {
      const prefsRef = doc(db, 'user_notification_preferences', userId);
      const prefsSnap = await getDocs(query(collection(db, 'user_notification_preferences'), where('userId', '==', userId)));
      
      if (prefsSnap.empty) {
        // إرجاع التفضيلات الافتراضية
        return {
          userId,
          pushEnabled: true,
          emailEnabled: true,
          smsEnabled: false,
          marketingEnabled: true,
          quietHours: { enabled: false, startTime: '22:00', endTime: '08:00' },
          frequency: 'immediate',
          categories: {}
        };
      }
      
      return prefsSnap.docs[0].data() as UserNotificationPreferences;
    } catch (error) {
      console.error('خطأ في الحصول على تفضيلات المستخدم:', error);
      // إرجاع التفضيلات الافتراضية في حالة الخطأ
      return {
        userId,
        pushEnabled: true,
        emailEnabled: true,
        smsEnabled: false,
        marketingEnabled: true,
        quietHours: { enabled: false, startTime: '22:00', endTime: '08:00' },
        frequency: 'immediate',
        categories: {}
      };
    }
  }

  private static shouldSendNotification(
    notification: Omit<ScheduledNotification, 'id' | 'createdAt'>,
    userPrefs: UserNotificationPreferences
  ): boolean {
    // التحقق من الساعات الهادئة
    if (userPrefs.quietHours.enabled) {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      
      if (currentTime >= userPrefs.quietHours.startTime || currentTime <= userPrefs.quietHours.endTime) {
        return false;
      }
    }

    // التحقق من تفضيلات القنوات
    const hasEnabledChannel = notification.channels.some(channel => {
      switch (channel) {
        case 'push': return userPrefs.pushEnabled;
        case 'email': return userPrefs.emailEnabled;
        case 'sms': return userPrefs.smsEnabled;
        default: return true;
      }
    });

    return hasEnabledChannel;
  }

  private static personalizeContent(
    template: NotificationTemplate,
    variables: Record<string, any>,
    userId: string
  ): { title: string; body: string } {
    let title = template.title;
    let body = template.body;

    // استبدال المتغيرات
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      title = title.replace(new RegExp(placeholder, 'g'), String(value));
      body = body.replace(new RegExp(placeholder, 'g'), String(value));
    });

    return { title, body };
  }

  private static selectOptimalChannels(
    requestedChannels: string[],
    userPrefs: UserNotificationPreferences,
    priority: string
  ): string[] {
    const availableChannels = requestedChannels.filter(channel => {
      switch (channel) {
        case 'push': return userPrefs.pushEnabled;
        case 'email': return userPrefs.emailEnabled;
        case 'sms': return userPrefs.smsEnabled;
        case 'in_app': return true; // دائماً متاح
        default: return false;
      }
    });

    // للإشعارات العاجلة، استخدم جميع القنوات المتاحة
    if (priority === 'urgent') {
      return availableChannels;
    }

    // للإشعارات العادية، استخدم قناة واحدة مفضلة
    return availableChannels.slice(0, 1);
  }

  private static async sendViaChannel(
    channel: string,
    userId: string,
    content: { title: string; body: string }
  ): Promise<void> {
    // هنا يتم استدعاء خدمات الإرسال المناسبة
    console.log(`إرسال إشعار عبر ${channel} للمستخدم ${userId}:`, content);
  }

  private static async logNotificationAnalytics(analytics: NotificationAnalytics): Promise<void> {
    try {
      const analyticsRef = collection(db, 'notification_analytics');
      await addDoc(analyticsRef, {
        ...analytics,
        timestamp: serverTimestamp()
      });
    } catch (error) {
      console.error('خطأ في تسجيل تحليلات الإشعار:', error);
    }
  }

  private static async getActiveBehavioralTriggers(): Promise<BehavioralTrigger[]> {
    try {
      const triggersRef = collection(db, 'behavioral_triggers');
      const q = query(triggersRef, where('isActive', '==', true));
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as BehavioralTrigger[];
    } catch (error) {
      console.error('خطأ في الحصول على المشغلات السلوكية:', error);
      return [];
    }
  }

  private static evaluateTriggerConditions(
    trigger: BehavioralTrigger,
    event: string,
    eventData: Record<string, any>
  ): boolean {
    return trigger.conditions.every(condition => {
      if (condition.event !== event) return false;

      const actualValue = eventData[condition.event];
      const expectedValue = condition.value;

      switch (condition.operator) {
        case 'equals':
          return actualValue === expectedValue;
        case 'greater_than':
          return Number(actualValue) > Number(expectedValue);
        case 'less_than':
          return Number(actualValue) < Number(expectedValue);
        case 'contains':
          return String(actualValue).includes(String(expectedValue));
        default:
          return false;
      }
    });
  }
}

export default AdvancedNotificationService;
export type {
  NotificationTemplate,
  ScheduledNotification,
  NotificationAnalytics,
  UserNotificationPreferences,
  BehavioralTrigger
};
