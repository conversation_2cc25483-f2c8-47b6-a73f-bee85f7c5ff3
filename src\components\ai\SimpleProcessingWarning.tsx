"use client";

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Cloud, Smartphone, Info } from 'lucide-react';

interface SimpleProcessingWarningProps {
  onProceed: () => void;
  onCancel: () => void;
  isLowEndDevice: boolean;
}

export default function SimpleProcessingWarning({
  onProceed,
  onCancel,
  isLowEndDevice
}: SimpleProcessingWarningProps) {
  
  if (isLowEndDevice) {
    return (
      <Alert className="max-w-md mx-auto">
        <Cloud className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-3">
            <p className="font-medium">معالجة محسنة لجهازك</p>
            <p className="text-sm">
              سيتم معالجة وثائقك عبر خوادمنا الآمنة لضمان:
            </p>
            <ul className="text-sm space-y-1 mr-4">
              <li>• سرعة أكبر في المعالجة</li>
              <li>• عدم استنزاف بطارية جهازك</li>
              <li>• دقة أعلى في استخراج البيانات</li>
            </ul>
            <div className="flex gap-2 pt-2">
              <Button onClick={onProceed} size="sm" className="flex-1">
                متابعة
              </Button>
              <Button onClick={onCancel} variant="outline" size="sm">
                إلغاء
              </Button>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Alert className="max-w-md mx-auto">
      <Smartphone className="h-4 w-4" />
      <AlertDescription>
        <div className="space-y-3">
          <p className="font-medium">معالجة محلية آمنة</p>
          <p className="text-sm">
            سيتم معالجة وثائقك على جهازك مباشرة لضمان:
          </p>
          <ul className="text-sm space-y-1 mr-4">
            <li>• خصوصية 100% - لا تغادر بياناتك جهازك</li>
            <li>• أمان كامل للمعلومات الحساسة</li>
            <li>• عدم الحاجة لاتصال إنترنت قوي</li>
          </ul>
          <div className="flex gap-2 pt-2">
            <Button onClick={onProceed} size="sm" className="flex-1">
              بدء المعالجة
            </Button>
            <Button onClick={onCancel} variant="outline" size="sm">
              إلغاء
            </Button>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
}
