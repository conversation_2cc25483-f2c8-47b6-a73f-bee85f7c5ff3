// src/services/nerService.ts - خدمة استخلاص الكيانات والمعلومات المتقدمة
import { NERResult } from './huggingFaceAIService';

// نماذج NER المتخصصة
const NER_MODELS = {
  arabic: {
    primary: 'aubmindlab/bert-base-arabertv02',
    secondary: 'CAMeL-Lab/bert-base-arabic-camelbert-mix',
    fallback: 'asafaya/bert-base-arabic'
  },
  english: {
    primary: 'dbmdz/bert-large-cased-finetuned-conll03-english',
    secondary: 'dslim/bert-base-NER',
    fallback: 'microsoft/DialoGPT-medium'
  },
  mixed: {
    primary: 'CAMeL-Lab/bert-base-arabic-camelbert-mix',
    secondary: 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
    fallback: 'aubmindlab/bert-base-arabertv02'
  }
};

// أنماط استخراج البيانات المتقدمة
const EXTRACTION_PATTERNS = {
  arabic: {
    // أسماء الأشخاص والشركات
    personName: [
      /(?:اسم|صاحب|مالك|مدير|رئيس)\s*:?\s*([^\n\r،]+)/gi,
      /(?:السيد|الأستاذ|المهندس|الدكتور|الأستاذة|المهندسة|الدكتورة)\s+([^\n\r،]+)/gi,
      /(?:المدير العام|المدير التنفيذي|الرئيس التنفيذي)\s*:?\s*([^\n\r،]+)/gi
    ],
    businessName: [
      /(?:شركة|مؤسسة|منشأة|مكتب|معهد|مركز)\s+([^\n\r،]+)/gi,
      /(?:اسم المنشأة|اسم الشركة|اسم المؤسسة)\s*:?\s*([^\n\r،]+)/gi,
      /(?:التسمية التجارية|الاسم التجاري)\s*:?\s*([^\n\r،]+)/gi
    ],
    registrationNumber: [
      /(?:رقم السجل|السجل التجاري|رقم التسجيل|رقم الترخيص)\s*:?\s*(\d+)/gi,
      /(?:ر\.ت|ر\.س|ر\.ترخيص)\s*:?\s*(\d+)/gi,
      /(?:Registration No|Reg\. No)\s*:?\s*(\d+)/gi
    ],
    nationalId: [
      /(?:رقم الهوية|الهوية الوطنية|رقم الهوية الوطنية)\s*:?\s*(\d+)/gi,
      /(?:National ID|ID No)\s*:?\s*(\d+)/gi
    ],
    dates: [
      /(?:تاريخ الإصدار|تاريخ التسجيل|صدر في|أصدر في)\s*:?\s*([^\n\r،]+)/gi,
      /(?:تاريخ الانتهاء|ينتهي في|صالح حتى|تاريخ الصلاحية)\s*:?\s*([^\n\r،]+)/gi,
      /(\d{1,2}\/\d{1,2}\/\d{4})/gi,
      /(\d{4}-\d{1,2}-\d{1,2})/gi,
      /(\d{1,2}\s+\w+\s+\d{4})/gi
    ],
    businessActivity: [
      /(?:النشاط|نوع النشاط|النشاط التجاري|المهنة|التخصص)\s*:?\s*([^\n\r،]+)/gi,
      /(?:Activity|Business Activity)\s*:?\s*([^\n\r،]+)/gi
    ],
    address: [
      /(?:العنوان|عنوان|المقر|الموقع)\s*:?\s*([^\n\r،]+)/gi,
      /(?:Address|Location)\s*:?\s*([^\n\r،]+)/gi,
      /(?:ص\.ب|صندوق بريد|P\.O\.Box)\s*:?\s*(\d+)/gi
    ],
    phone: [
      /(?:هاتف|جوال|تلفون|رقم الهاتف|رقم الجوال)\s*:?\s*([\d\s\-\+\(\)]+)/gi,
      /(?:Phone|Mobile|Tel)\s*:?\s*([\d\s\-\+\(\)]+)/gi,
      /(05\d{8})/gi,
      /(\+966\s*5\d{8})/gi
    ],
    email: [
      /(?:بريد إلكتروني|إيميل|البريد الإلكتروني)\s*:?\s*([^\s\n\r،]+@[^\s\n\r،]+)/gi,
      /(?:Email|E-mail)\s*:?\s*([^\s\n\r،]+@[^\s\n\r،]+)/gi,
      /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi
    ]
  }
};

export class NERService {
  private static instance: NERService;
  private apiKey: string;
  private baseUrl: string;

  private constructor() {
    this.apiKey = process.env.HUGGING_FACE_API_KEY || '';
    this.baseUrl = 'https://api-inference.huggingface.co/models';
  }

  static getInstance(): NERService {
    if (!NERService.instance) {
      NERService.instance = new NERService();
    }
    return NERService.instance;
  }

  /**
   * استخلاص الكيانات والمعلومات من النص
   */
  async extractEntitiesAndData(
    text: string, 
    language: 'ar' | 'en' | 'mixed',
    documentType?: string
  ): Promise<NERResult> {
    const startTime = Date.now();
    
    try {
      // استخلاص الكيانات باستخدام NER
      const entities = await this.extractNamedEntities(text, language);
      
      // استخلاص البيانات المهيكلة باستخدام الأنماط
      const structuredData = await this.extractStructuredData(text, language, documentType);
      
      // دمج النتائج وتحسينها
      const mergedData = this.mergeAndRefineData(entities, structuredData);
      
      // حساب الثقة الإجمالية
      const confidence = this.calculateConfidence(entities, mergedData);
      
      return {
        entities,
        extractedData: mergedData,
        confidence,
        processingTime: Date.now() - startTime,
        modelUsed: this.getModelForLanguage(language).primary
      };

    } catch (error) {
      console.error('❌ خطأ في استخلاص الكيانات:', error);
      throw new Error(`فشل في استخلاص البيانات: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    }
  }

  /**
   * استخلاص الكيانات المسماة باستخدام Hugging Face
   */
  private async extractNamedEntities(text: string, language: 'ar' | 'en' | 'mixed') {
    const models = this.getModelForLanguage(language);
    
    for (const modelName of [models.primary, models.secondary, models.fallback]) {
      try {
        console.log(`🔍 جاري استخدام نموذج NER: ${modelName}`);
        
        const response = await fetch(`${this.baseUrl}/${modelName}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            inputs: text,
            options: {
              wait_for_model: true,
              use_cache: false
            }
          })
        });

        if (!response.ok) {
          throw new Error(`NER API Error: ${response.statusText}`);
        }

        const result = await response.json();
        
        // معالجة النتيجة
        const entities = this.processNERResult(result);
        
        if (entities.length > 0) {
          console.log(`✅ نجح استخلاص ${entities.length} كيان`);
          return entities;
        }

      } catch (error) {
        console.warn(`⚠️ فشل النموذج ${modelName}:`, error);
        continue;
      }
    }

    // إذا فشلت جميع النماذج، استخدم الطريقة البديلة
    console.log('🔄 استخدام الطريقة البديلة لاستخلاص الكيانات...');
    return this.fallbackEntityExtraction(text, language);
  }

  /**
   * معالجة نتيجة NER من Hugging Face
   */
  private processNERResult(result: any) {
    const entities = [];
    
    if (Array.isArray(result)) {
      for (const entity of result) {
        entities.push({
          text: entity.word || entity.entity_group || entity.text || '',
          label: entity.entity_group || entity.label || 'UNKNOWN',
          confidence: entity.score || 0.8,
          start: entity.start || 0,
          end: entity.end || 0
        });
      }
    }
    
    return entities;
  }

  /**
   * استخلاص البيانات المهيكلة باستخدام الأنماط
   */
  private async extractStructuredData(
    text: string, 
    language: 'ar' | 'en' | 'mixed',
    documentType?: string
  ) {
    const data: Record<string, any> = {};
    const patterns = EXTRACTION_PATTERNS.arabic; // يمكن توسيعها للغات أخرى
    
    // تطبيق أنماط الاستخلاص
    for (const [fieldName, fieldPatterns] of Object.entries(patterns)) {
      for (const pattern of fieldPatterns) {
        const matches = text.match(pattern);
        if (matches && matches[1]) {
          data[fieldName] = this.cleanExtractedValue(matches[1], fieldName);
          break; // استخدم أول تطابق ناجح
        }
      }
    }

    // تحسينات خاصة بنوع المستند
    if (documentType) {
      this.applyDocumentSpecificRules(data, text, documentType);
    }

    return data;
  }

  /**
   * تنظيف القيم المستخلصة
   */
  private cleanExtractedValue(value: string, fieldType: string): string {
    let cleaned = value.trim();
    
    // إزالة علامات الترقيم الزائدة
    cleaned = cleaned.replace(/[،؛:]+$/, '');
    
    // تنظيف خاص بنوع الحقل
    switch (fieldType) {
      case 'registrationNumber':
      case 'nationalId':
        cleaned = cleaned.replace(/\D/g, ''); // الأرقام فقط
        break;
      case 'phone':
        cleaned = cleaned.replace(/[^\d\+\-\(\)\s]/g, '');
        break;
      case 'email':
        cleaned = cleaned.toLowerCase();
        break;
    }
    
    return cleaned;
  }

  /**
   * تطبيق قواعد خاصة بنوع المستند
   */
  private applyDocumentSpecificRules(data: Record<string, any>, text: string, documentType: string) {
    switch (documentType) {
      case 'commercial_registration':
        // التأكد من وجود رقم السجل التجاري
        if (!data.registrationNumber) {
          const crPattern = /(\d{10})/g;
          const match = text.match(crPattern);
          if (match) {
            data.registrationNumber = match[0];
          }
        }
        break;
        
      case 'national_id':
        // التأكد من صحة رقم الهوية الوطنية السعودية
        if (data.nationalId && data.nationalId.length === 10) {
          data.nationalIdValid = this.validateSaudiNationalId(data.nationalId);
        }
        break;
    }
  }

  /**
   * التحقق من صحة رقم الهوية الوطنية السعودية
   */
  private validateSaudiNationalId(id: string): boolean {
    if (!/^\d{10}$/.test(id)) return false;
    
    // خوارزمية التحقق من رقم الهوية السعودية
    const digits = id.split('').map(Number);
    let sum = 0;
    
    for (let i = 0; i < 9; i++) {
      if (i % 2 === 0) {
        const doubled = digits[i] * 2;
        sum += doubled > 9 ? doubled - 9 : doubled;
      } else {
        sum += digits[i];
      }
    }
    
    const checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit === digits[9];
  }

  /**
   * دمج وتحسين البيانات
   */
  private mergeAndRefineData(entities: any[], structuredData: Record<string, any>) {
    const merged = { ...structuredData };
    
    // دمج بيانات الكيانات
    entities.forEach(entity => {
      if (entity.label === 'PER' || entity.label === 'PERSON') {
        if (!merged.personName || !merged.ownerName) {
          merged.ownerName = merged.ownerName || entity.text;
        }
      } else if (entity.label === 'ORG' || entity.label === 'ORGANIZATION') {
        if (!merged.businessName) {
          merged.businessName = entity.text;
        }
      } else if (entity.label === 'LOC' || entity.label === 'LOCATION') {
        if (!merged.address) {
          merged.address = entity.text;
        }
      }
    });
    
    return merged;
  }

  /**
   * حساب الثقة الإجمالية
   */
  private calculateConfidence(entities: any[], extractedData: Record<string, any>): number {
    let confidence = 0.5; // قيمة أساسية
    
    // زيادة الثقة بناءً على عدد الكيانات المستخلصة
    confidence += Math.min(entities.length * 0.05, 0.3);
    
    // زيادة الثقة بناءً على عدد الحقول المملوءة
    const filledFields = Object.values(extractedData).filter(v => v && v.toString().trim()).length;
    confidence += Math.min(filledFields * 0.05, 0.2);
    
    return Math.min(confidence, 1.0);
  }

  /**
   * الحصول على النماذج للغة المحددة
   */
  private getModelForLanguage(language: 'ar' | 'en' | 'mixed') {
    return NER_MODELS[language] || NER_MODELS.mixed;
  }

  /**
   * طريقة بديلة لاستخلاص الكيانات
   */
  private fallbackEntityExtraction(text: string, language: string) {
    // استخلاص بسيط باستخدام regex
    const entities = [];
    
    // البحث عن أرقام (محتملة أن تكون أرقام تسجيل)
    const numbers = text.match(/\d{8,}/g);
    if (numbers) {
      numbers.forEach(num => {
        entities.push({
          text: num,
          label: 'NUMBER',
          confidence: 0.7,
          start: text.indexOf(num),
          end: text.indexOf(num) + num.length
        });
      });
    }
    
    return entities;
  }
}
