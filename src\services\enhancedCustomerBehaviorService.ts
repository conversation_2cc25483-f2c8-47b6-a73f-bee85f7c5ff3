// src/services/enhancedCustomerBehaviorService.ts
"use client";

import { 
  collection, 
  query, 
  where, 
  getDocs, 
  orderBy,
  Timestamp,
  limit,
  doc,
  getDoc
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// ===== أنواع البيانات =====

export interface CustomerBehaviorAnalysis {
  customerId: string;
  merchantId: string;
  behaviorProfile: {
    purchaseFrequency: 'high' | 'medium' | 'low';
    averageOrderValue: number;
    preferredCategories: string[];
    shoppingTimes: {
      preferredHours: number[];
      preferredDays: string[];
    };
    seasonalPatterns: SeasonalBehavior[];
  };
  loyaltyMetrics: {
    customerLifetimeValue: number;
    retentionProbability: number;
    churnRisk: 'low' | 'medium' | 'high';
    loyaltyScore: number;
  };
  recommendations: {
    productRecommendations: string[];
    marketingActions: string[];
    retentionStrategies: string[];
  };
  insights: CustomerInsight[];
  lastAnalyzed: Timestamp;
}

export interface SeasonalBehavior {
  season: 'spring' | 'summer' | 'fall' | 'winter';
  averageSpending: number;
  preferredProducts: string[];
  activityLevel: 'high' | 'medium' | 'low';
}

export interface CustomerInsight {
  type: 'behavior' | 'preference' | 'risk' | 'opportunity';
  title: string;
  description: string;
  confidence: number;
  actionable: boolean;
  suggestedActions: string[];
}

export interface CustomerSegmentAnalysis {
  segment: {
    name: string;
    description: string;
    criteria: string[];
  };
  customers: CustomerBehaviorAnalysis[];
  characteristics: {
    averageOrderValue: number;
    purchaseFrequency: number;
    retentionRate: number;
    lifetimeValue: number;
  };
  recommendations: {
    marketingStrategy: string[];
    productFocus: string[];
    communicationStyle: string[];
  };
}

// ===== خدمة تحليل سلوك العملاء المحسنة =====

export class EnhancedCustomerBehaviorService {
  private ordersCollection = collection(db, 'orders');
  private customersCollection = collection(db, 'users');
  private productsCollection = collection(db, 'products');
  private reviewsCollection = collection(db, 'reviews');

  /**
   * تحليل سلوك عميل محدد
   */
  async analyzeCustomerBehavior(
    customerId: string, 
    merchantId: string
  ): Promise<CustomerBehaviorAnalysis> {
    try {
      console.log(`🔍 تحليل سلوك العميل: ${customerId}`);

      // جلب بيانات العميل
      const customerDoc = await getDoc(doc(this.customersCollection, customerId));
      if (!customerDoc.exists()) {
        throw new Error('العميل غير موجود');
      }

      // جلب طلبات العميل
      const ordersQuery = query(
        this.ordersCollection,
        where('customerId', '==', customerId),
        where('merchantId', '==', merchantId),
        orderBy('createdAt', 'desc')
      );

      const ordersSnapshot = await getDocs(ordersQuery);
      const orders = ordersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // تحليل الملف الشخصي للسلوك
      const behaviorProfile = this.analyzeBehaviorProfile(orders);
      
      // حساب مقاييس الولاء
      const loyaltyMetrics = this.calculateLoyaltyMetrics(orders, customerDoc.data());
      
      // إنتاج التوصيات
      const recommendations = await this.generateRecommendations(
        customerId, 
        merchantId, 
        behaviorProfile, 
        loyaltyMetrics
      );
      
      // استخراج الرؤى
      const insights = this.extractInsights(behaviorProfile, loyaltyMetrics, orders);

      return {
        customerId,
        merchantId,
        behaviorProfile,
        loyaltyMetrics,
        recommendations,
        insights,
        lastAnalyzed: Timestamp.now()
      };

    } catch (error) {
      console.error('خطأ في تحليل سلوك العميل:', error);
      throw new Error('فشل في تحليل سلوك العميل');
    }
  }

  /**
   * تحليل الملف الشخصي للسلوك
   */
  private analyzeBehaviorProfile(orders: any[]) {
    if (orders.length === 0) {
      return {
        purchaseFrequency: 'low' as const,
        averageOrderValue: 0,
        preferredCategories: [],
        shoppingTimes: {
          preferredHours: [],
          preferredDays: []
        },
        seasonalPatterns: []
      };
    }

    // حساب تكرار الشراء
    const daysBetweenOrders = this.calculateDaysBetweenOrders(orders);
    const purchaseFrequency = daysBetweenOrders < 30 ? 'high' : 
                             daysBetweenOrders < 90 ? 'medium' : 'low';

    // حساب متوسط قيمة الطلب
    const totalValue = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
    const averageOrderValue = totalValue / orders.length;

    // تحليل الفئات المفضلة
    const categoryCount = new Map<string, number>();
    orders.forEach(order => {
      order.items?.forEach((item: any) => {
        const category = item.category || 'غير مصنف';
        categoryCount.set(category, (categoryCount.get(category) || 0) + item.quantity);
      });
    });

    const preferredCategories = Array.from(categoryCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([category]) => category);

    // تحليل أوقات التسوق
    const hourCounts = new Array(24).fill(0);
    const dayCounts = new Map<string, number>();

    orders.forEach(order => {
      const orderDate = order.createdAt.toDate();
      const hour = orderDate.getHours();
      const day = orderDate.toLocaleDateString('ar-SA', { weekday: 'long' });
      
      hourCounts[hour]++;
      dayCounts.set(day, (dayCounts.get(day) || 0) + 1);
    });

    const preferredHours = hourCounts
      .map((count, hour) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
      .map(item => item.hour);

    const preferredDays = Array.from(dayCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([day]) => day);

    // تحليل الأنماط الموسمية
    const seasonalPatterns = this.analyzeSeasonalPatterns(orders);

    return {
      purchaseFrequency,
      averageOrderValue,
      preferredCategories,
      shoppingTimes: {
        preferredHours,
        preferredDays
      },
      seasonalPatterns
    };
  }

  /**
   * حساب الأيام بين الطلبات
   */
  private calculateDaysBetweenOrders(orders: any[]): number {
    if (orders.length < 2) return 365; // افتراض طلب واحد في السنة

    const sortedOrders = orders.sort((a, b) => 
      a.createdAt.toDate().getTime() - b.createdAt.toDate().getTime()
    );

    let totalDays = 0;
    for (let i = 1; i < sortedOrders.length; i++) {
      const prevDate = sortedOrders[i - 1].createdAt.toDate();
      const currentDate = sortedOrders[i].createdAt.toDate();
      const daysDiff = (currentDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24);
      totalDays += daysDiff;
    }

    return totalDays / (sortedOrders.length - 1);
  }

  /**
   * تحليل الأنماط الموسمية
   */
  private analyzeSeasonalPatterns(orders: any[]): SeasonalBehavior[] {
    const seasonalData = new Map<string, {
      totalSpending: number;
      orderCount: number;
      products: Map<string, number>;
    }>();

    // تهيئة الفصول
    ['spring', 'summer', 'fall', 'winter'].forEach(season => {
      seasonalData.set(season, {
        totalSpending: 0,
        orderCount: 0,
        products: new Map()
      });
    });

    orders.forEach(order => {
      const orderDate = order.createdAt.toDate();
      const month = orderDate.getMonth();
      
      // تحديد الفصل
      let season = 'spring';
      if (month >= 5 && month <= 7) season = 'summer';
      else if (month >= 8 && month <= 10) season = 'fall';
      else if (month >= 11 || month <= 1) season = 'winter';

      const seasonData = seasonalData.get(season)!;
      seasonData.totalSpending += order.totalAmount || 0;
      seasonData.orderCount += 1;

      // تتبع المنتجات
      order.items?.forEach((item: any) => {
        const productName = item.productName || item.productId;
        seasonData.products.set(
          productName, 
          (seasonData.products.get(productName) || 0) + item.quantity
        );
      });
    });

    return Array.from(seasonalData.entries()).map(([season, data]) => ({
      season: season as 'spring' | 'summer' | 'fall' | 'winter',
      averageSpending: data.orderCount > 0 ? data.totalSpending / data.orderCount : 0,
      preferredProducts: Array.from(data.products.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([product]) => product),
      activityLevel: data.orderCount > orders.length * 0.3 ? 'high' :
                    data.orderCount > orders.length * 0.15 ? 'medium' : 'low'
    }));
  }

  /**
   * حساب مقاييس الولاء
   */
  private calculateLoyaltyMetrics(orders: any[], customerData: any) {
    const totalSpent = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
    const orderCount = orders.length;
    
    // حساب القيمة الدائمة للعميل
    const averageOrderValue = orderCount > 0 ? totalSpent / orderCount : 0;
    const customerLifetimeValue = totalSpent;

    // حساب احتمالية الاحتفاظ
    const daysSinceLastOrder = orders.length > 0 
      ? (Date.now() - orders[0].createdAt.toDate().getTime()) / (1000 * 60 * 60 * 24)
      : 365;
    
    const retentionProbability = Math.max(0, Math.min(100, 
      100 - (daysSinceLastOrder / 30) * 10
    ));

    // تقييم مخاطر الفقدان
    const churnRisk = daysSinceLastOrder > 90 ? 'high' :
                     daysSinceLastOrder > 30 ? 'medium' : 'low';

    // حساب نقاط الولاء
    const loyaltyScore = Math.min(100, 
      (orderCount * 10) + 
      (totalSpent / 100) + 
      (retentionProbability * 0.5)
    );

    return {
      customerLifetimeValue,
      retentionProbability,
      churnRisk,
      loyaltyScore
    };
  }

  /**
   * إنتاج التوصيات
   */
  private async generateRecommendations(
    customerId: string,
    merchantId: string,
    behaviorProfile: any,
    loyaltyMetrics: any
  ) {
    // توصيات المنتجات (محاكاة - في التطبيق الحقيقي نستخدم خوارزميات ML)
    const productRecommendations = behaviorProfile.preferredCategories.slice(0, 3);

    // إجراءات التسويق
    const marketingActions = [];
    if (loyaltyMetrics.churnRisk === 'high') {
      marketingActions.push('إرسال عرض خصم خاص');
      marketingActions.push('التواصل الشخصي مع العميل');
    }
    if (behaviorProfile.purchaseFrequency === 'high') {
      marketingActions.push('عرض برنامج الولاء');
      marketingActions.push('إشعارات المنتجات الجديدة');
    }

    // استراتيجيات الاحتفاظ
    const retentionStrategies = [];
    if (loyaltyMetrics.loyaltyScore > 70) {
      retentionStrategies.push('برنامج VIP للعملاء المميزين');
    }
    if (behaviorProfile.averageOrderValue > 200) {
      retentionStrategies.push('خصومات على الطلبات الكبيرة');
    }

    return {
      productRecommendations,
      marketingActions,
      retentionStrategies
    };
  }

  /**
   * استخراج الرؤى
   */
  private extractInsights(
    behaviorProfile: any, 
    loyaltyMetrics: any, 
    orders: any[]
  ): CustomerInsight[] {
    const insights: CustomerInsight[] = [];

    // رؤية حول تكرار الشراء
    if (behaviorProfile.purchaseFrequency === 'high') {
      insights.push({
        type: 'behavior',
        title: 'عميل نشط',
        description: 'هذا العميل يقوم بطلبات متكررة ويظهر ولاءً عالياً',
        confidence: 85,
        actionable: true,
        suggestedActions: ['تقديم برنامج ولاء', 'إشعارات المنتجات الجديدة']
      });
    }

    // رؤية حول مخاطر الفقدان
    if (loyaltyMetrics.churnRisk === 'high') {
      insights.push({
        type: 'risk',
        title: 'خطر فقدان العميل',
        description: 'العميل لم يقم بطلبات مؤخراً وقد يكون معرضاً للفقدان',
        confidence: 75,
        actionable: true,
        suggestedActions: ['إرسال عرض خاص', 'التواصل المباشر']
      });
    }

    // رؤية حول القيمة العالية
    if (loyaltyMetrics.customerLifetimeValue > 1000) {
      insights.push({
        type: 'opportunity',
        title: 'عميل عالي القيمة',
        description: 'هذا العميل يساهم بشكل كبير في الإيرادات',
        confidence: 90,
        actionable: true,
        suggestedActions: ['خدمة VIP', 'عروض حصرية']
      });
    }

    return insights;
  }

  /**
   * تحليل مجموعة من العملاء
   */
  async analyzeCustomerSegment(
    merchantId: string,
    segmentCriteria: {
      minOrderValue?: number;
      minOrderCount?: number;
      timeframe?: number; // بالأيام
    }
  ): Promise<CustomerSegmentAnalysis> {
    // جلب الطلبات حسب المعايير
    let ordersQuery = query(
      this.ordersCollection,
      where('merchantId', '==', merchantId)
    );

    if (segmentCriteria.timeframe) {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - segmentCriteria.timeframe);
      ordersQuery = query(
        ordersQuery,
        where('createdAt', '>=', Timestamp.fromDate(startDate))
      );
    }

    const ordersSnapshot = await getDocs(ordersQuery);
    const orders = ordersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // تجميع العملاء وتحليلهم
    const customerOrders = new Map<string, any[]>();
    orders.forEach(order => {
      const customerId = order.customerId;
      if (!customerOrders.has(customerId)) {
        customerOrders.set(customerId, []);
      }
      customerOrders.get(customerId)!.push(order);
    });

    // تطبيق معايير التجميع
    const qualifiedCustomers = Array.from(customerOrders.entries()).filter(([customerId, customerOrdersList]) => {
      const totalValue = customerOrdersList.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
      const orderCount = customerOrdersList.length;

      return (!segmentCriteria.minOrderValue || totalValue >= segmentCriteria.minOrderValue) &&
             (!segmentCriteria.minOrderCount || orderCount >= segmentCriteria.minOrderCount);
    });

    // تحليل كل عميل
    const customerAnalyses = await Promise.all(
      qualifiedCustomers.slice(0, 50).map(async ([customerId]) => {
        try {
          return await this.analyzeCustomerBehavior(customerId, merchantId);
        } catch (error) {
          console.warn(`تعذر تحليل العميل ${customerId}:`, error);
          return null;
        }
      })
    );

    const validAnalyses = customerAnalyses.filter(analysis => analysis !== null) as CustomerBehaviorAnalysis[];

    // حساب خصائص المجموعة
    const characteristics = {
      averageOrderValue: validAnalyses.reduce((sum, analysis) => 
        sum + analysis.behaviorProfile.averageOrderValue, 0) / validAnalyses.length,
      purchaseFrequency: validAnalyses.filter(analysis => 
        analysis.behaviorProfile.purchaseFrequency === 'high').length / validAnalyses.length,
      retentionRate: validAnalyses.filter(analysis => 
        analysis.loyaltyMetrics.churnRisk === 'low').length / validAnalyses.length * 100,
      lifetimeValue: validAnalyses.reduce((sum, analysis) => 
        sum + analysis.loyaltyMetrics.customerLifetimeValue, 0) / validAnalyses.length
    };

    return {
      segment: {
        name: 'مجموعة مخصصة',
        description: 'عملاء تم تحديدهم حسب معايير محددة',
        criteria: Object.entries(segmentCriteria).map(([key, value]) => `${key}: ${value}`)
      },
      customers: validAnalyses,
      characteristics,
      recommendations: {
        marketingStrategy: ['حملات مستهدفة', 'عروض شخصية'],
        productFocus: ['منتجات عالية الجودة', 'منتجات متميزة'],
        communicationStyle: ['تواصل شخصي', 'محتوى حصري']
      }
    };
  }
}

// إنشاء مثيل واحد من الخدمة
export const enhancedCustomerBehaviorService = new EnhancedCustomerBehaviorService();
