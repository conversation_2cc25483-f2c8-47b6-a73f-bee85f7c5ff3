"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Bot,
  MessageSquare,
  TrendingUp,
  Users,
  Settings,
  BarChart3
} from 'lucide-react';
import ChatbotManagement from '@/components/admin/ChatbotManagement';
import TicketManagement from '@/components/support/TicketManagement';

/**
 * صفحة إدارة الشات بوت والتذاكر للمسؤولين
 * واجهة شاملة لإدارة نظام الدعم الذكي
 */
export default function ChatbotAdminPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* رأس الصفحة */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
          <div className="p-3 bg-blue-100 rounded-lg">
            <Bot className="w-8 h-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              إدارة الشات بوت الذكي
            </h1>
            <p className="text-gray-600 mt-1">
              نظام إدارة شامل للشات بوت وتذاكر الدعم مع تحليلات متقدمة
            </p>
          </div>
        </div>

        {/* مؤشرات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الشات بوت</p>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-bold text-green-600">نشط</span>
                  </div>
                </div>
                <MessageSquare className="w-6 h-6 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">المحادثات اليوم</p>
                  <p className="text-lg font-bold text-blue-600">247</p>
                </div>
                <Users className="w-6 h-6 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">معدل الحل</p>
                  <p className="text-lg font-bold text-green-600">87%</p>
                </div>
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">رضا العملاء</p>
                  <p className="text-lg font-bold text-yellow-600">4.6/5</p>
                </div>
                <BarChart3 className="w-6 h-6 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* محتوى الصفحة */}
      <Tabs defaultValue="chatbot" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="chatbot" className="flex items-center space-x-2 rtl:space-x-reverse">
            <Bot className="w-4 h-4" />
            <span>إدارة الشات بوت</span>
          </TabsTrigger>
          <TabsTrigger value="tickets" className="flex items-center space-x-2 rtl:space-x-reverse">
            <MessageSquare className="w-4 h-4" />
            <span>إدارة التذاكر</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chatbot" className="mt-6">
          <ChatbotManagement />
        </TabsContent>

        <TabsContent value="tickets" className="mt-6">
          <TicketManagement />
        </TabsContent>
      </Tabs>

      {/* معلومات إضافية */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
              <Settings className="w-5 h-5" />
              <span>حالة النظام</span>
            </CardTitle>
            <CardDescription>
              معلومات حول حالة وأداء النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">خدمة الشات بوت</span>
                <Badge className="bg-green-100 text-green-800">
                  تعمل بشكل طبيعي
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">قاعدة المعرفة</span>
                <Badge className="bg-green-100 text-green-800">
                  محدثة
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">نظام التذاكر</span>
                <Badge className="bg-green-100 text-green-800">
                  نشط
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">التكامل مع Firebase</span>
                <Badge className="bg-green-100 text-green-800">
                  متصل
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
              <BarChart3 className="w-5 h-5" />
              <span>إحصائيات سريعة</span>
            </CardTitle>
            <CardDescription>
              أهم المؤشرات للأسبوع الحالي
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">إجمالي المحادثات</span>
                <span className="font-bold">1,247</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">متوسط وقت الاستجابة</span>
                <span className="font-bold">1.2 ثانية</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">التذاكر المحلولة</span>
                <span className="font-bold">89%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">التصعيد للدعم البشري</span>
                <span className="font-bold">13%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* تنبيهات مهمة */}
      <div className="mt-8">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader>
            <CardTitle className="text-lg">💡 نصائح لتحسين الأداء</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start space-x-2 rtl:space-x-reverse">
                <span className="text-blue-500">•</span>
                <span>قم بتحديث قاعدة المعرفة بانتظام لتحسين دقة الإجابات</span>
              </li>
              <li className="flex items-start space-x-2 rtl:space-x-reverse">
                <span className="text-blue-500">•</span>
                <span>راجع التذاكر المصعدة لتحديد المجالات التي تحتاج تحسين</span>
              </li>
              <li className="flex items-start space-x-2 rtl:space-x-reverse">
                <span className="text-blue-500">•</span>
                <span>استخدم تحليلات المحادثات لفهم احتياجات العملاء بشكل أفضل</span>
              </li>
              <li className="flex items-start space-x-2 rtl:space-x-reverse">
                <span className="text-blue-500">•</span>
                <span>تأكد من تدريب فريق الدعم على استخدام نظام التذاكر الجديد</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
