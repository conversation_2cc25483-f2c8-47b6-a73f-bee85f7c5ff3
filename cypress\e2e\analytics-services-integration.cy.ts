/// <reference types="cypress" />

describe('🔧 اختبار خدمات التحليلات المتقدمة', () => {
  beforeEach(() => {
    cy.mockLogin('merchant');
    
    // إعداد بيانات وهمية شاملة للاختبار
    cy.window().then((win) => {
      // بيانات طلبات متنوعة
      const mockOrders = [
        {
          id: 'order-1',
          merchantId: 'test-merchant-uid',
          customerId: 'customer-1',
          totalAmount: 350,
          status: 'completed',
          createdAt: { toDate: () => new Date('2024-01-10') },
          items: [
            { productId: 'prod-1', productName: 'لابتوب Dell', quantity: 1, price: 300, category: 'إلكترونيات' },
            { productId: 'prod-2', productName: 'ماوس لاسلكي', quantity: 1, price: 50, category: 'إلكترونيات' }
          ]
        },
        {
          id: 'order-2',
          merchantId: 'test-merchant-uid',
          customerId: 'customer-2',
          totalAmount: 180,
          status: 'completed',
          createdAt: { toDate: () => new Date('2024-01-15') },
          items: [
            { productId: 'prod-3', productName: 'قميص قطني', quantity: 2, price: 80, category: 'ملابس' },
            { productId: 'prod-4', productName: 'بنطال جينز', quantity: 1, price: 120, category: 'ملابس' }
          ]
        },
        {
          id: 'order-3',
          merchantId: 'test-merchant-uid',
          customerId: 'customer-1',
          totalAmount: 95,
          status: 'completed',
          createdAt: { toDate: () => new Date('2024-01-20') },
          items: [
            { productId: 'prod-5', productName: 'كتاب البرمجة', quantity: 1, price: 45, category: 'كتب' },
            { productId: 'prod-2', productName: 'ماوس لاسلكي', quantity: 1, price: 50, category: 'إلكترونيات' }
          ]
        },
        {
          id: 'order-4',
          merchantId: 'test-merchant-uid',
          customerId: 'customer-3',
          totalAmount: 220,
          status: 'completed',
          createdAt: { toDate: () => new Date('2024-01-25') },
          items: [
            { productId: 'prod-1', productName: 'لابتوب Dell', quantity: 1, price: 300, category: 'إلكترونيات' }
          ]
        }
      ];

      // بيانات منتجات مفصلة
      const mockProducts = [
        { 
          id: 'prod-1', 
          name: 'لابتوب Dell', 
          category: 'إلكترونيات', 
          merchantId: 'test-merchant-uid',
          price: 300,
          viewsCount: 150
        },
        { 
          id: 'prod-2', 
          name: 'ماوس لاسلكي', 
          category: 'إلكترونيات', 
          merchantId: 'test-merchant-uid',
          price: 50,
          viewsCount: 80
        },
        { 
          id: 'prod-3', 
          name: 'قميص قطني', 
          category: 'ملابس', 
          merchantId: 'test-merchant-uid',
          price: 80,
          viewsCount: 120
        },
        { 
          id: 'prod-4', 
          name: 'بنطال جينز', 
          category: 'ملابس', 
          merchantId: 'test-merchant-uid',
          price: 120,
          viewsCount: 90
        },
        { 
          id: 'prod-5', 
          name: 'كتاب البرمجة', 
          category: 'كتب', 
          merchantId: 'test-merchant-uid',
          price: 45,
          viewsCount: 60
        }
      ];

      // بيانات عملاء مفصلة
      const mockCustomers = [
        { 
          id: 'customer-1', 
          displayName: 'أحمد محمد', 
          email: '<EMAIL>',
          createdAt: { toDate: () => new Date('2023-12-01') }
        },
        { 
          id: 'customer-2', 
          displayName: 'فاطمة علي', 
          email: '<EMAIL>',
          createdAt: { toDate: () => new Date('2024-01-05') }
        },
        { 
          id: 'customer-3', 
          displayName: 'محمد أحمد', 
          email: '<EMAIL>',
          createdAt: { toDate: () => new Date('2024-01-20') }
        }
      ];

      // بيانات تقييمات
      const mockReviews = [
        { productId: 'prod-1', rating: 4.5, customerId: 'customer-1' },
        { productId: 'prod-1', rating: 5.0, customerId: 'customer-3' },
        { productId: 'prod-2', rating: 4.0, customerId: 'customer-1' },
        { productId: 'prod-3', rating: 4.2, customerId: 'customer-2' },
        { productId: 'prod-4', rating: 3.8, customerId: 'customer-2' },
        { productId: 'prod-5', rating: 4.7, customerId: 'customer-1' }
      ];

      // حفظ جميع البيانات الوهمية
      win.localStorage.setItem('mockOrders', JSON.stringify(mockOrders));
      win.localStorage.setItem('mockProducts', JSON.stringify(mockProducts));
      win.localStorage.setItem('mockCustomers', JSON.stringify(mockCustomers));
      win.localStorage.setItem('mockReviews', JSON.stringify(mockReviews));
    });
  });

  afterEach(() => {
    cy.window().then((win) => {
      win.localStorage.removeItem('mockOrders');
      win.localStorage.removeItem('mockProducts');
      win.localStorage.removeItem('mockCustomers');
      win.localStorage.removeItem('mockReviews');
    });
    cy.mockLogout();
  });

  describe('📊 خدمة التقارير المتقدمة (AdvancedReportsService)', () => {
    it('يجب أن تولد تقرير مبيعات شامل', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من تحميل البيانات
      cy.get('[data-testid="loading-indicator"]').should('be.visible');
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 10000 });
      
      // التحقق من وجود ملخص التقرير
      cy.get('[data-testid="sales-report-summary"]').should('be.visible');
      
      // التحقق من البيانات المحسوبة
      cy.get('[data-testid="total-revenue"]').should('contain.text', 'ريال');
      cy.get('[data-testid="total-orders"]').should('be.visible');
      cy.get('[data-testid="average-order-value"]').should('be.visible');
      cy.get('[data-testid="conversion-rate"]').should('be.visible');
    });

    it('يجب أن تحسب التحليل اليومي بشكل صحيح', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود التحليل اليومي
      cy.get('[data-testid="daily-breakdown"]').should('be.visible');
      
      // التحقق من وجود بيانات يومية
      cy.get('[data-testid="daily-sales-item"]').should('have.length.at.least', 1);
      
      // التحقق من تفاصيل اليوم
      cy.get('[data-testid="daily-sales-item"]').first().within(() => {
        cy.get('[data-testid="daily-date"]').should('be.visible');
        cy.get('[data-testid="daily-revenue"]').should('be.visible');
        cy.get('[data-testid="daily-orders"]').should('be.visible');
      });
    });

    it('يجب أن تحلل أداء المنتجات بدقة', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.contains('المنتجات').click();
      
      // التحقق من قائمة أداء المنتجات
      cy.get('[data-testid="product-performance-list"]').should('be.visible');
      
      // التحقق من ترتيب المنتجات حسب الإيرادات
      cy.get('[data-testid="product-item"]').first().within(() => {
        cy.get('[data-testid="product-revenue"]').should('be.visible');
        cy.get('[data-testid="product-sales-count"]').should('be.visible');
        cy.get('[data-testid="product-rating"]').should('be.visible');
      });
      
      // التحقق من وجود مؤشرات الاتجاه
      cy.get('[data-testid="product-trend-indicator"]').should('have.length.at.least', 1);
    });

    it('يجب أن تحسب تحليلات العملاء بشكل صحيح', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.contains('العملاء').click();
      
      // التحقق من إحصائيات العملاء
      cy.get('[data-testid="customer-analytics"]').should('be.visible');
      
      // التحقق من العملاء الجدد مقابل العائدين
      cy.get('[data-testid="new-customers"]').should('be.visible');
      cy.get('[data-testid="returning-customers"]').should('be.visible');
      
      // التحقق من معدل الاحتفاظ
      cy.get('[data-testid="retention-rate"]').should('be.visible');
      
      // التحقق من القيمة الدائمة للعميل
      cy.get('[data-testid="customer-lifetime-value"]').should('be.visible');
    });
  });

  describe('🧠 خدمة تحليل سلوك العملاء المحسنة', () => {
    it('يجب أن تحلل سلوك العميل الفردي', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.contains('العملاء').click();
      
      // النقر على عميل محدد لعرض تحليل السلوك
      cy.get('[data-testid="customer-item"]').first().click();
      
      // التحقق من ظهور تحليل السلوك (إذا كان مطبقاً)
      cy.get('[data-testid="customer-behavior-analysis"]').should('be.visible');
      
      // التحقق من الملف الشخصي للسلوك
      cy.get('[data-testid="purchase-frequency"]').should('be.visible');
      cy.get('[data-testid="preferred-categories"]').should('be.visible');
      cy.get('[data-testid="shopping-times"]').should('be.visible');
    });

    it('يجب أن تحسب مقاييس الولاء بدقة', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.contains('العملاء').click();
      
      // التحقق من مقاييس الولاء
      cy.get('[data-testid="loyalty-metrics"]').should('be.visible');
      
      // التحقق من نقاط الولاء
      cy.get('[data-testid="loyalty-score"]').should('be.visible');
      
      // التحقق من مخاطر الفقدان
      cy.get('[data-testid="churn-risk"]').should('be.visible');
      
      // التحقق من احتمالية الاحتفاظ
      cy.get('[data-testid="retention-probability"]').should('be.visible');
    });

    it('يجب أن تقدم توصيات ذكية', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.contains('العملاء').click();
      
      // التحقق من وجود قسم التوصيات
      cy.get('[data-testid="customer-recommendations"]').should('be.visible');
      
      // التحقق من توصيات المنتجات
      cy.get('[data-testid="product-recommendations"]').should('be.visible');
      
      // التحقق من إجراءات التسويق المقترحة
      cy.get('[data-testid="marketing-actions"]').should('be.visible');
      
      // التحقق من استراتيجيات الاحتفاظ
      cy.get('[data-testid="retention-strategies"]').should('be.visible');
    });
  });

  describe('📈 مكون مؤشرات الأداء المحسن', () => {
    it('يجب أن يعرض جميع المؤشرات الثمانية', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود جميع المؤشرات
      const expectedKPIs = [
        'إجمالي الإيرادات',
        'إجمالي الطلبات', 
        'إجمالي العملاء',
        'متوسط قيمة الطلب',
        'معدل التحويل',
        'معدل الاحتفاظ',
        'رضا العملاء',
        'المنتجات النشطة'
      ];
      
      expectedKPIs.forEach(kpi => {
        cy.contains(kpi).should('be.visible');
      });
    });

    it('يجب أن يحسب حالات المؤشرات بشكل صحيح', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود شارات الحالة
      cy.get('[data-testid="kpi-status-badge"]').should('have.length', 8);
      
      // التحقق من الألوان المختلفة
      cy.get('[data-testid="kpi-status-excellent"]').should('exist');
      cy.get('[data-testid="kpi-status-good"]').should('exist');
      
      // التحقق من الأيقونات المناسبة
      cy.get('[data-testid="status-icon"]').should('have.length.at.least', 8);
    });

    it('يجب أن يعرض التقدم نحو الأهداف', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود أشرطة التقدم
      cy.get('[data-testid="progress-bar"]').should('have.length', 8);
      
      // التحقق من نسب التقدم
      cy.get('[data-testid="progress-percentage"]').should('have.length', 8);
      
      // التحقق من أن النسب منطقية (بين 0 و 100)
      cy.get('[data-testid="progress-percentage"]').each(($el) => {
        const percentage = parseInt($el.text());
        expect(percentage).to.be.at.least(0);
        expect(percentage).to.be.at.most(100);
      });
    });

    it('يجب أن يعرض ملخص الأداء العام', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود ملخص الأداء
      cy.get('[data-testid="performance-summary"]').should('be.visible');
      
      // التحقق من عدد المؤشرات الممتازة
      cy.get('[data-testid="excellent-kpis-count"]').should('be.visible');
      
      // التحقق من عدد المؤشرات الجيدة
      cy.get('[data-testid="good-kpis-count"]').should('be.visible');
      
      // التحقق من عدد المؤشرات التي تحتاج تحسين
      cy.get('[data-testid="needs-improvement-count"]').should('be.visible');
    });
  });

  describe('📊 مكون الرسوم البيانية المتقدمة', () => {
    it('يجب أن يعرض أنواع رسوم بيانية متعددة', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // اختبار الرسم الخطي
      cy.get('[data-testid="chart-type-selector"]').click();
      cy.contains('خطي').click();
      cy.get('[data-testid="line-chart"]').should('be.visible');
      
      // اختبار رسم المساحة
      cy.get('[data-testid="chart-type-selector"]').click();
      cy.contains('مساحة').click();
      cy.get('[data-testid="area-chart"]').should('be.visible');
      
      // اختبار الرسم العمودي
      cy.get('[data-testid="chart-type-selector"]').click();
      cy.contains('أعمدة').click();
      cy.get('[data-testid="bar-chart"]').should('be.visible');
      
      // اختبار الرسم الدائري
      cy.get('[data-testid="chart-type-selector"]').click();
      cy.contains('دائري').click();
      cy.get('[data-testid="pie-chart"]').should('be.visible');
    });

    it('يجب أن تعمل أدوات التحكم في الفترة الزمنية', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // اختبار فترات زمنية مختلفة
      const timeRanges = ['7 أيام', '30 يوم', '90 يوم'];
      
      timeRanges.forEach(range => {
        cy.get('[data-testid="chart-time-range-selector"]').click();
        cy.contains(range).click();
        
        // التحقق من تحديث الرسم البياني
        cy.get('[data-testid="chart-container"]').should('be.visible');
      });
    });

    it('يجب أن يعرض الرسم المقارن بشكل صحيح', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من وجود الرسم المقارن
      cy.get('[data-testid="comparison-chart"]').should('be.visible');
      
      // التحقق من وجود خطوط متعددة للمقارنة
      cy.get('[data-testid="chart-line"]').should('have.length.at.least', 2);
      
      // التحقق من وجود وسائل الإيضاح
      cy.get('[data-testid="chart-legend"]').should('be.visible');
    });
  });

  describe('🔄 التكامل بين الخدمات', () => {
    it('يجب أن تتكامل البيانات بين جميع المكونات', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من تطابق البيانات بين المؤشرات والرسوم البيانية
      cy.get('[data-testid="kpi-revenue"]').find('[data-testid="kpi-value"]').invoke('text').as('kpiRevenue');
      
      cy.contains('المبيعات').click();
      cy.get('[data-testid="sales-total-revenue"]').invoke('text').as('salesRevenue');
      
      // مقارنة القيم (قد تحتاج تعديل حسب تنسيق النص)
      cy.get('@kpiRevenue').then(kpiValue => {
        cy.get('@salesRevenue').then(salesValue => {
          // التحقق من أن القيم متطابقة أو متقاربة
          expect(kpiValue).to.contain('ريال');
          expect(salesValue).to.contain('ريال');
        });
      });
    });

    it('يجب أن تتحدث جميع المكونات عند تغيير الفترة الزمنية', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // تغيير الفترة الزمنية
      cy.get('[data-testid="time-range-selector"]').click();
      cy.contains('7 أيام').click();
      
      // التحقق من تحديث المؤشرات
      cy.get('[data-testid="kpi-loading"]').should('be.visible');
      cy.get('[data-testid="kpi-loading"]').should('not.exist', { timeout: 5000 });
      
      // التحقق من تحديث الرسوم البيانية
      cy.get('[data-testid="chart-loading"]').should('be.visible');
      cy.get('[data-testid="chart-loading"]').should('not.exist', { timeout: 5000 });
      
      // التحقق من تحديث التبويبات
      cy.contains('المبيعات').click();
      cy.get('[data-testid="sales-data"]').should('be.visible');
    });
  });

  describe('⚠️ معالجة الأخطاء', () => {
    it('يجب أن تعرض رسائل خطأ مناسبة عند فشل تحميل البيانات', () => {
      // محاكاة خطأ في البيانات
      cy.window().then((win) => {
        win.localStorage.setItem('simulateError', 'true');
      });
      
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من ظهور رسالة الخطأ
      cy.get('[data-testid="error-message"]').should('be.visible');
      cy.contains('فشل في تحميل').should('be.visible');
      
      // التحقق من وجود زر إعادة المحاولة
      cy.get('[data-testid="retry-button"]').should('be.visible');
      
      // اختبار إعادة المحاولة
      cy.window().then((win) => {
        win.localStorage.removeItem('simulateError');
      });
      
      cy.get('[data-testid="retry-button"]').click();
      cy.get('[data-testid="error-message"]').should('not.exist');
    });

    it('يجب أن تعمل بشكل صحيح مع بيانات فارغة', () => {
      // محاكاة بيانات فارغة
      cy.window().then((win) => {
        win.localStorage.setItem('mockOrders', JSON.stringify([]));
        win.localStorage.setItem('mockProducts', JSON.stringify([]));
        win.localStorage.setItem('mockCustomers', JSON.stringify([]));
      });
      
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من عرض رسائل "لا توجد بيانات"
      cy.contains('لا توجد بيانات').should('be.visible');
      
      // التحقق من أن المؤشرات تعرض قيم صفر
      cy.get('[data-testid="kpi-value"]').should('contain.text', '0');
    });
  });
});
