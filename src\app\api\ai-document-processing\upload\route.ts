// src/app/api/ai-document-processing/upload/route.ts
// تم تحديثه لاستخدام النظام المحلي بدلاً من Hugging Face API
import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { HuggingFaceAIService } from '@/services/huggingFaceAIService';

// إعدادات رفع الملفات
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const SUPPORTED_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/heic',
  'application/pdf'
];

export async function POST(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'غير مصرح بالوصول' },
        { status: 401 }
      );
    }

    const token = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await getAuth().verifyIdToken(token);
    } catch (error) {
      return NextResponse.json(
        { error: 'رمز المصادقة غير صالح' },
        { status: 401 }
      );
    }

    // استخراج البيانات من الطلب
    const formData = await request.formData();
    const file = formData.get('document') as File;
    const userType = formData.get('userType') as string;
    const documentType = formData.get('documentType') as string;

    // التحقق من صحة البيانات
    if (!file) {
      return NextResponse.json(
        { error: 'لم يتم رفع أي ملف' },
        { status: 400 }
      );
    }

    if (!userType || !['merchant', 'representative'].includes(userType)) {
      return NextResponse.json(
        { error: 'نوع المستخدم غير صالح' },
        { status: 400 }
      );
    }

    // التحقق من حجم الملف
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { 
          error: 'حجم الملف كبير جداً',
          maxSize: '10 ميجابايت',
          currentSize: `${(file.size / 1024 / 1024).toFixed(2)} ميجابايت`
        },
        { status: 400 }
      );
    }

    // التحقق من نوع الملف
    if (!SUPPORTED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { 
          error: 'نوع الملف غير مدعوم',
          supportedTypes: ['JPG', 'PNG', 'WebP', 'HEIC', 'PDF'],
          receivedType: file.type
        },
        { status: 400 }
      );
    }

    // تحويل الملف إلى Buffer ورفعه (محاكاة)
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // في التطبيق الحقيقي، سيتم رفع الملف إلى التخزين السحابي
    const documentUrl = `https://storage.example.com/${Date.now()}-${file.name}`;

    // بدء معالجة المستند
    const aiService = HuggingFaceAIService.getInstance();
    
    try {
      const result = await aiService.processDocument(
        documentUrl,
        decodedToken.uid,
        userType as 'merchant' | 'representative',
        file.name,
        file.size,
        file.type
      );

      return NextResponse.json({
        success: true,
        message: 'تم رفع المستند وبدء المعالجة المحلية بنجاح',
        data: {
          processingId: result.id,
          status: result.status,
          currentStage: result.currentStage,
          estimatedTime: '2-5 ثواني',
          processingType: 'local',
          benefits: ['معالجة مجانية', 'حماية الخصوصية', 'سرعة عالية']
        }
      });

    } catch (processingError) {
      console.error('خطأ في معالجة المستند:', processingError);
      
      return NextResponse.json({
        success: false,
        error: 'فشل في معالجة المستند',
        details: processingError instanceof Error ? processingError.message : 'خطأ غير معروف'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('خطأ في API رفع المستندات:', error);
    
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 });
  }
}

// دعم OPTIONS للـ CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
