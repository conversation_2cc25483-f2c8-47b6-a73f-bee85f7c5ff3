#!/usr/bin/env node

/**
 * سكريبت تشغيل اختبارات نظام التحليلات المتقدمة
 * يقوم بتشغيل جميع اختبارات التحليلات بترتيب منطقي
 */

const { execSync } = require('child_process');
const path = require('path');

// ألوان للطباعة
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// دالة طباعة ملونة
function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// دالة تشغيل الأوامر
function runCommand(command, description) {
  colorLog(`\n🔄 ${description}...`, 'cyan');
  try {
    execSync(command, { stdio: 'inherit', cwd: process.cwd() });
    colorLog(`✅ ${description} - مكتمل`, 'green');
    return true;
  } catch (error) {
    colorLog(`❌ ${description} - فشل`, 'red');
    console.error(error.message);
    return false;
  }
}

// قائمة الاختبارات بالترتيب
const testSuites = [
  {
    file: 'advanced-analytics-system.cy.ts',
    name: 'اختبارات النظام الأساسي للتحليلات',
    description: 'اختبار الوظائف الأساسية لنظام التحليلات'
  },
  {
    file: 'analytics-services-integration.cy.ts', 
    name: 'اختبارات تكامل الخدمات',
    description: 'اختبار التكامل بين خدمات التحليلات المختلفة'
  },
  {
    file: 'analytics-performance-tests.cy.ts',
    name: 'اختبارات الأداء',
    description: 'اختبار أداء النظام مع البيانات الكبيرة'
  }
];

// دالة تشغيل اختبار واحد
function runSingleTest(testSuite, browser = 'chrome') {
  const command = `npx cypress run --spec "cypress/e2e/${testSuite.file}" --browser ${browser} --reporter json --reporter-options "output=cypress/results/${testSuite.file.replace('.cy.ts', '')}-results.json"`;
  
  colorLog(`\n📊 تشغيل: ${testSuite.name}`, 'magenta');
  colorLog(`📝 الوصف: ${testSuite.description}`, 'yellow');
  
  return runCommand(command, `اختبار ${testSuite.name}`);
}

// دالة تشغيل جميع الاختبارات
function runAllTests(browser = 'chrome', headless = true) {
  colorLog('\n🚀 بدء تشغيل اختبارات نظام التحليلات المتقدمة', 'bright');
  colorLog('=' .repeat(60), 'blue');
  
  const results = {
    total: testSuites.length,
    passed: 0,
    failed: 0,
    details: []
  };
  
  // إنشاء مجلد النتائج
  runCommand('mkdir -p cypress/results', 'إنشاء مجلد النتائج');
  
  // تشغيل كل اختبار
  for (const testSuite of testSuites) {
    const success = runSingleTest(testSuite, browser);
    
    if (success) {
      results.passed++;
      results.details.push({ name: testSuite.name, status: 'نجح' });
    } else {
      results.failed++;
      results.details.push({ name: testSuite.name, status: 'فشل' });
    }
  }
  
  // طباعة النتائج النهائية
  printFinalResults(results);
  
  return results.failed === 0;
}

// دالة طباعة النتائج النهائية
function printFinalResults(results) {
  colorLog('\n📋 ملخص النتائج', 'bright');
  colorLog('=' .repeat(60), 'blue');
  
  colorLog(`📊 إجمالي الاختبارات: ${results.total}`, 'cyan');
  colorLog(`✅ نجح: ${results.passed}`, 'green');
  colorLog(`❌ فشل: ${results.failed}`, 'red');
  
  colorLog('\n📝 تفاصيل النتائج:', 'bright');
  results.details.forEach((detail, index) => {
    const icon = detail.status === 'نجح' ? '✅' : '❌';
    const color = detail.status === 'نجح' ? 'green' : 'red';
    colorLog(`${index + 1}. ${icon} ${detail.name} - ${detail.status}`, color);
  });
  
  if (results.failed === 0) {
    colorLog('\n🎉 جميع الاختبارات نجحت!', 'green');
  } else {
    colorLog(`\n⚠️  ${results.failed} اختبار فشل من أصل ${results.total}`, 'red');
  }
  
  colorLog('\n📁 تقارير مفصلة متوفرة في: cypress/results/', 'cyan');
}

// دالة تشغيل اختبارات الأداء فقط
function runPerformanceTests(browser = 'chrome') {
  colorLog('\n⚡ تشغيل اختبارات الأداء فقط', 'bright');
  
  const performanceTest = testSuites.find(test => 
    test.file === 'analytics-performance-tests.cy.ts'
  );
  
  if (performanceTest) {
    return runSingleTest(performanceTest, browser);
  } else {
    colorLog('❌ لم يتم العثور على اختبارات الأداء', 'red');
    return false;
  }
}

// دالة تشغيل اختبارات التكامل فقط
function runIntegrationTests(browser = 'chrome') {
  colorLog('\n🔗 تشغيل اختبارات التكامل فقط', 'bright');
  
  const integrationTest = testSuites.find(test => 
    test.file === 'analytics-services-integration.cy.ts'
  );
  
  if (integrationTest) {
    return runSingleTest(integrationTest, browser);
  } else {
    colorLog('❌ لم يتم العثور على اختبارات التكامل', 'red');
    return false;
  }
}

// دالة تنظيف النتائج القديمة
function cleanResults() {
  colorLog('\n🧹 تنظيف النتائج القديمة...', 'yellow');
  runCommand('rm -rf cypress/results/*', 'تنظيف النتائج القديمة');
  runCommand('rm -rf cypress/screenshots/*', 'تنظيف لقطات الشاشة القديمة');
  runCommand('rm -rf cypress/videos/*', 'تنظيف مقاطع الفيديو القديمة');
}

// دالة إنتاج تقرير HTML
function generateHTMLReport() {
  colorLog('\n📄 إنتاج تقرير HTML...', 'cyan');
  
  const command = `npx mochawesome-merge cypress/results/*.json > cypress/results/merged-report.json && npx marge cypress/results/merged-report.json --reportDir cypress/results/html --inline`;
  
  return runCommand(command, 'إنتاج تقرير HTML');
}

// دالة المساعدة
function showHelp() {
  colorLog('\n📖 مساعدة سكريبت اختبارات التحليلات', 'bright');
  colorLog('=' .repeat(50), 'blue');
  
  console.log(`
الاستخدام: node run-analytics-tests.js [خيارات]

الخيارات:
  --all                 تشغيل جميع الاختبارات (افتراضي)
  --performance        تشغيل اختبارات الأداء فقط
  --integration        تشغيل اختبارات التكامل فقط
  --browser <name>     المتصفح المستخدم (chrome, firefox, edge)
  --clean              تنظيف النتائج القديمة قبل التشغيل
  --report             إنتاج تقرير HTML بعد التشغيل
  --help               عرض هذه المساعدة

أمثلة:
  node run-analytics-tests.js --all --browser chrome --clean --report
  node run-analytics-tests.js --performance --browser firefox
  node run-analytics-tests.js --integration --clean
  `);
}

// دالة رئيسية
function main() {
  const args = process.argv.slice(2);
  
  // تحليل المعاملات
  const options = {
    mode: 'all',
    browser: 'chrome',
    clean: false,
    report: false,
    help: false
  };
  
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--all':
        options.mode = 'all';
        break;
      case '--performance':
        options.mode = 'performance';
        break;
      case '--integration':
        options.mode = 'integration';
        break;
      case '--browser':
        options.browser = args[++i] || 'chrome';
        break;
      case '--clean':
        options.clean = true;
        break;
      case '--report':
        options.report = true;
        break;
      case '--help':
        options.help = true;
        break;
    }
  }
  
  // عرض المساعدة
  if (options.help) {
    showHelp();
    return;
  }
  
  // تنظيف النتائج القديمة
  if (options.clean) {
    cleanResults();
  }
  
  // تشغيل الاختبارات حسب الوضع
  let success = false;
  
  switch (options.mode) {
    case 'performance':
      success = runPerformanceTests(options.browser);
      break;
    case 'integration':
      success = runIntegrationTests(options.browser);
      break;
    case 'all':
    default:
      success = runAllTests(options.browser);
      break;
  }
  
  // إنتاج تقرير HTML
  if (options.report && success) {
    generateHTMLReport();
  }
  
  // إنهاء البرنامج
  process.exit(success ? 0 : 1);
}

// تشغيل البرنامج الرئيسي
if (require.main === module) {
  main();
}

module.exports = {
  runAllTests,
  runPerformanceTests,
  runIntegrationTests,
  cleanResults,
  generateHTMLReport
};
