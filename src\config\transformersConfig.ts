// src/config/transformersConfig.ts - تكوين النماذج المحلية لـ Transformers.js

/**
 * تكوين النماذج المحلية المحسّنة للأداء والدقة
 * جميع النماذج صغيرة الحجم وسريعة التحميل
 */

export interface ModelConfig {
  id: string;
  name: string;
  size: string;
  language: 'ar' | 'en' | 'multilingual';
  task: 'ocr' | 'ner' | 'classification';
  priority: number; // 1 = أولوية عالية، 3 = أولوية منخفضة
  fallback?: string;
}

// نماذج OCR المحسّنة
export const OCR_MODELS: Record<string, ModelConfig> = {
  // نموذج TrOCR الأساسي للنصوص المطبوعة
  trocr_printed: {
    id: 'Xenova/trocr-base-printed',
    name: 'TrOCR Base Printed',
    size: '~45MB',
    language: 'multilingual',
    task: 'ocr',
    priority: 1
  },
  
  // نموذج TrOCR للخط اليدوي
  trocr_handwritten: {
    id: 'Xenova/trocr-base-handwritten',
    name: 'TrOCR Base Handwritten',
    size: '~45MB',
    language: 'multilingual',
    task: 'ocr',
    priority: 2,
    fallback: 'trocr_printed'
  },

  // نموذج احتياطي سريع
  tesseract_fallback: {
    id: 'tesseract.js',
    name: 'Tesseract.js Fallback',
    size: '~2MB',
    language: 'multilingual',
    task: 'ocr',
    priority: 3
  }
};

// نماذج NER المحسّنة
export const NER_MODELS: Record<string, ModelConfig> = {
  // نموذج BERT متعدد اللغات صغير
  bert_multilingual: {
    id: 'Xenova/bert-base-multilingual-cased',
    name: 'BERT Multilingual Base',
    size: '~110MB',
    language: 'multilingual',
    task: 'ner',
    priority: 1
  },

  // نموذج DistilBERT سريع للإنجليزية
  distilbert_en: {
    id: 'Xenova/distilbert-base-cased',
    name: 'DistilBERT English',
    size: '~65MB',
    language: 'en',
    task: 'ner',
    priority: 2,
    fallback: 'bert_multilingual'
  },

  // نموذج AraBERT مضغوط للعربية
  arabert_mini: {
    id: 'Xenova/arabert-base',
    name: 'AraBERT Mini',
    size: '~85MB',
    language: 'ar',
    task: 'ner',
    priority: 1
  }
};

// نماذج التصنيف المحسّنة
export const CLASSIFICATION_MODELS: Record<string, ModelConfig> = {
  // نموذج تصنيف المستندات
  document_classifier: {
    id: 'Xenova/distilbert-base-uncased-finetuned-sst-2-english',
    name: 'Document Classifier',
    size: '~65MB',
    language: 'multilingual',
    task: 'classification',
    priority: 1
  },

  // نموذج تحليل المشاعر متعدد اللغات
  sentiment_multilingual: {
    id: 'Xenova/bert-base-multilingual-uncased-sentiment',
    name: 'Multilingual Sentiment',
    size: '~110MB',
    language: 'multilingual',
    task: 'classification',
    priority: 2,
    fallback: 'document_classifier'
  }
};

// إعدادات الأداء والذاكرة
export const PERFORMANCE_CONFIG = {
  // حد أقصى للذاكرة المستخدمة (بالميجابايت)
  maxMemoryUsage: 512,
  
  // عدد النماذج المحملة في نفس الوقت
  maxConcurrentModels: 2,
  
  // مهلة تحميل النموذج (بالثواني)
  modelLoadTimeout: 30,
  
  // تفعيل التخزين المؤقت
  enableCaching: true,
  
  // حجم التخزين المؤقت (بالميجابايت)
  cacheSize: 100,
  
  // تفعيل ضغط النماذج
  enableCompression: true,
  
  // استخدام Web Workers للمعالجة
  useWebWorkers: true,
  
  // عدد الخيوط المتوازية
  numThreads: navigator.hardwareConcurrency || 4
};

// أنواع المستندات المدعومة
export const SUPPORTED_DOCUMENT_TYPES = {
  identity: {
    ar: ['هوية', 'بطاقة شخصية', 'جواز سفر'],
    en: ['id card', 'identity', 'passport', 'driver license']
  },
  business: {
    ar: ['سجل تجاري', 'رخصة تجارية', 'شهادة تأسيس'],
    en: ['business license', 'commercial registration', 'certificate']
  },
  financial: {
    ar: ['كشف حساب', 'فاتورة', 'إيصال'],
    en: ['bank statement', 'invoice', 'receipt', 'bill']
  }
};

// إعدادات جودة المعالجة
export const QUALITY_SETTINGS = {
  // الحد الأدنى لثقة OCR
  minOCRConfidence: 0.7,
  
  // الحد الأدنى لثقة NER
  minNERConfidence: 0.6,
  
  // الحد الأدنى لثقة التصنيف
  minClassificationConfidence: 0.8,
  
  // الحد الأقصى لوقت المعالجة (بالثواني)
  maxProcessingTime: 30,
  
  // تفعيل إعادة المحاولة عند الفشل
  enableRetry: true,
  
  // عدد المحاولات القصوى
  maxRetries: 3,
  
  // تأخير بين المحاولات (بالثواني)
  retryDelay: 2
};

// إعدادات اللغة
export const LANGUAGE_CONFIG = {
  // اللغة الافتراضية
  defaultLanguage: 'ar' as const,
  
  // اللغات المدعومة
  supportedLanguages: ['ar', 'en', 'mixed'] as const,
  
  // كشف اللغة التلقائي
  autoDetectLanguage: true,
  
  // حد الثقة لكشف اللغة
  languageDetectionThreshold: 0.8
};

// تصدير التكوين الشامل
export const TRANSFORMERS_CONFIG = {
  models: {
    ocr: OCR_MODELS,
    ner: NER_MODELS,
    classification: CLASSIFICATION_MODELS
  },
  performance: PERFORMANCE_CONFIG,
  quality: QUALITY_SETTINGS,
  language: LANGUAGE_CONFIG,
  documentTypes: SUPPORTED_DOCUMENT_TYPES
} as const;

export type TransformersConfig = typeof TRANSFORMERS_CONFIG;
export type SupportedLanguage = typeof LANGUAGE_CONFIG.supportedLanguages[number];
export type ModelTask = 'ocr' | 'ner' | 'classification';
