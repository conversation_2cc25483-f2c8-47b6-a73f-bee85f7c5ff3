"use client";

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { detectUserLanguage, saveUserLanguagePreference, useLocale } from '@/hooks/use-locale';
import type { Locale } from '@/lib/i18n';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Globe, Check, X, Languages } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface AutoLanguageDetectorProps {
  currentLocale: Locale;
  onLanguageChange?: (locale: Locale) => void;
}

/**
 * مكون التعرف التلقائي على اللغة
 * يعرض اقتراح تغيير اللغة بناءً على إعدادات المستخدم
 */
export default function AutoLanguageDetector({
  currentLocale,
  onLanguageChange
}: AutoLanguageDetectorProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { t } = useLocale();
  const [detectedLanguage, setDetectedLanguage] = useState<Locale | null>(null);
  const [showSuggestion, setShowSuggestion] = useState(false);
  const [isChanging, setIsChanging] = useState(false);

  useEffect(() => {
    // تشغيل اكتشاف اللغة
    const detected = detectUserLanguage();
    setDetectedLanguage(detected);

    // عرض الاقتراح إذا كانت اللغة المكتشفة مختلفة عن الحالية
    const shouldShowSuggestion = detected !== currentLocale && 
                                !localStorage.getItem('language-suggestion-dismissed') &&
                                !localStorage.getItem('preferred-language');

    if (shouldShowSuggestion) {
      // تأخير عرض الاقتراح لتجنب التداخل مع التحميل
      setTimeout(() => {
        setShowSuggestion(true);
      }, 2000);
    }
  }, [currentLocale]);

  const handleAcceptSuggestion = async () => {
    if (!detectedLanguage) return;

    setIsChanging(true);
    
    try {
      // حفظ اللغة المفضلة
      saveUserLanguagePreference(detectedLanguage);
      
      // تغيير المسار
      const newPathname = pathname.replace(`/${currentLocale}`, `/${detectedLanguage}`);
      
      // إشعار المكون الأب
      onLanguageChange?.(detectedLanguage);
      
      // التنقل للمسار الجديد
      router.push(newPathname);
      
      setShowSuggestion(false);
    } catch (error) {
      console.error('خطأ في تغيير اللغة:', error);
      setIsChanging(false);
    }
  };

  const handleDismissSuggestion = () => {
    // حفظ رفض الاقتراح
    localStorage.setItem('language-suggestion-dismissed', 'true');
    localStorage.setItem('language-suggestion-dismissed-timestamp', Date.now().toString());
    setShowSuggestion(false);
  };

  const getLanguageName = (locale: Locale): string => {
    return locale === 'ar' ? 'العربية' : 'English';
  };

  const getLanguageFlag = (locale: Locale): string => {
    return locale === 'ar' ? '🇸🇦' : '🇺🇸';
  };

  if (!showSuggestion || !detectedLanguage) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <Card className="border-2 border-primary/20 shadow-lg">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Globe className="w-4 h-4 text-primary" />
            {t('autoLanguageDetection')}
            <Badge variant="secondary" className="text-xs">
              {t('smart')}
            </Badge>
          </CardTitle>
          <CardDescription className="text-xs">
            {t('languageDetectedMessage')}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-3">
            {/* معلومات اللغة المكتشفة */}
            <div className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2">
                <span className="text-lg">{getLanguageFlag(detectedLanguage)}</span>
                <div>
                  <p className="font-medium text-sm">{getLanguageName(detectedLanguage)}</p>
                  <p className="text-xs text-muted-foreground">{t('detectedLanguage')}</p>
                </div>
              </div>
              <Languages className="w-4 h-4 text-muted-foreground" />
            </div>

            {/* أزرار الإجراء */}
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={handleAcceptSuggestion}
                disabled={isChanging}
                className="flex-1"
              >
                <Check className="w-3 h-3 mr-1" />
                {isChanging ? t('changingLanguage') : t('changeLanguage')}
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={handleDismissSuggestion}
                disabled={isChanging}
              >
                <X className="w-3 h-3" />
              </Button>
            </div>

            {/* معلومات إضافية */}
            <p className="text-xs text-muted-foreground text-center">
              {t('languageChangeInfo')}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Hook لاستخدام مكون التعرف التلقائي على اللغة
 */
export function useAutoLanguageDetection(currentLocale: Locale) {
  const [shouldShowDetector, setShouldShowDetector] = useState(false);
  const [detectedLanguage, setDetectedLanguage] = useState<Locale | null>(null);

  useEffect(() => {
    const detected = detectUserLanguage();
    setDetectedLanguage(detected);

    // تحديد ما إذا كان يجب عرض المكون
    const shouldShow = detected !== currentLocale && 
                      !localStorage.getItem('language-suggestion-dismissed') &&
                      !localStorage.getItem('preferred-language');

    setShouldShowDetector(shouldShow);
  }, [currentLocale]);

  return {
    shouldShowDetector,
    detectedLanguage,
    setShouldShowDetector
  };
}
