'use client';

import { useAuth } from '@/context/AuthContext';
import { LoyaltyProgramManager } from '@/components/merchant/LoyaltyProgramManager';
import { Card, CardContent } from '@/components/ui/card';

export default function MerchantLoyaltyPage() {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <p>يرجى تسجيل الدخول للوصول إلى هذه الصفحة</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <LoyaltyProgramManager merchantId={user.uid} />
    </div>
  );
}
