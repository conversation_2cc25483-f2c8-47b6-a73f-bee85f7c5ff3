// src/lib/session-manager.ts
// 🔐 نظام إدارة الجلسات المتقدم - Apex Level

import { auth, db } from './firebase';
import { doc, setDoc, getDoc, updateDoc, deleteDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { ApexEncryptionEngine } from './encryption';
import { logUserAction, logSecurityViolation } from './audit-system';
import CryptoJS from 'crypto-js';

// ===== INTERFACES =====

export interface SessionData {
  sessionId: string;
  userId: string;
  deviceFingerprint: string;
  ipAddress: string;
  userAgent: string;
  location?: string;
  createdAt: Date;
  lastActivity: Date;
  expiresAt: Date;
  isActive: boolean;
  securityLevel: SecurityLevel;
  permissions: string[];
  metadata: {
    loginMethod: string;
    mfaVerified: boolean;
    riskScore: number;
    deviceTrusted: boolean;
  };
}

export interface DeviceInfo {
  fingerprint: string;
  name: string;
  type: 'desktop' | 'mobile' | 'tablet' | 'unknown';
  os: string;
  browser: string;
  trusted: boolean;
  firstSeen: Date;
  lastSeen: Date;
}

export interface SessionValidationResult {
  valid: boolean;
  reason?: string;
  requiresReauth?: boolean;
  securityAction?: string;
}

export enum SecurityLevel {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  MAXIMUM = 4
}

// ===== APEX SESSION MANAGER =====

export class ApexSessionManager {
  private static readonly SESSION_TIMEOUT = {
    [SecurityLevel.LOW]: 24 * 60 * 60 * 1000,      // 24 hours
    [SecurityLevel.MEDIUM]: 8 * 60 * 60 * 1000,    // 8 hours
    [SecurityLevel.HIGH]: 2 * 60 * 60 * 1000,      // 2 hours
    [SecurityLevel.MAXIMUM]: 30 * 60 * 1000        // 30 minutes
  };

  private static readonly MAX_CONCURRENT_SESSIONS = 5;
  private static readonly ACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private static readonly DEVICE_TRUST_PERIOD = 30 * 24 * 60 * 60 * 1000; // 30 days

  /**
   * إنشاء جلسة جديدة
   */
  static async createSession(
    userId: string,
    deviceInfo: Partial<DeviceInfo>,
    securityContext: {
      ipAddress: string;
      userAgent: string;
      location?: string;
      loginMethod: string;
      mfaVerified: boolean;
      riskScore: number;
    }
  ): Promise<SessionData> {
    try {
      // توليد معرف جلسة آمن
      const sessionId = this.generateSecureSessionId();
      
      // تحديد مستوى الأمان
      const securityLevel = this.determineSecurityLevel(securityContext.riskScore, securityContext.mfaVerified);
      
      // إنشاء بصمة الجهاز
      const deviceFingerprint = await this.generateDeviceFingerprint(deviceInfo, securityContext);
      
      // فحص الأجهزة الموثوقة
      const deviceTrusted = await this.isDeviceTrusted(userId, deviceFingerprint);
      
      // تحديد الصلاحيات
      const permissions = await this.determinePermissions(userId, securityLevel, deviceTrusted);
      
      // إنشاء بيانات الجلسة
      const sessionData: SessionData = {
        sessionId,
        userId,
        deviceFingerprint,
        ipAddress: this.hashIP(securityContext.ipAddress),
        userAgent: this.hashUserAgent(securityContext.userAgent),
        location: securityContext.location,
        createdAt: new Date(),
        lastActivity: new Date(),
        expiresAt: new Date(Date.now() + this.SESSION_TIMEOUT[securityLevel]),
        isActive: true,
        securityLevel,
        permissions,
        metadata: {
          loginMethod: securityContext.loginMethod,
          mfaVerified: securityContext.mfaVerified,
          riskScore: securityContext.riskScore,
          deviceTrusted
        }
      };

      // فحص الحد الأقصى للجلسات
      await this.enforceSessionLimit(userId);
      
      // حفظ الجلسة (مشفرة)
      await this.saveSession(sessionData);
      
      // تسجيل معلومات الجهاز
      await this.updateDeviceInfo(userId, deviceFingerprint, deviceInfo);
      
      // تسجيل الحدث
      await logUserAction('session_created', userId, {
        sessionId,
        securityLevel,
        deviceTrusted,
        mfaVerified: securityContext.mfaVerified
      });

      return sessionData;
    } catch (error) {
      console.error('🔴 خطأ في إنشاء الجلسة:', error);
      throw new Error('فشل في إنشاء الجلسة');
    }
  }

  /**
   * التحقق من صحة الجلسة
   */
  static async validateSession(sessionId: string): Promise<SessionValidationResult> {
    try {
      const sessionData = await this.getSession(sessionId);
      
      if (!sessionData) {
        return { valid: false, reason: 'جلسة غير موجودة' };
      }

      // فحص انتهاء الصلاحية
      if (sessionData.expiresAt < new Date()) {
        await this.invalidateSession(sessionId);
        return { 
          valid: false, 
          reason: 'انتهت صلاحية الجلسة',
          requiresReauth: true
        };
      }

      // فحص النشاط
      const inactivityPeriod = Date.now() - sessionData.lastActivity.getTime();
      if (inactivityPeriod > this.ACTIVITY_TIMEOUT) {
        await this.invalidateSession(sessionId);
        return { 
          valid: false, 
          reason: 'انتهت مهلة عدم النشاط',
          requiresReauth: true
        };
      }

      // فحص الحالة النشطة
      if (!sessionData.isActive) {
        return { 
          valid: false, 
          reason: 'الجلسة غير نشطة',
          requiresReauth: true
        };
      }

      // تحديث آخر نشاط
      await this.updateLastActivity(sessionId);

      return { valid: true };
    } catch (error) {
      console.error('🔴 خطأ في التحقق من الجلسة:', error);
      return { 
        valid: false, 
        reason: 'خطأ في التحقق من الجلسة',
        securityAction: 'force_logout'
      };
    }
  }

  /**
   * تحديث نشاط الجلسة
   */
  static async updateSessionActivity(
    sessionId: string,
    activityData: {
      ipAddress?: string;
      userAgent?: string;
      location?: string;
      action?: string;
    }
  ): Promise<void> {
    try {
      const sessionData = await this.getSession(sessionId);
      if (!sessionData) return;

      // فحص تغيير IP أو User Agent
      const ipChanged = activityData.ipAddress && 
        this.hashIP(activityData.ipAddress) !== sessionData.ipAddress;
      const userAgentChanged = activityData.userAgent && 
        this.hashUserAgent(activityData.userAgent) !== sessionData.userAgent;

      if (ipChanged || userAgentChanged) {
        // تسجيل تحذير أمني
        await logSecurityViolation('session_hijack_attempt', sessionData.userId, {
          sessionId,
          ipChanged,
          userAgentChanged,
          originalIP: sessionData.ipAddress,
          newIP: activityData.ipAddress ? this.hashIP(activityData.ipAddress) : undefined
        });

        // إجراءات أمنية
        if (sessionData.securityLevel >= SecurityLevel.HIGH) {
          await this.invalidateSession(sessionId);
          return;
        }
      }

      // تحديث النشاط
      await this.updateLastActivity(sessionId);
    } catch (error) {
      console.error('🔴 خطأ في تحديث نشاط الجلسة:', error);
    }
  }

  /**
   * إنهاء الجلسة
   */
  static async terminateSession(sessionId: string, reason: string = 'user_logout'): Promise<void> {
    try {
      const sessionData = await this.getSession(sessionId);
      if (!sessionData) return;

      // تحديث حالة الجلسة
      await this.invalidateSession(sessionId);

      // تسجيل الحدث
      await logUserAction('session_terminated', sessionData.userId, {
        sessionId,
        reason,
        duration: Date.now() - sessionData.createdAt.getTime()
      });
    } catch (error) {
      console.error('🔴 خطأ في إنهاء الجلسة:', error);
    }
  }

  /**
   * إنهاء جميع جلسات المستخدم
   */
  static async terminateAllUserSessions(userId: string, excludeSessionId?: string): Promise<number> {
    try {
      const userSessions = await this.getUserSessions(userId);
      let terminatedCount = 0;

      for (const session of userSessions) {
        if (session.sessionId !== excludeSessionId) {
          await this.invalidateSession(session.sessionId);
          terminatedCount++;
        }
      }

      // تسجيل الحدث
      await logUserAction('all_sessions_terminated', userId, {
        terminatedCount,
        excludedSession: excludeSessionId
      });

      return terminatedCount;
    } catch (error) {
      console.error('🔴 خطأ في إنهاء جميع الجلسات:', error);
      return 0;
    }
  }

  /**
   * الحصول على جلسات المستخدم النشطة
   */
  static async getUserActiveSessions(userId: string): Promise<SessionData[]> {
    try {
      const sessions = await this.getUserSessions(userId);
      return sessions.filter(session => 
        session.isActive && 
        session.expiresAt > new Date()
      );
    } catch (error) {
      console.error('🔴 خطأ في جلب الجلسات النشطة:', error);
      return [];
    }
  }

  // ===== PRIVATE METHODS =====

  private static generateSecureSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = CryptoJS.lib.WordArray.random(32).toString();
    return `sess_${timestamp}_${randomPart}`;
  }

  private static determineSecurityLevel(riskScore: number, mfaVerified: boolean): SecurityLevel {
    if (riskScore >= 0.8) return SecurityLevel.MAXIMUM;
    if (riskScore >= 0.6) return SecurityLevel.HIGH;
    if (riskScore >= 0.3 || !mfaVerified) return SecurityLevel.MEDIUM;
    return SecurityLevel.LOW;
  }

  private static async generateDeviceFingerprint(
    deviceInfo: Partial<DeviceInfo>,
    context: any
  ): Promise<string> {
    const fingerprintData = {
      userAgent: context.userAgent,
      screen: typeof window !== 'undefined' ? `${screen.width}x${screen.height}` : 'unknown',
      timezone: typeof window !== 'undefined' ? Intl.DateTimeFormat().resolvedOptions().timeZone : 'unknown',
      language: typeof window !== 'undefined' ? navigator.language : 'unknown',
      platform: deviceInfo.os || 'unknown'
    };

    const fingerprintString = JSON.stringify(fingerprintData);
    return CryptoJS.SHA256(fingerprintString).toString();
  }

  private static async isDeviceTrusted(userId: string, deviceFingerprint: string): Promise<boolean> {
    try {
      const deviceRef = doc(db, 'user_devices', `${userId}_${deviceFingerprint}`);
      const deviceSnap = await getDoc(deviceRef);
      
      if (!deviceSnap.exists()) return false;
      
      const deviceData = deviceSnap.data();
      const trustExpiry = deviceData.lastSeen.toDate().getTime() + this.DEVICE_TRUST_PERIOD;
      
      return deviceData.trusted && Date.now() < trustExpiry;
    } catch (error) {
      console.error('🔴 خطأ في فحص ثقة الجهاز:', error);
      return false;
    }
  }

  private static async determinePermissions(
    userId: string,
    securityLevel: SecurityLevel,
    deviceTrusted: boolean
  ): Promise<string[]> {
    const basePermissions = ['read_profile', 'update_profile'];
    
    if (deviceTrusted && securityLevel <= SecurityLevel.MEDIUM) {
      basePermissions.push('make_purchases', 'view_orders');
    }
    
    if (securityLevel <= SecurityLevel.LOW) {
      basePermissions.push('admin_actions');
    }
    
    return basePermissions;
  }

  private static async enforceSessionLimit(userId: string): Promise<void> {
    const activeSessions = await this.getUserActiveSessions(userId);
    
    if (activeSessions.length >= this.MAX_CONCURRENT_SESSIONS) {
      // إنهاء أقدم جلسة
      const oldestSession = activeSessions.sort((a, b) => 
        a.lastActivity.getTime() - b.lastActivity.getTime()
      )[0];
      
      await this.invalidateSession(oldestSession.sessionId);
    }
  }

  private static async saveSession(sessionData: SessionData): Promise<void> {
    try {
      const encryptedSession = await ApexEncryptionEngine.encryptWithPFS(sessionData);
      const sessionRef = doc(db, 'user_sessions', sessionData.sessionId);
      await setDoc(sessionRef, {
        userId: sessionData.userId,
        encryptedData: encryptedSession,
        createdAt: sessionData.createdAt,
        expiresAt: sessionData.expiresAt,
        isActive: sessionData.isActive
      });
    } catch (error) {
      console.error('🔴 خطأ في حفظ الجلسة:', error);
      throw error;
    }
  }

  private static async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      const sessionRef = doc(db, 'user_sessions', sessionId);
      const sessionSnap = await getDoc(sessionRef);
      
      if (!sessionSnap.exists()) return null;
      
      const data = sessionSnap.data();
      return await ApexEncryptionEngine.decryptWithVerification(data.encryptedData);
    } catch (error) {
      console.error('🔴 خطأ في جلب الجلسة:', error);
      return null;
    }
  }

  private static async getUserSessions(userId: string): Promise<SessionData[]> {
    try {
      const sessionsQuery = query(
        collection(db, 'user_sessions'),
        where('userId', '==', userId),
        where('isActive', '==', true)
      );
      
      const querySnapshot = await getDocs(sessionsQuery);
      const sessions: SessionData[] = [];
      
      for (const doc of querySnapshot.docs) {
        try {
          const data = doc.data();
          const sessionData = await ApexEncryptionEngine.decryptWithVerification(data.encryptedData);
          sessions.push(sessionData);
        } catch (error) {
          console.error('🔴 خطأ في فك تشفير جلسة:', error);
        }
      }
      
      return sessions;
    } catch (error) {
      console.error('🔴 خطأ في جلب جلسات المستخدم:', error);
      return [];
    }
  }

  private static async invalidateSession(sessionId: string): Promise<void> {
    try {
      const sessionRef = doc(db, 'user_sessions', sessionId);
      await updateDoc(sessionRef, {
        isActive: false,
        terminatedAt: new Date()
      });
    } catch (error) {
      console.error('🔴 خطأ في إلغاء الجلسة:', error);
    }
  }

  private static async updateLastActivity(sessionId: string): Promise<void> {
    try {
      const sessionData = await this.getSession(sessionId);
      if (!sessionData) return;
      
      sessionData.lastActivity = new Date();
      await this.saveSession(sessionData);
    } catch (error) {
      console.error('🔴 خطأ في تحديث آخر نشاط:', error);
    }
  }

  private static async updateDeviceInfo(
    userId: string,
    deviceFingerprint: string,
    deviceInfo: Partial<DeviceInfo>
  ): Promise<void> {
    try {
      const deviceRef = doc(db, 'user_devices', `${userId}_${deviceFingerprint}`);
      const deviceSnap = await getDoc(deviceRef);
      
      const deviceData: DeviceInfo = {
        fingerprint: deviceFingerprint,
        name: deviceInfo.name || 'Unknown Device',
        type: deviceInfo.type || 'unknown',
        os: deviceInfo.os || 'Unknown OS',
        browser: deviceInfo.browser || 'Unknown Browser',
        trusted: deviceSnap.exists() ? deviceSnap.data().trusted : false,
        firstSeen: deviceSnap.exists() ? deviceSnap.data().firstSeen.toDate() : new Date(),
        lastSeen: new Date()
      };
      
      await setDoc(deviceRef, deviceData);
    } catch (error) {
      console.error('🔴 خطأ في تحديث معلومات الجهاز:', error);
    }
  }

  private static hashIP(ip: string): string {
    return CryptoJS.SHA256(ip + 'ip_salt').toString().substring(0, 16);
  }

  private static hashUserAgent(userAgent: string): string {
    return CryptoJS.SHA256(userAgent + 'ua_salt').toString().substring(0, 16);
  }
}

export default ApexSessionManager;
