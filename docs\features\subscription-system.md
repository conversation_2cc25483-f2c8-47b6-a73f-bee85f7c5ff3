# 💳 نظام الاشتراكات - مِخْلاة

## 🎯 نظرة عامة

نظام اشتراكات متكامل وشامل يدعم جميع أنواع المستخدمين في منصة مِخْلاة مع تكامل كامل مع أنظمة الدفع الإلكتروني.

## ✨ الميزات الأساسية

### 📊 **أنواع الخطط المدعومة**

#### 🛒 **خطط العملاء**
- **الخطة الأساسية** (مجانية)
  - تصفح جميع المنتجات والمتاجر
  - إنشاء طلبات غير محدودة
  - تتبع الطلبات الأساسي
  - تقييم المنتجات والمتاجر

- **الخطة المميزة** (9.99 ريال/شهر)
  - شحن مجاني غير محدود
  - خصم 20% على جميع المشتريات
  - دعم أولوية 24/7
  - طلبات ذات أولوية
  - عروض حصرية

#### 🏪 **خطط التجار**
- **التاجر الأساسي** (مجانية)
  - إدارة متجر أساسية
  - إضافة وإدارة المنتجات
  - دعم عبر البريد الإلكتروني
  - لوحة تحكم بسيطة

- **التاجر المميز** (99.99 ريال/شهر)
  - منتجات غير محدودة
  - دعم أولوية 24/7
  - لوحة تحكم متقدمة
  - تخصيص كامل للمتجر
  - تحليلات مبيعات متقدمة

- **تاجر الأعمال** (199.99 ريال/شهر)
  - جميع ميزات الخطة المميزة
  - دعم VIP مع مدير حساب مخصص
  - تحليلات ذكية وتنبؤات
  - تكامل ERP و POS
  - نظام CRM متكامل

#### 🚚 **خطط المندوبين**
- **الخطة الأساسية** (مجانية)
  - توصيلات غير محدودة
  - دعم أساسي
  - إحصائيات أساسية
  - أولوية عادية

- **الخطة المميزة** (29.99 ريال/شهر)
  - جميع ميزات الخطة الأساسية
  - أولوية عالية في الطلبات
  - إحصائيات متقدمة
  - تقارير مفصلة
  - دعم أولوية

### 🔄 **تدفق الاشتراك**

#### **للخطط المجانية**
```
صفحة الأسعار → النقر على "البدء" → تسجيل الدخول (إذا لزم) → 
صفحة التفعيل → النقر على "تفعيل الآن" → صفحة النجاح
```

#### **للخطط المدفوعة**
```
صفحة الأسعار → النقر على "اختيار الخطة" → تسجيل الدخول (إذا لزم) → 
صفحة الدفع → إتمام الدفع → صفحة النجاح
```

## 🛠️ التطبيق التقني

### 📁 **هيكل الملفات**

```
src/
├── app/[locale]/subscription/
│   ├── page.tsx              # صفحة الاشتراك الرئيسية
│   ├── activate/page.tsx     # تفعيل الخطط المجانية
│   ├── success/page.tsx      # صفحة النجاح
│   └── cancel/page.tsx       # صفحة الإلغاء
├── services/
│   └── subscriptionService.ts # خدمة إدارة الاشتراكات
├── hooks/
│   └── useSubscription.ts     # Hook للاشتراكات
└── components/pricing/
    ├── PricingTabs.tsx        # تبويبات الأسعار
    └── SubscriptionCard.tsx   # بطاقة الاشتراك
```

### 🗄️ **قاعدة البيانات**

#### **مجموعة الاشتراكات**
- `subscriptions`: معلومات الاشتراكات النشطة
- `subscription_history`: تاريخ جميع عمليات الاشتراك

#### **الحقول الأساسية**
- `userId`: معرف المستخدم
- `planId`: معرف الخطة
- `status`: حالة الاشتراك (active, cancelled, expired, pending)
- `paymentId`: معرف الدفعة (للخطط المدفوعة)
- `startDate`: تاريخ بداية الاشتراك
- `autoRenew`: التجديد التلقائي

### 🔧 **الـ APIs المستخدمة**

#### **خدمة الاشتراكات**
```typescript
// إنشاء اشتراك جديد
createSubscription(userId, planId, planType, paymentId?, transactionId?, amount?)

// تفعيل اشتراك
activateSubscription(subscriptionId, transactionId?)

// إلغاء اشتراك
cancelSubscription(subscriptionId, reason?)

// جلب الاشتراك النشط
getUserActiveSubscription(userId)

// التحقق من صحة الاشتراك
validateSubscription(userId, requiredPlanType?)
```

#### **Hook الاشتراكات**
```typescript
const {
  activeSubscription,    // الاشتراك النشط
  currentPlan,          // تفاصيل الخطة الحالية
  loading,              // حالة التحميل
  createSubscription,   // إنشاء اشتراك
  activateSubscription, // تفعيل اشتراك
  cancelSubscription    // إلغاء اشتراك
} = useSubscription();
```

## 💰 نظام الدفع

### 🔗 **التكامل مع أنظمة الدفع**
- **PayPal**: دفع دولي آمن
- **البطاقات الائتمانية**: Visa, MasterCard, American Express
- **المحافظ الرقمية**: Apple Pay, Google Pay
- **الدفع المحلي**: STC Pay, مدى

### 🛡️ **الأمان**
- تشفير جميع معاملات الدفع
- عدم تخزين بيانات البطاقات
- استخدام HTTPS لجميع الاتصالات
- التحقق من صحة جميع المدخلات

## 🌐 الدعم متعدد اللغات

### 🔤 **الترجمات المدعومة**
- **العربية**: الترجمة الأساسية
- **الإنجليزية**: ترجمة كاملة ومتسقة

### 📝 **المفاتيح الجديدة**
- `subscriptionCheckout`: إتمام الاشتراك
- `activatePlan`: تفعيل الخطة
- `paymentDetails`: تفاصيل الدفع
- `subscriptionSuccessful`: تم الاشتراك بنجاح
- `planActivatedSuccessfully`: تم تفعيل الخطة بنجاح

## 🧪 الاختبارات

### 📋 **اختبارات Cypress**
- اختبار عرض صفحة الأسعار
- اختبار أزرار الاشتراك
- اختبار تدفق الاشتراك المجاني
- اختبار تدفق الاشتراك المدفوع
- اختبار صفحات النجاح والإلغاء
- اختبار جميع أنواع الخطط

### ✅ **معايير النجاح**
- جميع الأزرار تعمل بشكل صحيح
- التوجيه يتم للصفحات الصحيحة
- الدفع يتم معالجته بنجاح
- الاشتراكات تُحفظ في قاعدة البيانات
- الترجمات تظهر بشكل صحيح

## 🚀 الاستخدام

### 👨‍💻 **للمطورين**

#### **إضافة خطة جديدة**
1. أضف الخطة في `src/constants/plans.ts`
2. أضف الترجمات في ملفات اللغات
3. اختبر الخطة في صفحة الأسعار

#### **تخصيص تدفق الدفع**
1. عدّل `AdvancedPaymentForm` حسب الحاجة
2. أضف طرق دفع جديدة في `paymentService`
3. اختبر التكامل مع بوابات الدفع

### 👤 **للمستخدمين**

#### **كيفية الاشتراك**
1. اذهب لصفحة الأسعار
2. اختر نوع المستخدم (عميل/تاجر/مندوب)
3. اختر الخطة المناسبة
4. أكمل عملية الدفع (للخطط المدفوعة)
5. استمتع بالميزات الجديدة

## 📈 الإحصائيات

### 📊 **معلومات التطوير**
- **الملفات الجديدة**: 6 ملفات
- **الملفات المحدثة**: 3 ملفات
- **الترجمات الجديدة**: 50+ مفتاح
- **الاختبارات**: 10+ سيناريو
- **وقت التطوير**: ~4 ساعات

### 🎯 **معدلات النجاح المتوقعة**
- **الخطط المجانية**: 95%+ معدل تفعيل
- **الخطط المدفوعة**: 85%+ معدل إتمام الدفع
- **رضا المستخدمين**: 90%+ معدل رضا متوقع

## 🔮 التطوير المستقبلي

### 📅 **الميزات المخططة**
- نظام الترقية والتراجع بين الخطط
- الفواتير التلقائية والتذكيرات
- نظام الخصومات والعروض الترويجية
- تقارير استخدام مفصلة
- API للتكامل مع أنظمة خارجية

### 🔧 **التحسينات التقنية**
- تحسين أداء قاعدة البيانات
- إضافة المزيد من طرق الدفع المحلية
- تحسين واجهة المستخدم للجوال
- إضافة إشعارات فورية
- نظام النسخ الاحتياطي المتقدم

---

**تم تطوير هذا النظام بواسطة فريق مِخْلاة - 26 يونيو 2025**
