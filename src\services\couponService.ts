import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc,
  getDoc,
  getDocs,
  query, 
  where, 
  orderBy,
  limit,
  startAfter,
  increment,
  serverTimestamp,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { 
  CouponDocument, 
  CouponValidation, 
  DiscountResult, 
  CreateCouponData,
  UpdateCouponData,
  CartData,
  CouponAnalytics,
  CouponUsageLog,
  CouponErrorCode,
  CouponFilters,
  CouponSortOptions,
  CouponsResponse
} from '@/types/coupon';

export class CouponService {
  private couponsCollection = collection(db, 'coupons');
  private usageLogsCollection = collection(db, 'coupon_usage_logs');

  /**
   * إنشاء كوبون جديد
   */
  async createCoupon(merchantId: string, couponData: CreateCouponData): Promise<string> {
    try {
      // التحقق من عدم وجود كوبون بنفس الكود
      const existingCoupon = await this.getCouponByCode(couponData.code);
      if (existingCoupon) {
        throw new Error('كود الكوبون موجود بالفعل');
      }

      const newCoupon: Omit<CouponDocument, 'id'> = {
        ...couponData,
        merchantId,
        usedCount: 0,
        isActive: true,
        validFrom: Timestamp.fromDate(couponData.validFrom),
        validUntil: Timestamp.fromDate(couponData.validUntil),
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
        createdBy: merchantId,
      };

      const docRef = await addDoc(this.couponsCollection, newCoupon);
      return docRef.id;
    } catch (error) {
      console.error('Error creating coupon:', error);
      throw new Error('فشل في إنشاء الكوبون');
    }
  }

  /**
   * تحديث كوبون موجود
   */
  async updateCoupon(couponId: string, updateData: UpdateCouponData): Promise<void> {
    try {
      const couponRef = doc(this.couponsCollection, couponId);
      
      const updatePayload: any = {
        ...updateData,
        updatedAt: serverTimestamp(),
      };

      // تحويل التواريخ إذا كانت موجودة
      if (updateData.validFrom) {
        updatePayload.validFrom = Timestamp.fromDate(updateData.validFrom);
      }
      if (updateData.validUntil) {
        updatePayload.validUntil = Timestamp.fromDate(updateData.validUntil);
      }

      await updateDoc(couponRef, updatePayload);
    } catch (error) {
      console.error('Error updating coupon:', error);
      throw new Error('فشل في تحديث الكوبون');
    }
  }

  /**
   * حذف كوبون
   */
  async deleteCoupon(couponId: string): Promise<void> {
    try {
      const couponRef = doc(this.couponsCollection, couponId);
      await deleteDoc(couponRef);
    } catch (error) {
      console.error('Error deleting coupon:', error);
      throw new Error('فشل في حذف الكوبون');
    }
  }

  /**
   * الحصول على كوبون بالكود
   */
  async getCouponByCode(code: string): Promise<CouponDocument | null> {
    try {
      const q = query(this.couponsCollection, where('code', '==', code));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return { id: doc.id, ...doc.data() } as CouponDocument;
    } catch (error) {
      console.error('Error getting coupon by code:', error);
      return null;
    }
  }

  /**
   * الحصول على كوبون بالمعرف
   */
  async getCouponById(couponId: string): Promise<CouponDocument | null> {
    try {
      const couponRef = doc(this.couponsCollection, couponId);
      const couponDoc = await getDoc(couponRef);
      
      if (!couponDoc.exists()) {
        return null;
      }

      return { id: couponDoc.id, ...couponDoc.data() } as CouponDocument;
    } catch (error) {
      console.error('Error getting coupon by ID:', error);
      return null;
    }
  }

  /**
   * التحقق من صحة الكوبون
   */
  async validateCoupon(code: string, cartData: CartData): Promise<CouponValidation> {
    try {
      const coupon = await this.getCouponByCode(code);
      
      if (!coupon) {
        return {
          isValid: false,
          discount: 0,
          message: 'كوبون غير صالح',
          errorCode: CouponErrorCode.INVALID_CODE
        };
      }

      // التحقق من أن الكوبون نشط
      if (!coupon.isActive) {
        return {
          isValid: false,
          discount: 0,
          message: 'الكوبون غير نشط',
          errorCode: CouponErrorCode.INACTIVE
        };
      }

      // التحقق من التاجر
      if (coupon.merchantId !== cartData.merchantId) {
        return {
          isValid: false,
          discount: 0,
          message: 'الكوبون غير صالح لهذا المتجر',
          errorCode: CouponErrorCode.INVALID_CODE
        };
      }

      // التحقق من تاريخ البداية
      if (new Date() < coupon.validFrom.toDate()) {
        return {
          isValid: false,
          discount: 0,
          message: 'لم يحن وقت استخدام الكوبون بعد',
          errorCode: CouponErrorCode.NOT_STARTED
        };
      }

      // التحقق من تاريخ الانتهاء
      if (new Date() > coupon.validUntil.toDate()) {
        return {
          isValid: false,
          discount: 0,
          message: 'انتهت صلاحية الكوبون',
          errorCode: CouponErrorCode.EXPIRED
        };
      }

      // التحقق من حد الاستخدام
      if (coupon.usedCount >= coupon.usageLimit) {
        return {
          isValid: false,
          discount: 0,
          message: 'تم استنفاد استخدامات الكوبون',
          errorCode: CouponErrorCode.USAGE_LIMIT_REACHED
        };
      }

      // التحقق من الحد الأدنى للطلب
      if (coupon.minOrderAmount && cartData.totalAmount < coupon.minOrderAmount) {
        return {
          isValid: false,
          discount: 0,
          message: `الحد الأدنى للطلب ${coupon.minOrderAmount} ريال`,
          errorCode: CouponErrorCode.MIN_ORDER_NOT_MET
        };
      }

      // التحقق من المنتجات المطبقة
      if (coupon.applicableProducts && coupon.applicableProducts.length > 0) {
        const hasApplicableProduct = cartData.items.some(item => 
          coupon.applicableProducts!.includes(item.productId)
        );
        if (!hasApplicableProduct) {
          return {
            isValid: false,
            discount: 0,
            message: 'الكوبون غير صالح للمنتجات في السلة',
            errorCode: CouponErrorCode.PRODUCT_NOT_APPLICABLE
          };
        }
      }

      // التحقق من الفئات المطبقة
      if (coupon.applicableCategories && coupon.applicableCategories.length > 0) {
        const hasApplicableCategory = cartData.items.some(item => 
          coupon.applicableCategories!.includes(item.categoryId)
        );
        if (!hasApplicableCategory) {
          return {
            isValid: false,
            discount: 0,
            message: 'الكوبون غير صالح لفئات المنتجات في السلة',
            errorCode: CouponErrorCode.PRODUCT_NOT_APPLICABLE
          };
        }
      }

      // حساب الخصم
      const discountResult = this.calculateDiscount(coupon, cartData);

      return {
        isValid: true,
        discount: discountResult.discountAmount,
        message: 'تم تطبيق الكوبون بنجاح',
        coupon,
        freeShipping: coupon.type === 'free_shipping'
      };
    } catch (error) {
      console.error('Error validating coupon:', error);
      return {
        isValid: false,
        discount: 0,
        message: 'خطأ في التحقق من الكوبون'
      };
    }
  }

  /**
   * حساب الخصم
   */
  private calculateDiscount(coupon: CouponDocument, cartData: CartData): DiscountResult {
    let discountAmount = 0;
    const originalAmount = cartData.totalAmount;

    if (coupon.type === 'percentage') {
      discountAmount = (originalAmount * coupon.value) / 100;
      if (coupon.maxDiscount && discountAmount > coupon.maxDiscount) {
        discountAmount = coupon.maxDiscount;
      }
    } else if (coupon.type === 'fixed') {
      discountAmount = Math.min(coupon.value, originalAmount);
    } else if (coupon.type === 'free_shipping') {
      discountAmount = 0; // يتم التعامل مع الشحن المجاني بشكل منفصل
    }

    return {
      originalAmount,
      discountAmount,
      finalAmount: originalAmount - discountAmount,
      couponCode: coupon.code,
      freeShipping: coupon.type === 'free_shipping',
      appliedAt: serverTimestamp() as Timestamp
    };
  }

  /**
   * تطبيق الكوبون وتسجيل الاستخدام
   */
  async applyCoupon(couponId: string, orderId: string, customerId: string, discountResult: DiscountResult): Promise<void> {
    try {
      const batch = writeBatch(db);

      // تحديث عداد الاستخدام
      const couponRef = doc(this.couponsCollection, couponId);
      batch.update(couponRef, {
        usedCount: increment(1),
        updatedAt: serverTimestamp()
      });

      // تسجيل الاستخدام
      const usageLog: Omit<CouponUsageLog, 'id'> = {
        couponId,
        customerId,
        orderId,
        discountAmount: discountResult.discountAmount,
        originalAmount: discountResult.originalAmount,
        finalAmount: discountResult.finalAmount,
        usedAt: serverTimestamp() as Timestamp,
        merchantId: '', // سيتم تحديثه من السياق
      };

      const usageLogRef = doc(this.usageLogsCollection);
      batch.set(usageLogRef, usageLog);

      await batch.commit();
    } catch (error) {
      console.error('Error applying coupon:', error);
      throw new Error('فشل في تطبيق الكوبون');
    }
  }

  /**
   * الحصول على كوبونات التاجر مع الفلترة والترتيب
   */
  async getMerchantCoupons(
    merchantId: string,
    filters?: CouponFilters,
    sortOptions?: CouponSortOptions,
    page: number = 1,
    pageLimit: number = 20
  ): Promise<CouponsResponse> {
    try {
      let q = query(
        this.couponsCollection,
        where('merchantId', '==', merchantId)
      );

      // تطبيق الفلاتر
      if (filters) {
        if (filters.type && filters.type.length > 0) {
          q = query(q, where('type', 'in', filters.type));
        }

        if (filters.dateRange) {
          q = query(q,
            where('createdAt', '>=', Timestamp.fromDate(filters.dateRange.start)),
            where('createdAt', '<=', Timestamp.fromDate(filters.dateRange.end))
          );
        }
      }

      // تطبيق الترتيب
      if (sortOptions) {
        q = query(q, orderBy(sortOptions.field, sortOptions.direction));
      } else {
        q = query(q, orderBy('createdAt', 'desc'));
      }

      // تطبيق التصفح
      q = query(q, limit(pageLimit));
      if (page > 1) {
        // للتصفح، نحتاج للحصول على آخر مستند من الصفحة السابقة
        // هذا تطبيق مبسط، في التطبيق الحقيقي نحتاج لحفظ cursor
        const skipCount = (page - 1) * pageLimit;
        const skipQuery = query(
          this.couponsCollection,
          where('merchantId', '==', merchantId),
          orderBy('createdAt', 'desc'),
          limit(skipCount)
        );
        const skipSnapshot = await getDocs(skipQuery);
        if (!skipSnapshot.empty) {
          const lastDoc = skipSnapshot.docs[skipSnapshot.docs.length - 1];
          q = query(q, startAfter(lastDoc));
        }
      }

      const querySnapshot = await getDocs(q);
      const coupons: CouponDocument[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CouponDocument));

      // تطبيق فلاتر إضافية في الذاكرة
      let filteredCoupons = coupons;
      if (filters) {
        if (filters.searchTerm) {
          const searchTerm = filters.searchTerm.toLowerCase();
          filteredCoupons = filteredCoupons.filter(coupon =>
            coupon.code.toLowerCase().includes(searchTerm) ||
            coupon.description?.toLowerCase().includes(searchTerm)
          );
        }

        if (filters.status && filters.status.length > 0) {
          filteredCoupons = filteredCoupons.filter(coupon => {
            const status = this.getCouponStatus(coupon);
            return filters.status!.includes(status);
          });
        }

        if (filters.usageRange) {
          filteredCoupons = filteredCoupons.filter(coupon =>
            coupon.usedCount >= (filters.usageRange!.min || 0) &&
            coupon.usedCount <= (filters.usageRange!.max || Infinity)
          );
        }
      }

      return {
        coupons: filteredCoupons,
        total: filteredCoupons.length,
        page,
        limit: pageLimit,
        hasMore: querySnapshot.docs.length === pageLimit
      };
    } catch (error) {
      console.error('Error getting merchant coupons:', error);
      throw new Error('فشل في جلب الكوبونات');
    }
  }

  /**
   * تحديد حالة الكوبون
   */
  private getCouponStatus(coupon: CouponDocument): 'active' | 'inactive' | 'expired' | 'used_up' {
    if (!coupon.isActive) return 'inactive';
    if (new Date() > coupon.validUntil.toDate()) return 'expired';
    if (coupon.usedCount >= coupon.usageLimit) return 'used_up';
    return 'active';
  }

  /**
   * الحصول على إحصائيات الكوبونات
   */
  async getCouponAnalytics(merchantId: string, dateRange?: { start: Date; end: Date }): Promise<CouponAnalytics> {
    try {
      // الحصول على جميع كوبونات التاجر
      let couponsQuery = query(
        this.couponsCollection,
        where('merchantId', '==', merchantId)
      );

      if (dateRange) {
        couponsQuery = query(couponsQuery,
          where('createdAt', '>=', Timestamp.fromDate(dateRange.start)),
          where('createdAt', '<=', Timestamp.fromDate(dateRange.end))
        );
      }

      const couponsSnapshot = await getDocs(couponsQuery);
      const coupons: CouponDocument[] = couponsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CouponDocument));

      // الحصول على سجلات الاستخدام
      let usageQuery = query(
        this.usageLogsCollection,
        where('merchantId', '==', merchantId)
      );

      if (dateRange) {
        usageQuery = query(usageQuery,
          where('usedAt', '>=', Timestamp.fromDate(dateRange.start)),
          where('usedAt', '<=', Timestamp.fromDate(dateRange.end))
        );
      }

      const usageSnapshot = await getDocs(usageQuery);
      const usageLogs: CouponUsageLog[] = usageSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CouponUsageLog));

      // حساب الإحصائيات
      const totalCoupons = coupons.length;
      const activeCoupons = coupons.filter(c => this.getCouponStatus(c) === 'active').length;
      const expiredCoupons = coupons.filter(c => this.getCouponStatus(c) === 'expired').length;
      const totalUsage = usageLogs.length;
      const totalDiscount = usageLogs.reduce((sum, log) => sum + log.discountAmount, 0);
      const averageDiscount = totalUsage > 0 ? totalDiscount / totalUsage : 0;

      // إحصائيات حسب النوع
      const usageByType = coupons.reduce((acc, coupon) => {
        acc[coupon.type] = (acc[coupon.type] || 0) + coupon.usedCount;
        return acc;
      }, {} as Record<string, number>);

      // أفضل الكوبونات أداءً
      const topPerformingCoupons = coupons
        .map(coupon => ({
          couponId: coupon.id,
          code: coupon.code,
          usageCount: coupon.usedCount,
          totalDiscount: usageLogs
            .filter(log => log.couponId === coupon.id)
            .reduce((sum, log) => sum + log.discountAmount, 0),
          conversionRate: coupon.usageLimit > 0 ? (coupon.usedCount / coupon.usageLimit) * 100 : 0
        }))
        .sort((a, b) => b.usageCount - a.usageCount)
        .slice(0, 10);

      // الاستخدام الشهري
      const usageByMonth = this.groupUsageByMonth(usageLogs);

      return {
        totalCoupons,
        activeCoupons,
        expiredCoupons,
        totalUsage,
        totalDiscount,
        averageDiscount,
        topPerformingCoupons,
        usageByType,
        usageByMonth
      };
    } catch (error) {
      console.error('Error getting coupon analytics:', error);
      throw new Error('فشل في جلب إحصائيات الكوبونات');
    }
  }

  /**
   * تجميع الاستخدام حسب الشهر
   */
  private groupUsageByMonth(usageLogs: CouponUsageLog[]) {
    const monthlyData: Record<string, { usageCount: number; totalDiscount: number }> = {};

    usageLogs.forEach(log => {
      const date = log.usedAt.toDate();
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { usageCount: 0, totalDiscount: 0 };
      }

      monthlyData[monthKey].usageCount++;
      monthlyData[monthKey].totalDiscount += log.discountAmount;
    });

    return Object.entries(monthlyData).map(([monthKey, data]) => {
      const [year, month] = monthKey.split('-');
      return {
        month,
        year: parseInt(year),
        usageCount: data.usageCount,
        totalDiscount: data.totalDiscount
      };
    }).sort((a, b) => {
      if (a.year !== b.year) return a.year - b.year;
      return parseInt(a.month) - parseInt(b.month);
    });
  }

  /**
   * تفعيل/إلغاء تفعيل كوبون
   */
  async toggleCouponStatus(couponId: string): Promise<void> {
    try {
      const coupon = await this.getCouponById(couponId);
      if (!coupon) {
        throw new Error('الكوبون غير موجود');
      }

      await this.updateCoupon(couponId, {
        isActive: !coupon.isActive
      });
    } catch (error) {
      console.error('Error toggling coupon status:', error);
      throw new Error('فشل في تغيير حالة الكوبون');
    }
  }

  /**
   * نسخ كوبون
   */
  async duplicateCoupon(couponId: string, newCode: string): Promise<string> {
    try {
      const originalCoupon = await this.getCouponById(couponId);
      if (!originalCoupon) {
        throw new Error('الكوبون الأصلي غير موجود');
      }

      const duplicateData: CreateCouponData = {
        code: newCode,
        type: originalCoupon.type,
        value: originalCoupon.value,
        minOrderAmount: originalCoupon.minOrderAmount,
        maxDiscount: originalCoupon.maxDiscount,
        usageLimit: originalCoupon.usageLimit,
        validFrom: new Date(),
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 يوم من الآن
        applicableProducts: originalCoupon.applicableProducts,
        applicableCategories: originalCoupon.applicableCategories,
        excludedProducts: originalCoupon.excludedProducts,
        excludedCategories: originalCoupon.excludedCategories,
        customerRestrictions: originalCoupon.customerRestrictions,
        description: `نسخة من ${originalCoupon.description || originalCoupon.code}`,
        internalNotes: originalCoupon.internalNotes
      };

      return await this.createCoupon(originalCoupon.merchantId, duplicateData);
    } catch (error) {
      console.error('Error duplicating coupon:', error);
      throw new Error('فشل في نسخ الكوبون');
    }
  }
}

export const couponService = new CouponService();
