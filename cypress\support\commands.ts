/// <reference types="cypress" />

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// أوامر مخصصة للمشروع

// تسجيل دخول وهمي
Cypress.Commands.add('mockLogin', (userType: 'customer' | 'merchant' | 'representative' = 'customer') => {
  cy.window().then((win) => {
    // Mock user data
    const mockUser = {
      uid: `test-${userType}-uid`,
      email: `test-${userType}@example.com`,
      displayName: `Test ${userType}`,
      emailVerified: true
    }
    
    // Store in localStorage to simulate authentication
    win.localStorage.setItem('mockUser', JSON.stringify(mockUser))
    win.localStorage.setItem('userType', userType)
  })
})

// تسجيل خروج وهمي
Cypress.Commands.add('mockLogout', () => {
  cy.window().then((win) => {
    win.localStorage.removeItem('mockUser')
    win.localStorage.removeItem('userType')
  })
})

// أوامر مخصصة لاختبارات التحليلات
Cypress.Commands.add('loadAnalyticsTestData', () => {
  cy.fixture('analytics-test-data').then((data) => {
    cy.window().then((win) => {
      win.localStorage.setItem('mockSalesReport', JSON.stringify(data.mockSalesReport))
      win.localStorage.setItem('mockKPIData', JSON.stringify(data.mockKPIData))
      win.localStorage.setItem('mockCustomerBehavior', JSON.stringify(data.mockCustomerBehavior))
    })
  })
})

// تنظيف بيانات اختبار التحليلات
Cypress.Commands.add('cleanAnalyticsTestData', () => {
  cy.window().then((win) => {
    win.localStorage.removeItem('mockSalesReport')
    win.localStorage.removeItem('mockKPIData')
    win.localStorage.removeItem('mockCustomerBehavior')
    win.localStorage.removeItem('mockOrders')
    win.localStorage.removeItem('mockProducts')
    win.localStorage.removeItem('mockCustomers')
    win.localStorage.removeItem('mockReviews')
  })
})

// انتظار تحميل مؤشرات الأداء
Cypress.Commands.add('waitForKPILoad', () => {
  cy.get('[data-testid="kpi-dashboard"]').should('be.visible')
  cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 15000 })
  cy.get('[data-testid="kpi-value"]').should('have.length.at.least', 4)
})

// انتظار تحميل الرسوم البيانية
Cypress.Commands.add('waitForChartsLoad', () => {
  cy.get('[data-testid="advanced-charts-panel"]').should('be.visible')
  cy.get('[data-testid="chart-container"]').should('be.visible')
  cy.get('[data-testid="chart-loading"]').should('not.exist', { timeout: 10000 })
})

// التحقق من صحة بيانات KPI
Cypress.Commands.add('validateKPIData', () => {
  cy.get('[data-testid="kpi-revenue"]').within(() => {
    cy.get('[data-testid="kpi-value"]').should('contain.text', 'ريال')
    cy.get('[data-testid="kpi-growth"]').should('be.visible')
    cy.get('[data-testid="kpi-progress"]').should('be.visible')
  })

  cy.get('[data-testid="kpi-orders"]').within(() => {
    cy.get('[data-testid="kpi-value"]').should('match', /\d+/)
    cy.get('[data-testid="kpi-growth"]').should('be.visible')
  })

  cy.get('[data-testid="kpi-customers"]').within(() => {
    cy.get('[data-testid="kpi-value"]').should('match', /\d+/)
    cy.get('[data-testid="kpi-growth"]').should('be.visible')
  })
})

// اختبار تغيير الفترة الزمنية
Cypress.Commands.add('testTimeRangeChange', (range: '7d' | '30d' | '90d' | '1y') => {
  const rangeLabels = {
    '7d': '7 أيام',
    '30d': '30 يوم',
    '90d': '90 يوم',
    '1y': 'سنة'
  }

  cy.get('[data-testid="time-range-selector"]').click()
  cy.contains(rangeLabels[range]).click()
  cy.get('[data-testid="loading-indicator"]').should('be.visible')
  cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 10000 })
})

// اختبار تغيير نوع الرسم البياني
Cypress.Commands.add('testChartTypeChange', (chartType: 'line' | 'area' | 'bar' | 'pie') => {
  const chartLabels = {
    'line': 'خطي',
    'area': 'مساحة',
    'bar': 'أعمدة',
    'pie': 'دائري'
  }

  const chartTestIds = {
    'line': 'line-chart',
    'area': 'area-chart',
    'bar': 'bar-chart',
    'pie': 'pie-chart'
  }

  cy.get('[data-testid="chart-type-selector"]').click()
  cy.contains(chartLabels[chartType]).click()
  cy.get(`[data-testid="${chartTestIds[chartType]}"]`).should('be.visible')
})

// قياس وقت التحميل
Cypress.Commands.add('measureLoadTime', (elementSelector: string, maxTime: number) => {
  const startTime = performance.now()

  cy.get(elementSelector).should('be.visible').then(() => {
    const loadTime = performance.now() - startTime
    cy.log(`وقت التحميل: ${loadTime.toFixed(2)} مللي ثانية`)
    expect(loadTime).to.be.lessThan(maxTime)
  })
})

// التحقق من التجاوب
Cypress.Commands.add('testResponsiveness', (device: 'mobile' | 'tablet' | 'desktop') => {
  const viewports = {
    'mobile': [375, 667],
    'tablet': [768, 1024],
    'desktop': [1920, 1080]
  }

  cy.viewport(viewports[device][0], viewports[device][1])
  cy.get('[data-testid="kpi-dashboard"]').should('be.visible')
  cy.get('[data-testid="advanced-charts-panel"]').should('be.visible')
})

// محاكاة خطأ في الشبكة
Cypress.Commands.add('simulateNetworkError', () => {
  cy.intercept('**', { forceNetworkError: true })
})

// محاكاة شبكة بطيئة
Cypress.Commands.add('simulateSlowNetwork', (delay: number = 2000) => {
  cy.intercept('**', (req) => {
    req.reply((res) => {
      return new Promise(resolve => {
        setTimeout(() => resolve(res), delay)
      })
    })
  })
})

// انتظار تحميل الصفحة
Cypress.Commands.add('waitForPageLoad', () => {
  cy.get('[data-testid="loading"]', { timeout: 10000 }).should('not.exist')
  cy.get('body').should('be.visible')
})

// التنقل إلى صفحة بلغة محددة
Cypress.Commands.add('visitWithLocale', (path: string, locale: 'ar' | 'en' = 'ar') => {
  const fullPath = `/${locale}${path.startsWith('/') ? path : `/${path}`}`
  cy.visit(fullPath)
})

// انتظار اختفاء شاشة التحميل
Cypress.Commands.add('waitForLoadingToFinish', () => {
  cy.get('[data-testid="auth-loading"]', { timeout: 15000 }).should('not.exist')
  cy.get('[data-testid="page-loading"]', { timeout: 10000 }).should('not.exist')
})

// التحقق من وجود عنصر بنص معين
Cypress.Commands.add('shouldContainArabicText', (text: string) => {
  cy.contains(text).should('be.visible')
})

// التحقق من وجود نص إنجليزي
Cypress.Commands.add('shouldContainEnglishText', (text: string) => {
  cy.contains(text).should('be.visible')
})

// محاكاة تسجيل حساب جديد
Cypress.Commands.add('mockSignup', (userType: 'customer' | 'merchant' | 'representative' = 'customer') => {
  cy.window().then((win) => {
    // Mock successful signup
    const mockUser = {
      uid: `new-${userType}-${Date.now()}`,
      email: `new-${userType}@test.com`,
      displayName: `New ${userType}`,
      emailVerified: false,
      userType: userType
    }

    // Store in localStorage to simulate successful signup
    win.localStorage.setItem('mockUser', JSON.stringify(mockUser))
    win.localStorage.setItem('userType', userType)
    win.localStorage.setItem('justSignedUp', 'true')
  })
})

// Mock Firebase Auth
Cypress.Commands.add('mockFirebaseAuth', () => {
  cy.window().then((win) => {
    // Mock Firebase auth methods
    if (win.firebase) {
      cy.stub(win.firebase.auth(), 'onAuthStateChanged').callsFake((callback) => {
        const mockUser = win.localStorage.getItem('mockUser')
        if (mockUser) {
          callback(JSON.parse(mockUser))
        } else {
          callback(null)
        }
        return () => {} // unsubscribe function
      })
    }
  })
})

// Mock Geolocation
Cypress.Commands.add('mockGeolocation', (lat: number = 24.7136, lng: number = 46.6753) => {
  cy.window().then((win) => {
    cy.stub(win.navigator.geolocation, 'getCurrentPosition').callsFake((success) => {
      success({
        coords: {
          latitude: lat,
          longitude: lng,
          accuracy: 100,
          altitude: null,
          altitudeAccuracy: null,
          heading: null,
          speed: null
        },
        timestamp: Date.now()
      })
    })
  })
})

// Mock ERP/POS Integration Data
Cypress.Commands.add('mockERPIntegration', () => {
  cy.window().then((win) => {
    const mockERPData = {
      id: 'test-erp-integration',
      merchantId: 'test-merchant-id',
      systemType: 'sap',
      systemName: 'SAP Test System',
      isActive: true,
      configuration: {
        apiUrl: 'https://test-sap-api.example.com',
        apiKey: 'test-api-key',
        username: 'test-user',
        database: 'test-db'
      },
      syncSettings: {
        syncProducts: true,
        syncInventory: true,
        syncOrders: true,
        syncCustomers: true,
        syncAccounting: false,
        syncInterval: 60,
        autoSync: true
      }
    }
    win.localStorage.setItem('mockERPIntegration', JSON.stringify(mockERPData))
  })
})

// Mock CRM Customer Data
Cypress.Commands.add('mockCRMCustomers', () => {
  cy.window().then((win) => {
    const mockCustomers = [
      {
        id: 'customer-1',
        userId: 'user-1',
        merchantId: 'test-merchant-id',
        profile: {
          firstName: 'أحمد',
          lastName: 'محمد',
          email: '<EMAIL>',
          phone: '+************',
          dateOfBirth: '1990-01-01',
          gender: 'male'
        },
        behavior: {
          totalOrders: 15,
          totalSpent: 2500,
          averageOrderValue: 166.67,
          lastOrderDate: new Date().toISOString(),
          preferredCategories: ['electronics', 'clothing']
        },
        tags: ['vip', 'frequent_buyer'],
        segment: 'high_value'
      }
    ]
    win.localStorage.setItem('mockCRMCustomers', JSON.stringify(mockCustomers))
  })
})

// Mock Coupon Data
Cypress.Commands.add('mockCoupons', () => {
  cy.window().then((win) => {
    const mockCoupons = [
      {
        id: 'coupon-1',
        code: 'SAVE20',
        type: 'percentage',
        value: 20,
        merchantId: 'test-merchant-id',
        isActive: true,
        usageLimit: 100,
        usedCount: 25,
        minOrderAmount: 100,
        validFrom: new Date().toISOString(),
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]
    win.localStorage.setItem('mockCoupons', JSON.stringify(mockCoupons))
  })
})

// Mock Loyalty Program Data
Cypress.Commands.add('mockLoyaltyProgram', () => {
  cy.window().then((win) => {
    const mockLoyalty = {
      customerId: 'user-1',
      merchantId: 'test-merchant-id',
      totalPoints: 1250,
      availablePoints: 1000,
      tier: 'gold',
      nextTierPoints: 500,
      transactions: [
        {
          id: 'loyalty-1',
          type: 'earned',
          points: 50,
          description: 'نقاط من طلب #12345',
          date: new Date().toISOString()
        }
      ]
    }
    win.localStorage.setItem('mockLoyaltyProgram', JSON.stringify(mockLoyalty))
  })
})

// Fill Form Helper
Cypress.Commands.add('fillForm', (formData: Record<string, string>) => {
  Object.entries(formData).forEach(([field, value]) => {
    cy.get(`[data-testid="${field}"], [name="${field}"], #${field}`)
      .should('be.visible')
      .clear()
      .type(value)
  })
})

// Wait for API Response
Cypress.Commands.add('waitForAPI', (alias: string, timeout: number = 10000) => {
  cy.wait(alias, { timeout })
})

// Check Dashboard Stats
Cypress.Commands.add('checkDashboardStats', () => {
  cy.get('[data-testid="dashboard-stats"]').should('be.visible')
  cy.get('[data-testid="total-orders"]').should('contain.text', '0')
  cy.get('[data-testid="total-revenue"]').should('be.visible')
})

// تسجيل دخول كنوع مستخدم محدد
Cypress.Commands.add('loginAs', (userType: string, userData: any) => {
  cy.window().then((win) => {
    win.localStorage.setItem('mockUser', JSON.stringify(userData))
    win.localStorage.setItem('userType', userType)
  })
})

// محاكاة بيانات المتجر
Cypress.Commands.add('mockStoreData', (storeData: any) => {
  cy.window().then((win) => {
    win.localStorage.setItem('mockStoreData', JSON.stringify(storeData))
  })
})

// محاكاة طلبات التجار في الانتظار
Cypress.Commands.add('mockPendingMerchants', (merchants: any[]) => {
  cy.window().then((win) => {
    win.localStorage.setItem('mockPendingMerchants', JSON.stringify(merchants))
  })
})

// تسجيل كتاجر
Cypress.Commands.add('signupAsMerchant', (name: string, email: string, password: string) => {
  // اختيار نوع المستخدم
  cy.get('[data-cy="user-type-merchant"]').click()
  cy.get('[data-cy="next-step"]').click()

  // الموافقة على الشروط
  cy.get('[data-cy="terms-checkbox"]').check()
  cy.get('[data-cy="next-step"]').click()

  // تأكيد الاختيار
  cy.get('[data-cy="next-step"]').click()

  // ملء البيانات الأساسية
  cy.get('[data-cy="username"]').type(name)
  cy.get('[data-cy="email"]').type(email)
  cy.get('[data-cy="next-detailed-step"]').click()

  // كلمة المرور
  cy.get('[data-cy="password"]').type(password)
  cy.get('[data-cy="confirm-password"]').type(password)
  cy.get('[data-cy="next-detailed-step"]').click()

  // رفع الملفات (محاكاة)
  cy.get('[data-cy="commercial-registration"]').selectFile('cypress/fixtures/test-document.pdf')
  cy.get('[data-cy="freelance-document"]').selectFile('cypress/fixtures/test-document.pdf')
  cy.get('[data-cy="next-detailed-step"]').click()

  // إرسال الطلب
  cy.get('[data-cy="submit-signup"]').click()
})

// موافقة على التاجر
Cypress.Commands.add('approveMerchant', (email: string, notes: string) => {
  // البحث عن التاجر
  cy.contains(email).parents('[data-cy="merchant-application"]').within(() => {
    // النقر على مراجعة الطلب
    cy.get('[data-cy="review-application"]').click()

    // إضافة ملاحظات
    cy.get('[data-cy="approval-notes"]').type(notes)

    // النقر على قبول
    cy.get('[data-cy="approve-merchant"]').click()
  })
})

// محاكاة Firestore للموافقات
Cypress.Commands.add('mockFirestore', () => {
  cy.intercept('GET', '**/firestore/**', { fixture: 'firestore-response.json' })
  cy.intercept('POST', '**/firestore/**', { statusCode: 200, body: { success: true } })
  cy.intercept('PATCH', '**/firestore/**', { statusCode: 200, body: { success: true } })
})

// إضافة تعريفات TypeScript للأوامر المخصصة
declare global {
  namespace Cypress {
    interface Chainable {
      mockLogin(userType?: 'customer' | 'merchant' | 'representative'): Chainable<void>
      mockLogout(): Chainable<void>
      waitForPageLoad(): Chainable<void>
      visitWithLocale(path: string, locale?: 'ar' | 'en'): Chainable<void>
      waitForLoadingToFinish(): Chainable<void>
      shouldContainArabicText(text: string): Chainable<void>
      shouldContainEnglishText(text: string): Chainable<void>
      mockSignup(userType?: 'customer' | 'merchant' | 'representative'): Chainable<void>
      mockFirebaseAuth(): Chainable<void>
      mockGeolocation(lat?: number, lng?: number): Chainable<void>
      mockERPIntegration(): Chainable<void>
      mockCRMCustomers(): Chainable<void>
      mockCoupons(): Chainable<void>
      mockLoyaltyProgram(): Chainable<void>
      fillForm(formData: Record<string, string>): Chainable<void>
      waitForAPI(alias: string, timeout?: number): Chainable<void>
      checkDashboardStats(): Chainable<void>
      loginAs(userType: string, userData: any): Chainable<void>
      mockStoreData(storeData: any): Chainable<void>
      mockPendingMerchants(merchants: any[]): Chainable<void>
      signupAsMerchant(name: string, email: string, password: string): Chainable<void>
      approveMerchant(email: string, notes: string): Chainable<void>
      mockFirestore(): Chainable<void>

      // أوامر اختبارات التحليلات المتقدمة
      loadAnalyticsTestData(): Chainable<void>
      cleanAnalyticsTestData(): Chainable<void>
      waitForKPILoad(): Chainable<void>
      waitForChartsLoad(): Chainable<void>
      validateKPIData(): Chainable<void>
      testTimeRangeChange(range: '7d' | '30d' | '90d' | '1y'): Chainable<void>
      testChartTypeChange(chartType: 'line' | 'area' | 'bar' | 'pie'): Chainable<void>
      measureLoadTime(elementSelector: string, maxTime: number): Chainable<void>
      testResponsiveness(device: 'mobile' | 'tablet' | 'desktop'): Chainable<void>
      simulateNetworkError(): Chainable<void>
      simulateSlowNetwork(delay?: number): Chainable<void>
    }
  }
}
