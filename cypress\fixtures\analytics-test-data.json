{"mockSalesReport": {"id": "report_test-merchant-uid_1640995200000", "merchantId": "test-merchant-uid", "period": {"startDate": "2024-01-01T00:00:00.000Z", "endDate": "2024-01-31T23:59:59.999Z", "type": "monthly"}, "summary": {"totalRevenue": 125000, "totalOrders": 450, "averageOrderValue": 277.78, "totalProducts": 25, "totalCustomers": 180, "conversionRate": 3.2, "growthRate": 15.5}, "dailyBreakdown": [{"date": "2024-01-01", "revenue": 4200, "orders": 15, "customers": 12, "averageOrderValue": 280, "topProducts": ["prod-1", "prod-2", "prod-3"]}, {"date": "2024-01-02", "revenue": 3800, "orders": 14, "customers": 11, "averageOrderValue": 271.43, "topProducts": ["prod-1", "prod-4", "prod-2"]}, {"date": "2024-01-03", "revenue": 4500, "orders": 16, "customers": 13, "averageOrderValue": 281.25, "topProducts": ["prod-2", "prod-1", "prod-5"]}], "productPerformance": [{"productId": "prod-1", "productName": "لابتوب Dell XPS 13", "category": "إلكترونيات", "totalSales": 45, "totalRevenue": 67500, "averageRating": 4.7, "viewsCount": 1250, "conversionRate": 3.6, "profitMargin": 25.5, "trend": "up", "growthRate": 18.2}, {"productId": "prod-2", "productName": "ماوس لاسلكي Logitech", "category": "إلكترونيات", "totalSales": 120, "totalRevenue": 6000, "averageRating": 4.3, "viewsCount": 800, "conversionRate": 15.0, "profitMargin": 35.0, "trend": "up", "growthRate": 12.5}, {"productId": "prod-3", "productName": "قميص قطني أزرق", "category": "ملابس", "totalSales": 85, "totalRevenue": 6800, "averageRating": 4.1, "viewsCount": 650, "conversionRate": 13.1, "profitMargin": 45.0, "trend": "stable", "growthRate": 2.1}], "customerAnalytics": {"totalCustomers": 180, "newCustomers": 45, "returningCustomers": 135, "customerRetentionRate": 75.0, "averageCustomerLifetimeValue": 694.44, "topCustomers": [{"customerId": "customer-1", "customerName": "<PERSON>حمد محمد علي", "totalOrders": 12, "totalSpent": 3600, "averageOrderValue": 300, "lastOrderDate": "2024-01-28T10:30:00.000Z"}, {"customerId": "customer-2", "customerName": "فاطمة أحمد", "totalOrders": 8, "totalSpent": 2400, "averageOrderValue": 300, "lastOrderDate": "2024-01-25T14:15:00.000Z"}, {"customerId": "customer-3", "customerName": "محم<PERSON> سالم", "totalOrders": 10, "totalSpent": 2800, "averageOrderValue": 280, "lastOrderDate": "2024-01-30T09:45:00.000Z"}], "customerSegments": [{"segment": "high_value", "count": 36, "percentage": 20.0, "averageSpending": 1388.89, "characteristics": ["إنفاق عالي", "طلبات متكررة", "ولاء عالي"]}, {"segment": "regular", "count": 90, "percentage": 50.0, "averageSpending": 694.44, "characteristics": ["إنفاق متوسط", "طلبات منتظمة"]}, {"segment": "new", "count": 45, "percentage": 25.0, "averageSpending": 347.22, "characteristics": ["ع<PERSON><PERSON><PERSON><PERSON> جدد", "يحتاجون متابعة"]}, {"segment": "at_risk", "count": 9, "percentage": 5.0, "averageSpending": 208.33, "characteristics": ["لم يطلبوا مؤخراً", "يحتاجون إعادة تفعيل"]}]}, "trends": {"revenueGrowth": {"current": 125000, "previous": 108500, "growthRate": 15.2, "trend": "up"}, "orderGrowth": {"current": 450, "previous": 385, "growthRate": 16.9, "trend": "up"}, "customerGrowth": {"current": 180, "previous": 165, "growthRate": 9.1, "trend": "up"}, "seasonalPatterns": [{"period": "الربع الأول", "averageRevenue": 112500, "averageOrders": 405, "pattern": "normal"}, {"period": "الربع الثاني", "averageRevenue": 137500, "averageOrders": 495, "pattern": "peak"}, {"period": "الربع الثالث", "averageRevenue": 100000, "averageOrders": 360, "pattern": "low"}, {"period": "الربع الرابع", "averageRevenue": 150000, "averageOrders": 540, "pattern": "peak"}], "predictions": {"nextMonthRevenue": 143750, "nextMonthOrders": 518, "confidence": 82.5}}, "generatedAt": "2024-01-31T23:59:59.999Z"}, "mockKPIData": [{"id": "revenue", "title": "إجمالي الإيرادات", "value": 125000, "previousValue": 108500, "target": 150000, "unit": "ريال", "format": "currency", "growth": 15.2, "status": "good"}, {"id": "orders", "title": "إجمالي الطلبات", "value": 450, "previousValue": 385, "target": 500, "unit": "ط<PERSON><PERSON>", "format": "number", "growth": 16.9, "status": "excellent"}, {"id": "customers", "title": "إجمالي العملاء", "value": 180, "previousValue": 165, "target": 200, "unit": "عميل", "format": "number", "growth": 9.1, "status": "good"}, {"id": "aov", "title": "متوسط قيمة الطلب", "value": 277.78, "previousValue": 281.82, "target": 300, "unit": "ريال", "format": "currency", "growth": -1.4, "status": "warning"}], "mockCustomerBehavior": {"customerId": "customer-1", "merchantId": "test-merchant-uid", "behaviorProfile": {"purchaseFrequency": "high", "averageOrderValue": 300, "preferredCategories": ["إلكترونيات", "كتب", "ملابس"], "shoppingTimes": {"preferredHours": [10, 14, 20], "preferredDays": ["ال<PERSON><PERSON>د", "الثلاثاء", "الخميس"]}, "seasonalPatterns": [{"season": "spring", "averageSpending": 280, "preferredProducts": ["قميص قطني", "كتاب البرمجة"], "activityLevel": "medium"}, {"season": "summer", "averageSpending": 320, "preferredProducts": ["لابتوب Dell", "ماوس لاسلكي"], "activityLevel": "high"}]}, "loyaltyMetrics": {"customerLifetimeValue": 3600, "retentionProbability": 85.5, "churnRisk": "low", "loyaltyScore": 78.2}, "recommendations": {"productRecommendations": ["إلكترونيات", "كتب", "ملابس"], "marketingActions": ["عرض برنا<PERSON>ج الولاء", "إشعارات المنتجات الجديدة"], "retentionStrategies": ["برنامج VIP للعملاء المميزين"]}, "insights": [{"type": "behavior", "title": "عميل نشط", "description": "هذا العميل يقوم بطلبات متكررة ويظهر ولاءً عالياً", "confidence": 85, "actionable": true, "suggestedActions": ["تقديم برنامج ولاء", "إشعارات المنتجات الجديدة"]}, {"type": "opportunity", "title": "عميل عالي القيمة", "description": "هذا العميل يساهم بشكل كبير في الإيرادات", "confidence": 90, "actionable": true, "suggestedActions": ["خدمة VIP", "عروض حصرية"]}]}, "testScenarios": {"performanceTest": {"largeDatasetSize": 1000, "expectedLoadTime": 3000, "expectedProcessingTime": 10000}, "errorHandling": {"networkError": "فشل في الاتصال بالخادم", "dataError": "خطأ في معالجة البيانات", "emptyDataMessage": "لا توجد بيانات للعرض"}, "responsiveBreakpoints": {"mobile": {"width": 375, "height": 667}, "tablet": {"width": 768, "height": 1024}, "desktop": {"width": 1920, "height": 1080}}}}