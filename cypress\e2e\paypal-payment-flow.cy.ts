/// <reference types="cypress" />

describe('PayPal Payment Flow', () => {
  beforeEach(() => {
    // إعداد البيئة للاختبار
    cy.visit('/');
    cy.viewport(1280, 720);
  });

  describe('PayPal Integration Tests', () => {
    it('يجب أن يعرض خيار PayPal في صفحة الدفع', () => {
      // الانتقال إلى صفحة الدفع
      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      // التحقق من وجود خيار PayPal
      cy.get('[data-testid="payment-method-paypal"]').should('be.visible');
      cy.get('[data-testid="payment-method-paypal"]').should('contain', 'PayPal');
    });

    it('يجب أن يعرض مكون PayPal Button عند اختيار PayPal', () => {
      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      // اختيار PayPal كطريقة دفع
      cy.get('[data-testid="payment-method-paypal"]').click();
      
      // التحقق من ظهور مكون PayPal
      cy.get('[data-testid="paypal-button-container"]').should('be.visible');
      cy.get('[data-testid="paypal-amount-display"]').should('be.visible');
    });

    it('يجب أن يعرض رسالة خطأ عند عدم تكوين PayPal', () => {
      // محاكاة عدم وجود Client ID
      cy.window().then((win) => {
        delete win.process?.env?.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
      });
      
      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      cy.get('[data-testid="payment-method-paypal"]').click();
      cy.get('[data-testid="paypal-error-message"]').should('contain', 'PayPal Client ID غير مُكوّن');
    });

    it('يجب أن يعرض حالة التحميل أثناء تحميل PayPal', () => {
      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      cy.get('[data-testid="payment-method-paypal"]').click();
      
      // التحقق من حالة التحميل
      cy.get('[data-testid="paypal-loading"]').should('be.visible');
      cy.get('[data-testid="paypal-loading"]').should('contain', 'جاري تحميل PayPal');
    });
  });

  describe('PayPal API Tests', () => {
    it('يجب أن ينشئ طلب PayPal بنجاح', () => {
      // محاكاة إنشاء طلب PayPal
      cy.intercept('POST', '/api/paypal/create-order', {
        statusCode: 200,
        body: {
          id: 'PAYPAL_ORDER_123',
          status: 'CREATED',
          links: [
            {
              href: 'https://www.sandbox.paypal.com/checkoutnow?token=PAYPAL_ORDER_123',
              rel: 'approve',
              method: 'GET'
            }
          ]
        }
      }).as('createPayPalOrder');

      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      cy.get('[data-testid="payment-method-paypal"]').click();
      cy.get('[data-testid="paypal-create-order-btn"]').click();
      
      cy.wait('@createPayPalOrder');
      cy.get('[data-testid="paypal-success-message"]').should('be.visible');
    });

    it('يجب أن يعالج خطأ إنشاء طلب PayPal', () => {
      // محاكاة خطأ في إنشاء الطلب
      cy.intercept('POST', '/api/paypal/create-order', {
        statusCode: 500,
        body: {
          error: 'فشل في إنشاء طلب PayPal',
          details: 'PayPal credentials not configured'
        }
      }).as('createPayPalOrderError');

      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      cy.get('[data-testid="payment-method-paypal"]').click();
      cy.get('[data-testid="paypal-create-order-btn"]').click();
      
      cy.wait('@createPayPalOrderError');
      cy.get('[data-testid="paypal-error-message"]').should('contain', 'فشل في إنشاء طلب PayPal');
    });

    it('يجب أن يلتقط طلب PayPal بنجاح', () => {
      // محاكاة التقاط طلب PayPal
      cy.intercept('POST', '/api/paypal/capture-order', {
        statusCode: 200,
        body: {
          id: 'CAPTURE_123',
          status: 'COMPLETED',
          orderID: 'PAYPAL_ORDER_123',
          orderId: 'ORDER_123'
        }
      }).as('capturePayPalOrder');

      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      // محاكاة عملية الدفع المكتملة
      cy.window().then((win) => {
        win.postMessage({
          type: 'PAYPAL_PAYMENT_SUCCESS',
          orderID: 'PAYPAL_ORDER_123'
        }, '*');
      });
      
      cy.wait('@capturePayPalOrder');
      cy.get('[data-testid="payment-success-message"]').should('be.visible');
    });
  });

  describe('PayPal Security Tests', () => {
    it('يجب أن يتحقق من صحة بيانات الطلب', () => {
      cy.intercept('POST', '/api/paypal/create-order', (req) => {
        expect(req.body).to.have.property('orderId');
        expect(req.body).to.have.property('amount');
        expect(req.body).to.have.property('currency');
        expect(req.body.amount).to.be.a('number');
        expect(req.body.amount).to.be.greaterThan(0);
      }).as('validatePayPalRequest');

      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      cy.get('[data-testid="payment-method-paypal"]').click();
      cy.get('[data-testid="paypal-create-order-btn"]').click();
      
      cy.wait('@validatePayPalRequest');
    });

    it('يجب أن يرفض الطلبات بدون بيانات مطلوبة', () => {
      cy.intercept('POST', '/api/paypal/create-order', {
        statusCode: 400,
        body: {
          error: 'البيانات المطلوبة مفقودة'
        }
      }).as('invalidPayPalRequest');

      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      // محاكاة طلب بدون بيانات
      cy.window().then((win) => {
        fetch('/api/paypal/create-order', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({})
        });
      });
      
      cy.wait('@invalidPayPalRequest');
    });
  });

  describe('PayPal UI/UX Tests', () => {
    it('يجب أن يعرض معلومات الأمان', () => {
      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      cy.get('[data-testid="payment-method-paypal"]').click();
      
      cy.get('[data-testid="paypal-security-info"]').should('be.visible');
      cy.get('[data-testid="paypal-security-info"]').should('contain', 'محمي بأمان PayPal');
    });

    it('يجب أن يعرض المبلغ بالعملة الصحيحة', () => {
      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      cy.get('[data-testid="payment-method-paypal"]').click();
      
      cy.get('[data-testid="paypal-amount-display"]').should('be.visible');
      cy.get('[data-testid="paypal-amount-display"]').should('match', /\d+\.\d{2}/);
    });

    it('يجب أن يكون متجاوب على الأجهزة المختلفة', () => {
      // اختبار على الجوال
      cy.viewport(375, 667);
      cy.visitWithLocale('/checkout');
      cy.waitForLoadingToFinish();
      
      cy.get('[data-testid="payment-method-paypal"]').click();
      cy.get('[data-testid="paypal-button-container"]').should('be.visible');
      
      // اختبار على التابلت
      cy.viewport(768, 1024);
      cy.get('[data-testid="paypal-button-container"]').should('be.visible');
      
      // اختبار على سطح المكتب
      cy.viewport(1280, 720);
      cy.get('[data-testid="paypal-button-container"]').should('be.visible');
    });
  });
});
