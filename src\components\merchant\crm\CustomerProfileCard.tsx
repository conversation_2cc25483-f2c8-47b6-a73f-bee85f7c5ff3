// src/components/merchant/crm/CustomerProfileCard.tsx
'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  ShoppingBag,
  Star,
  TrendingUp,
  MessageSquare,
  Tag,
  Plus,
  Edit,
  Save,
  X,
  AlertTriangle,
  Crown,
  Gift
} from 'lucide-react';
import type { CustomerProfile, CustomerNote } from '@/types';
import { cn } from '@/lib/utils';

interface CustomerProfileCardProps {
  customer: CustomerProfile;
  onUpdateTags?: (tags: string[]) => Promise<void>;
  onAddNote?: (note: Omit<CustomerNote, 'id' | 'createdAt'>) => Promise<void>;
  className?: string;
}

export const CustomerProfileCard: React.FC<CustomerProfileCardProps> = ({
  customer,
  onUpdateTags,
  onAddNote,
  className
}) => {
  const [isEditingTags, setIsEditingTags] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [newNote, setNewNote] = useState({
    content: '',
    type: 'general' as const,
    isPrivate: false
  });

  // حساب العمر إذا كان تاريخ الميلاد متوفراً
  const calculateAge = (birthDate: Date): number => {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  // الحصول على لون المستوى
  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'bronze': return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'silver': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'gold': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'platinum': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // الحصول على أيقونة المستوى
  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'platinum': return <Crown className="h-3 w-3" />;
      case 'gold': return <Star className="h-3 w-3" />;
      default: return null;
    }
  };

  // إضافة علامة جديدة
  const handleAddTag = async () => {
    if (newTag.trim() && !customer.tags.includes(newTag.trim())) {
      const updatedTags = [...customer.tags, newTag.trim()];
      await onUpdateTags?.(updatedTags);
      setNewTag('');
    }
  };

  // حذف علامة
  const handleRemoveTag = async (tagToRemove: string) => {
    const updatedTags = customer.tags.filter(tag => tag !== tagToRemove);
    await onUpdateTags?.(updatedTags);
  };

  // إضافة ملاحظة جديدة
  const handleAddNote = async () => {
    if (newNote.content.trim()) {
      await onAddNote?.({
        ...newNote,
        createdBy: 'current-user', // يجب الحصول على معرف المستخدم الحالي
        createdByName: 'المستخدم الحالي' // يجب الحصول على اسم المستخدم الحالي
      });
      setNewNote({ content: '', type: 'general', isPrivate: false });
      setIsAddingNote(false);
    }
  };

  // تنسيق التاريخ
  const formatDate = (timestamp: any) => {
    return timestamp?.toDate?.()?.toLocaleDateString('ar-SA') || 'غير محدد';
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={customer.personalInfo.avatar} />
              <AvatarFallback className="text-lg">
                {customer.personalInfo.name.charAt(0)}
              </AvatarFallback>
            </Avatar>
            
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <CardTitle className="text-xl">{customer.personalInfo.name}</CardTitle>
                <Badge className={getTierColor(customer.segmentation.tier)}>
                  {getTierIcon(customer.segmentation.tier)}
                  <span className="ms-1">
                    {customer.segmentation.tier === 'bronze' && 'برونزي'}
                    {customer.segmentation.tier === 'silver' && 'فضي'}
                    {customer.segmentation.tier === 'gold' && 'ذهبي'}
                    {customer.segmentation.tier === 'platinum' && 'بلاتيني'}
                  </span>
                </Badge>
              </div>
              
              <CardDescription className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  {customer.personalInfo.email}
                </span>
                {customer.personalInfo.phone && (
                  <span className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    {customer.personalInfo.phone}
                  </span>
                )}
              </CardDescription>
            </div>
          </div>

          {/* مؤشرات التحذير */}
          <div className="flex flex-col gap-2">
            {customer.segmentation.churnProbability > 0.7 && (
              <Badge variant="destructive" className="text-xs">
                <AlertTriangle className="h-3 w-3 me-1" />
                معرض للفقدان
              </Badge>
            )}
            {customer.stats.complaintsCount > 0 && (
              <Badge variant="outline" className="text-xs">
                {customer.stats.complaintsCount} شكوى
              </Badge>
            )}
            {customer.tags.includes('vip') && (
              <Badge className="bg-purple-100 text-purple-800 text-xs">
                <Crown className="h-3 w-3 me-1" />
                VIP
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <ShoppingBag className="h-5 w-5 text-blue-600 mx-auto mb-1" />
            <div className="text-lg font-semibold text-blue-900">
              {customer.shoppingBehavior.totalOrders}
            </div>
            <div className="text-xs text-blue-600">إجمالي الطلبات</div>
          </div>

          <div className="text-center p-3 bg-green-50 rounded-lg">
            <TrendingUp className="h-5 w-5 text-green-600 mx-auto mb-1" />
            <div className="text-lg font-semibold text-green-900">
              {customer.shoppingBehavior.totalSpent.toLocaleString()} ر.س
            </div>
            <div className="text-xs text-green-600">إجمالي الإنفاق</div>
          </div>

          <div className="text-center p-3 bg-yellow-50 rounded-lg">
            <Star className="h-5 w-5 text-yellow-600 mx-auto mb-1" />
            <div className="text-lg font-semibold text-yellow-900">
              {customer.stats.averageRating.toFixed(1)}
            </div>
            <div className="text-xs text-yellow-600">متوسط التقييم</div>
          </div>

          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <Gift className="h-5 w-5 text-purple-600 mx-auto mb-1" />
            <div className="text-lg font-semibold text-purple-900">
              {customer.stats.loyaltyPoints}
            </div>
            <div className="text-xs text-purple-600">نقاط الولاء</div>
          </div>
        </div>

        <Separator />

        {/* المعلومات الشخصية */}
        <div className="space-y-3">
          <h4 className="font-semibold flex items-center gap-2">
            <User className="h-4 w-4" />
            المعلومات الشخصية
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {customer.personalInfo.dateOfBirth && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>العمر: {calculateAge(customer.personalInfo.dateOfBirth.toDate())} سنة</span>
              </div>
            )}
            
            {customer.personalInfo.gender && (
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span>الجنس: {customer.personalInfo.gender === 'male' ? 'ذكر' : 'أنثى'}</span>
              </div>
            )}

            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>تاريخ التسجيل: {formatDate(customer.createdAt)}</span>
            </div>

            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
              <span>آخر تفاعل: {formatDate(customer.lastInteractionDate)}</span>
            </div>
          </div>

          {/* العناوين */}
          {customer.addresses.length > 0 && (
            <div className="space-y-2">
              <h5 className="font-medium text-sm">العناوين:</h5>
              {customer.addresses.map((address) => (
                <div key={address.id} className="flex items-start gap-2 text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4 mt-0.5" />
                  <div>
                    <span className="font-medium">{address.label}: </span>
                    {address.street}, {address.city}
                    {address.isDefault && (
                      <Badge variant="outline" className="ms-2 text-xs">افتراضي</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <Separator />

        {/* العلامات */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold flex items-center gap-2">
              <Tag className="h-4 w-4" />
              العلامات
            </h4>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditingTags(!isEditingTags)}
            >
              {isEditingTags ? <X className="h-3 w-3" /> : <Edit className="h-3 w-3" />}
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {customer.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
                {isEditingTags && (
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="ms-1 hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </Badge>
            ))}
          </div>

          {isEditingTags && (
            <div className="flex gap-2">
              <Input
                placeholder="إضافة علامة جديدة"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                className="text-sm"
              />
              <Button size="sm" onClick={handleAddTag}>
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>

        <Separator />

        {/* الملاحظات */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              الملاحظات ({customer.notes.length})
            </h4>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddingNote(!isAddingNote)}
            >
              <Plus className="h-3 w-3 me-1" />
              إضافة ملاحظة
            </Button>
          </div>

          {isAddingNote && (
            <div className="space-y-3 p-3 border rounded-lg bg-gray-50">
              <div>
                <Label htmlFor="note-content">المحتوى</Label>
                <Textarea
                  id="note-content"
                  placeholder="اكتب ملاحظتك هنا..."
                  value={newNote.content}
                  onChange={(e) => setNewNote(prev => ({ ...prev, content: e.target.value }))}
                  className="mt-1"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <select
                    value={newNote.type}
                    onChange={(e) => setNewNote(prev => ({ ...prev, type: e.target.value as any }))}
                    className="text-sm border rounded px-2 py-1"
                  >
                    <option value="general">عامة</option>
                    <option value="complaint">شكوى</option>
                    <option value="compliment">إطراء</option>
                    <option value="follow_up">متابعة</option>
                    <option value="important">مهمة</option>
                  </select>
                  
                  <label className="flex items-center gap-1 text-sm">
                    <input
                      type="checkbox"
                      checked={newNote.isPrivate}
                      onChange={(e) => setNewNote(prev => ({ ...prev, isPrivate: e.target.checked }))}
                    />
                    خاصة
                  </label>
                </div>
                
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleAddNote}>
                    <Save className="h-3 w-3 me-1" />
                    حفظ
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setIsAddingNote(false)}>
                    إلغاء
                  </Button>
                </div>
              </div>
            </div>
          )}

          {customer.notes.length > 0 ? (
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {customer.notes.slice(0, 3).map((note) => (
                <div key={note.id} className="p-3 border rounded-lg text-sm">
                  <div className="flex items-start justify-between mb-2">
                    <Badge variant="outline" className="text-xs">
                      {note.type === 'general' && 'عامة'}
                      {note.type === 'complaint' && 'شكوى'}
                      {note.type === 'compliment' && 'إطراء'}
                      {note.type === 'follow_up' && 'متابعة'}
                      {note.type === 'important' && 'مهمة'}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {formatDate(note.createdAt)}
                    </span>
                  </div>
                  <p className="text-muted-foreground">{note.content}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    بواسطة: {note.createdByName}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">
              لا توجد ملاحظات بعد
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
