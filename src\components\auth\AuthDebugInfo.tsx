// src/components/auth/AuthDebugInfo.tsx
"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { auth } from '@/lib/firebase';
import { onAuthStateChanged } from 'firebase/auth';

export default function AuthDebugInfo() {
  const { user, initialLoadingCompleted } = useAuth();
  const [firebaseUser, setFirebaseUser] = useState<any>(null);
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setFirebaseUser(currentUser);
      setDebugInfo({
        firebaseUserExists: !!currentUser,
        firebaseUserId: currentUser?.uid,
        firebaseUserEmail: currentUser?.email,
        contextUserExists: !!user,
        contextUserId: user?.uid,
        initialLoadingCompleted,
        timestamp: new Date().toISOString(),
        localStorage: {
          justSignedUp: localStorage.getItem('justSignedUp'),
          newUserType: localStorage.getItem('newUserType'),
          signupTimestamp: localStorage.getItem('signupTimestamp'),
        },
        sessionStorage: {
          justSignedUp: sessionStorage.getItem('justSignedUp'),
          newUserType: sessionStorage.getItem('newUserType'),
          signupTimestamp: sessionStorage.getItem('signupTimestamp'),
        }
      });
    });

    return () => unsubscribe();
  }, [user, initialLoadingCompleted]);

  // عرض معلومات التصحيح فقط في بيئة التطوير
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">🔍 Auth Debug Info</h3>
      <div className="space-y-1">
        <div>Firebase User: {debugInfo.firebaseUserExists ? '✅' : '❌'}</div>
        <div>Context User: {debugInfo.contextUserExists ? '✅' : '❌'}</div>
        <div>Loading Complete: {debugInfo.initialLoadingCompleted ? '✅' : '❌'}</div>
        {debugInfo.firebaseUserId && (
          <div>User ID: {debugInfo.firebaseUserId.substring(0, 8)}...</div>
        )}
        <div>Just Signed Up (LS): {debugInfo.localStorage?.justSignedUp || 'No'}</div>
        <div>Just Signed Up (SS): {debugInfo.sessionStorage?.justSignedUp || 'No'}</div>
        <div>User Type: {debugInfo.localStorage?.newUserType || debugInfo.sessionStorage?.newUserType || 'None'}</div>
      </div>
    </div>
  );
}
