/// <reference types="cypress" />

describe('⚡ اختبارات الأداء لنظام التحليلات المتقدمة', () => {
  beforeEach(() => {
    cy.mockLogin('merchant');
    
    // إعداد بيانات كبيرة للاختبار
    cy.window().then((win) => {
      // إنشاء بيانات كبيرة لاختبار الأداء
      const generateLargeDataset = () => {
        const orders = [];
        const products = [];
        const customers = [];
        
        // إنشاء 1000 طلب
        for (let i = 1; i <= 1000; i++) {
          const randomDate = new Date();
          randomDate.setDate(randomDate.getDate() - Math.floor(Math.random() * 365));
          
          orders.push({
            id: `order-${i}`,
            merchantId: 'test-merchant-uid',
            customerId: `customer-${Math.floor(Math.random() * 100) + 1}`,
            totalAmount: Math.floor(Math.random() * 500) + 50,
            status: 'completed',
            createdAt: { toDate: () => randomDate },
            items: [
              {
                productId: `prod-${Math.floor(Math.random() * 50) + 1}`,
                productName: `منتج ${Math.floor(Math.random() * 50) + 1}`,
                quantity: Math.floor(Math.random() * 3) + 1,
                price: Math.floor(Math.random() * 200) + 20,
                category: ['إلكترونيات', 'ملابس', 'كتب', 'رياضة', 'منزل'][Math.floor(Math.random() * 5)]
              }
            ]
          });
        }
        
        // إنشاء 50 منتج
        for (let i = 1; i <= 50; i++) {
          products.push({
            id: `prod-${i}`,
            name: `منتج ${i}`,
            category: ['إلكترونيات', 'ملابس', 'كتب', 'رياضة', 'منزل'][Math.floor(Math.random() * 5)],
            merchantId: 'test-merchant-uid',
            price: Math.floor(Math.random() * 300) + 20,
            viewsCount: Math.floor(Math.random() * 1000) + 50
          });
        }
        
        // إنشاء 100 عميل
        for (let i = 1; i <= 100; i++) {
          const joinDate = new Date();
          joinDate.setDate(joinDate.getDate() - Math.floor(Math.random() * 730));
          
          customers.push({
            id: `customer-${i}`,
            displayName: `عميل ${i}`,
            email: `customer${i}@example.com`,
            createdAt: { toDate: () => joinDate }
          });
        }
        
        return { orders, products, customers };
      };
      
      const largeDataset = generateLargeDataset();
      win.localStorage.setItem('mockOrders', JSON.stringify(largeDataset.orders));
      win.localStorage.setItem('mockProducts', JSON.stringify(largeDataset.products));
      win.localStorage.setItem('mockCustomers', JSON.stringify(largeDataset.customers));
      win.localStorage.setItem('performanceTest', 'true');
    });
  });

  afterEach(() => {
    cy.window().then((win) => {
      win.localStorage.removeItem('mockOrders');
      win.localStorage.removeItem('mockProducts');
      win.localStorage.removeItem('mockCustomers');
      win.localStorage.removeItem('performanceTest');
    });
    cy.mockLogout();
  });

  describe('🚀 اختبارات سرعة التحميل', () => {
    it('يجب أن تحمل الصفحة الرئيسية للتقارير في أقل من 3 ثوان', () => {
      const startTime = performance.now();
      
      cy.visitWithLocale('/merchant/reports');
      cy.contains('التقارير والتحليلات').should('be.visible');
      
      cy.then(() => {
        const loadTime = performance.now() - startTime;
        cy.log(`وقت تحميل الصفحة: ${loadTime.toFixed(2)} مللي ثانية`);
        expect(loadTime).to.be.lessThan(3000);
      });
    });

    it('يجب أن تحمل مؤشرات الأداء في أقل من 2 ثانية', () => {
      cy.visitWithLocale('/merchant/reports');
      
      const startTime = performance.now();
      cy.get('[data-testid="kpi-dashboard"]').should('be.visible');
      
      cy.then(() => {
        const loadTime = performance.now() - startTime;
        cy.log(`وقت تحميل مؤشرات الأداء: ${loadTime.toFixed(2)} مللي ثانية`);
        expect(loadTime).to.be.lessThan(2000);
      });
    });

    it('يجب أن تحمل الرسوم البيانية في أقل من 2.5 ثانية', () => {
      cy.visitWithLocale('/merchant/reports');
      
      const startTime = performance.now();
      cy.get('[data-testid="advanced-charts-panel"]').should('be.visible');
      
      cy.then(() => {
        const loadTime = performance.now() - startTime;
        cy.log(`وقت تحميل الرسوم البيانية: ${loadTime.toFixed(2)} مللي ثانية`);
        expect(loadTime).to.be.lessThan(2500);
      });
    });
  });

  describe('📊 اختبارات أداء معالجة البيانات الكبيرة', () => {
    it('يجب أن تعالج 1000 طلب بكفاءة', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // انتظار تحميل البيانات
      cy.get('[data-testid="loading-indicator"]').should('be.visible');
      
      const startTime = performance.now();
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 15000 });
      
      cy.then(() => {
        const processingTime = performance.now() - startTime;
        cy.log(`وقت معالجة 1000 طلب: ${processingTime.toFixed(2)} مللي ثانية`);
        expect(processingTime).to.be.lessThan(10000); // أقل من 10 ثوان
      });
      
      // التحقق من صحة البيانات المعالجة
      cy.get('[data-testid="total-orders"]').should('contain.text', '1000');
    });

    it('يجب أن تعرض قائمة المنتجات الكبيرة بسلاسة', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.contains('المنتجات').click();
      
      const startTime = performance.now();
      cy.get('[data-testid="product-performance-list"]').should('be.visible');
      
      cy.then(() => {
        const renderTime = performance.now() - startTime;
        cy.log(`وقت عرض قائمة المنتجات: ${renderTime.toFixed(2)} مللي ثانية`);
        expect(renderTime).to.be.lessThan(1500);
      });
      
      // التحقق من عرض المنتجات
      cy.get('[data-testid="product-item"]').should('have.length.at.least', 10);
    });

    it('يجب أن تعرض قائمة العملاء الكبيرة بكفاءة', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.contains('العملاء').click();
      
      const startTime = performance.now();
      cy.get('[data-testid="top-customers-list"]').should('be.visible');
      
      cy.then(() => {
        const renderTime = performance.now() - startTime;
        cy.log(`وقت عرض قائمة العملاء: ${renderTime.toFixed(2)} مللي ثانية`);
        expect(renderTime).to.be.lessThan(1500);
      });
      
      // التحقق من عرض العملاء
      cy.get('[data-testid="customer-item"]').should('have.length.at.least', 5);
    });
  });

  describe('🔄 اختبارات أداء التحديث والتفاعل', () => {
    it('يجب أن يستجيب تغيير الفترة الزمنية بسرعة', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // انتظار التحميل الأولي
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 15000 });
      
      const startTime = performance.now();
      
      // تغيير الفترة الزمنية
      cy.get('[data-testid="time-range-selector"]').click();
      cy.contains('7 أيام').click();
      
      // انتظار التحديث
      cy.get('[data-testid="loading-indicator"]').should('be.visible');
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 10000 });
      
      cy.then(() => {
        const updateTime = performance.now() - startTime;
        cy.log(`وقت تحديث البيانات: ${updateTime.toFixed(2)} مللي ثانية`);
        expect(updateTime).to.be.lessThan(5000);
      });
    });

    it('يجب أن يستجيب تغيير نوع الرسم البياني بسرعة', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 15000 });
      
      const startTime = performance.now();
      
      // تغيير نوع الرسم البياني
      cy.get('[data-testid="chart-type-selector"]').click();
      cy.contains('أعمدة').click();
      
      cy.get('[data-testid="bar-chart"]').should('be.visible');
      
      cy.then(() => {
        const switchTime = performance.now() - startTime;
        cy.log(`وقت تغيير نوع الرسم البياني: ${switchTime.toFixed(2)} مللي ثانية`);
        expect(switchTime).to.be.lessThan(1000);
      });
    });

    it('يجب أن يستجيب التنقل بين التبويبات بسرعة', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 15000 });
      
      const tabs = ['المبيعات', 'العملاء', 'المنتجات'];
      
      tabs.forEach(tab => {
        const startTime = performance.now();
        
        cy.contains(tab).click();
        cy.get('[data-testid="tab-content"]').should('be.visible');
        
        cy.then(() => {
          const switchTime = performance.now() - startTime;
          cy.log(`وقت التنقل إلى تبويب ${tab}: ${switchTime.toFixed(2)} مللي ثانية`);
          expect(switchTime).to.be.lessThan(800);
        });
      });
    });
  });

  describe('💾 اختبارات استهلاك الذاكرة', () => {
    it('يجب ألا يتسبب في تسريب الذاكرة', () => {
      cy.visitWithLocale('/merchant/reports');
      
      // قياس استهلاك الذاكرة الأولي
      cy.window().then((win) => {
        if (win.performance && win.performance.memory) {
          const initialMemory = win.performance.memory.usedJSHeapSize;
          cy.log(`استهلاك الذاكرة الأولي: ${(initialMemory / 1024 / 1024).toFixed(2)} MB`);
          
          // تنفيذ عمليات متعددة
          cy.get('[data-testid="time-range-selector"]').click();
          cy.contains('30 يوم').click();
          cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 10000 });
          
          cy.contains('المبيعات').click();
          cy.contains('العملاء').click();
          cy.contains('المنتجات').click();
          cy.contains('نظرة عامة').click();
          
          // قياس استهلاك الذاكرة النهائي
          cy.then(() => {
            const finalMemory = win.performance.memory.usedJSHeapSize;
            const memoryIncrease = finalMemory - initialMemory;
            cy.log(`زيادة استهلاك الذاكرة: ${(memoryIncrease / 1024 / 1024).toFixed(2)} MB`);
            
            // يجب ألا تزيد الذاكرة بأكثر من 50 MB
            expect(memoryIncrease).to.be.lessThan(50 * 1024 * 1024);
          });
        }
      });
    });
  });

  describe('📱 اختبارات الأداء على الأجهزة المختلفة', () => {
    it('يجب أن تعمل بكفاءة على الهواتف المحمولة', () => {
      cy.viewport('iphone-x');
      
      const startTime = performance.now();
      cy.visitWithLocale('/merchant/reports');
      cy.contains('التقارير والتحليلات').should('be.visible');
      
      cy.then(() => {
        const loadTime = performance.now() - startTime;
        cy.log(`وقت التحميل على الهاتف: ${loadTime.toFixed(2)} مللي ثانية`);
        expect(loadTime).to.be.lessThan(4000); // وقت أطول قليلاً للهواتف
      });
      
      // اختبار التفاعل على الهاتف
      cy.get('[data-testid="kpi-dashboard"]').should('be.visible');
      cy.contains('المبيعات').click();
      cy.get('[data-testid="sales-summary"]').should('be.visible');
    });

    it('يجب أن تعمل بكفاءة على الأجهزة اللوحية', () => {
      cy.viewport('ipad-2');
      
      const startTime = performance.now();
      cy.visitWithLocale('/merchant/reports');
      cy.contains('التقارير والتحليلات').should('be.visible');
      
      cy.then(() => {
        const loadTime = performance.now() - startTime;
        cy.log(`وقت التحميل على الجهاز اللوحي: ${loadTime.toFixed(2)} مللي ثانية`);
        expect(loadTime).to.be.lessThan(3500);
      });
      
      // اختبار عرض الرسوم البيانية على الجهاز اللوحي
      cy.get('[data-testid="advanced-charts-panel"]').should('be.visible');
      cy.get('[data-testid="chart-container"]').should('be.visible');
    });
  });

  describe('🌐 اختبارات الأداء مع الشبكة البطيئة', () => {
    it('يجب أن تعمل مع اتصال إنترنت بطيء', () => {
      // محاكاة شبكة بطيئة
      cy.intercept('**', (req) => {
        req.reply((res) => {
          // تأخير الاستجابة بـ 2 ثانية
          return new Promise(resolve => {
            setTimeout(() => resolve(res), 2000);
          });
        });
      });
      
      const startTime = performance.now();
      cy.visitWithLocale('/merchant/reports');
      
      // التحقق من ظهور مؤشر التحميل
      cy.get('[data-testid="loading-indicator"]').should('be.visible');
      
      // انتظار التحميل مع الشبكة البطيئة
      cy.contains('التقارير والتحليلات').should('be.visible');
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 20000 });
      
      cy.then(() => {
        const loadTime = performance.now() - startTime;
        cy.log(`وقت التحميل مع الشبكة البطيئة: ${loadTime.toFixed(2)} مللي ثانية`);
        // يجب أن يكون أقل من 15 ثانية حتى مع الشبكة البطيئة
        expect(loadTime).to.be.lessThan(15000);
      });
    });
  });

  describe('📈 اختبارات أداء الرسوم البيانية', () => {
    it('يجب أن ترسم الرسوم البيانية المعقدة بسرعة', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 15000 });
      
      // اختبار رسم بيانات كثيرة
      const startTime = performance.now();
      
      cy.get('[data-testid="chart-type-selector"]').click();
      cy.contains('خطي').click();
      
      cy.get('[data-testid="line-chart"]').should('be.visible');
      
      cy.then(() => {
        const renderTime = performance.now() - startTime;
        cy.log(`وقت رسم الرسم البياني الخطي: ${renderTime.toFixed(2)} مللي ثانية`);
        expect(renderTime).to.be.lessThan(2000);
      });
    });

    it('يجب أن تتعامل مع تحديث البيانات في الوقت الفعلي', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 15000 });
      
      // محاكاة تحديث البيانات
      const startTime = performance.now();
      
      cy.get('[data-testid="refresh-button"]').click();
      cy.get('[data-testid="loading-indicator"]').should('be.visible');
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 10000 });
      
      cy.then(() => {
        const updateTime = performance.now() - startTime;
        cy.log(`وقت تحديث البيانات: ${updateTime.toFixed(2)} مللي ثانية`);
        expect(updateTime).to.be.lessThan(5000);
      });
    });
  });

  describe('🔍 اختبارات الأداء مع البحث والفلترة', () => {
    it('يجب أن تستجيب عمليات البحث بسرعة', () => {
      cy.visitWithLocale('/merchant/reports');
      cy.contains('المنتجات').click();
      cy.get('[data-testid="loading-indicator"]').should('not.exist', { timeout: 15000 });
      
      // اختبار البحث في المنتجات
      const startTime = performance.now();
      
      cy.get('[data-testid="product-search"]').type('منتج 1');
      cy.get('[data-testid="search-results"]').should('be.visible');
      
      cy.then(() => {
        const searchTime = performance.now() - startTime;
        cy.log(`وقت البحث: ${searchTime.toFixed(2)} مللي ثانية`);
        expect(searchTime).to.be.lessThan(1000);
      });
    });
  });
});
