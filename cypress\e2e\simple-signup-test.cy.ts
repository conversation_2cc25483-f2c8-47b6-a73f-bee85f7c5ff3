describe('اختبار بسيط لإنشاء الحساب وتبديل اللغة', () => {
  beforeEach(() => {
    // زيارة الصفحة الرئيسية أولاً
    cy.visit('/')
    cy.wait(2000)
  })

  it('يجب أن تعمل الصفحة الرئيسية', () => {
    // التحقق من تحميل الصفحة
    cy.get('body').should('be.visible')
    cy.url().should('include', 'localhost:9002')
  })

  it('يجب أن يعمل تبديل اللغة في الصفحة الرئيسية', () => {
    // البحث عن زر تبديل اللغة
    cy.get('button').contains('EN').should('be.visible').click()
    cy.wait(2000)
    
    // التحقق من تغيير اللغة
    cy.url().should('include', '/en/')
    
    // العودة للعربية
    cy.get('button').contains('ع').should('be.visible').click()
    cy.wait(2000)
    
    cy.url().should('include', '/ar/')
  })

  it('يجب أن تعمل صفحة إنشاء الحساب', () => {
    // الانتقال لصفحة إنشاء الحساب
    cy.visit('/ar/signup')
    cy.wait(3000)
    
    // التحقق من تحميل الصفحة
    cy.get('body').should('be.visible')
    cy.url().should('include', '/signup')
  })

  it('يجب أن يعمل النقر على إنشاء حساب', () => {
    // البحث عن رابط إنشاء الحساب في الصفحة الرئيسية
    cy.contains('إنشاء حساب').should('be.visible').click()
    cy.wait(2000)
    
    // التحقق من الانتقال
    cy.url().should('include', '/signup')
  })

  it('يجب أن تعمل صفحة اختيار نوع المستخدم', () => {
    // الانتقال لصفحة اختيار نوع المستخدم
    cy.visit('/ar/user-type-selection')
    cy.wait(3000)
    
    // التحقق من وجود خيارات نوع المستخدم
    cy.get('body').should('contain', 'عميل')
    cy.get('body').should('contain', 'تاجر')
    cy.get('body').should('contain', 'مندوب')
  })

  it('يجب أن يعمل اختيار نوع المستخدم', () => {
    cy.visit('/ar/user-type-selection')
    cy.wait(3000)
    
    // البحث عن زر العميل والنقر عليه
    cy.contains('عميل').should('be.visible').click()
    cy.wait(2000)
    
    // التحقق من الانتقال لصفحة التسجيل
    cy.url().should('include', '/signup')
  })

  it('يجب أن يعمل تبديل اللغة في صفحة التسجيل', () => {
    cy.visit('/ar/signup?userType=customer')
    cy.wait(3000)
    
    // البحث عن زر تبديل اللغة
    cy.get('button').contains('EN').should('be.visible').click()
    cy.wait(2000)
    
    // التحقق من تغيير اللغة
    cy.url().should('include', '/en/')
    
    // العودة للعربية
    cy.get('button').contains('ع').should('be.visible').click()
    cy.wait(2000)
    
    cy.url().should('include', '/ar/')
  })

  it('يجب أن تعمل صفحة تسجيل الدخول', () => {
    cy.visit('/ar/login')
    cy.wait(3000)
    
    // التحقق من وجود نموذج تسجيل الدخول
    cy.get('input[type="email"]').should('be.visible')
    cy.get('input[type="password"]').should('be.visible')
  })

  it('يجب أن يعمل التبديل بين تسجيل الدخول والتسجيل', () => {
    cy.visit('/ar/login')
    cy.wait(3000)
    
    // البحث عن رابط إنشاء حساب
    cy.contains('إنشاء حساب').should('be.visible').click()
    cy.wait(2000)
    
    // التحقق من الانتقال
    cy.url().should('include', '/signup')
    
    // العودة لتسجيل الدخول
    cy.contains('تسجيل الدخول').should('be.visible').click()
    cy.wait(2000)
    
    cy.url().should('include', '/login')
  })
})
