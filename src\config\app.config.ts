/**
 * تكوين التطبيق الثابت - مِخْلاة
 * يحتوي على الإعدادات الثابتة التي لا تحتاج لمتغيرات البيئة
 */

export const APP_CONFIG = {
  // إعدادات التطبيق الأساسية
  app: {
    name: 'مِخْلاة',
    version: '24.1.8',
    description: 'منصة التجارة الإلكترونية الذكية',
    defaultLocale: 'ar',
    supportedLocales: ['ar', 'en'],
    timezone: 'Asia/Riyadh'
  },

  // إعدادات الأمان
  security: {
    encryptionAlgorithm: 'AES-256-GCM',
    sessionTimeout: 30, // دقيقة
    maxLoginAttempts: 5,
    passwordComplexity: 'high',
    enableBruteForceProtection: true,
    enable2FA: false // يمكن تفعيله لاحقاً
  },

  // إعدادات الدفع
  payment: {
    supportedCurrencies: ['SAR', 'USD', 'EUR', 'GBP'],
    defaultCurrency: 'SAR',
    freeDeliveryThreshold: 100, // ريال سعودي
    deliveryFee: 15, // ريال سعودي
    methods: {
      paypal: {
        enabled: true,
        feePercentage: 3.4,
        supportedCurrencies: ['SAR', 'USD', 'EUR', 'GBP'],
        limits: { min: 1, max: 100000 }
      },
      mada: {
        enabled: true,
        feePercentage: 1.5,
        supportedCurrencies: ['SAR'],
        limits: { min: 1, max: 50000 }
      },
      visa: {
        enabled: true,
        feePercentage: 2.5,
        fixedFee: 2,
        supportedCurrencies: ['SAR', 'USD', 'EUR'],
        limits: { min: 1, max: 100000 }
      },
      mastercard: {
        enabled: true,
        feePercentage: 2.5,
        fixedFee: 2,
        supportedCurrencies: ['SAR', 'USD', 'EUR'],
        limits: { min: 1, max: 100000 }
      },
      applePay: {
        enabled: true,
        feePercentage: 2.0,
        supportedCurrencies: ['SAR', 'USD'],
        limits: { min: 1, max: 25000 }
      },
      googlePay: {
        enabled: true,
        feePercentage: 2.0,
        supportedCurrencies: ['SAR', 'USD'],
        limits: { min: 1, max: 25000 }
      },
      stcPay: {
        enabled: true,
        feePercentage: 1.0,
        supportedCurrencies: ['SAR'],
        limits: { min: 1, max: 30000 }
      }
    }
  },

  // إعدادات التقسيط
  installment: {
    tamara: {
      enabled: true,
      months: [3],
      interestRate: 3.0,
      limits: { min: 100, max: 20000 }
    },
    tabby: {
      enabled: true,
      months: [6],
      interestRate: 5.0,
      limits: { min: 200, max: 50000 }
    }
  },

  // إعدادات التخزين المؤقت
  cache: {
    ttlSeconds: 3600,
    maxSizeMB: 512,
    enableRedis: false // يتم تفعيله حسب البيئة
  },

  // إعدادات الملفات
  files: {
    maxDocumentSizeMB: 10,
    allowedImageTypes: ['jpg', 'jpeg', 'png', 'webp'],
    allowedDocumentTypes: ['pdf', 'doc', 'docx'],
    maxImagesPerProduct: 10
  },

  // إعدادات الذكاء الاصطناعي
  ai: {
    useLocalAI: false, // للإنتاج السحابي
    maxConcurrentAnalysis: 3,
    analysisTimeout: 30000,
    cacheEnabled: true,
    securityLevel: 'high'
  },

  // إعدادات الشبكة
  network: {
    rateLimitRequestsPerMinute: 100,
    corsOrigins: ['https://mikhla.com', 'https://admin.mikhla.com'],
    enableBrotli: true,
    enableGzip: true
  },

  // إعدادات المراقبة
  monitoring: {
    enabled: true,
    metricsEndpoint: '/metrics',
    healthCheckEndpoint: '/health',
    logLevel: 'info' // debug, info, warn, error
  },

  // إعدادات النسخ الاحتياطي
  backup: {
    enabled: true,
    schedule: '0 2 * * *', // يومياً في الساعة 2 صباحاً
    retention: 30, // يوم
    encryption: 'AES256'
  },

  // إعدادات الإشعارات
  notifications: {
    enableEmail: true,
    enableSMS: true,
    enablePush: true,
    enableWhatsApp: false, // يتم تفعيله حسب الحاجة
    defaultChannels: ['email', 'sms']
  },

  // إعدادات البحث
  search: {
    maxResults: 50,
    enableFuzzySearch: true,
    enableAutoComplete: true,
    cacheResults: true
  },

  // إعدادات الخرائط
  maps: {
    defaultZoom: 12,
    maxZoom: 18,
    enableClustering: true,
    clusterRadius: 50
  }
} as const;

// نوع TypeScript للتكوين
export type AppConfig = typeof APP_CONFIG;

// دالة للحصول على إعداد معين
export function getConfig<T extends keyof AppConfig>(section: T): AppConfig[T] {
  return APP_CONFIG[section];
}

// دالة للحصول على إعداد فرعي
export function getNestedConfig<T extends keyof AppConfig, K extends keyof AppConfig[T]>(
  section: T,
  key: K
): AppConfig[T][K] {
  return APP_CONFIG[section][key];
}

// إعدادات البيئة (فقط المتغيرات الحساسة)
export const ENV_CONFIG = {
  // Firebase (ضروري)
  firebase: {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
    measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    privateKey: process.env.FIREBASE_PRIVATE_KEY,
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL
  },

  // PayPal (ضروري)
  paypal: {
    clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID,
    clientSecret: process.env.PAYPAL_CLIENT_SECRET,
    webhookId: process.env.PAYPAL_WEBHOOK_ID
  },

  // التطبيق
  app: {
    url: process.env.NEXT_PUBLIC_APP_URL || 'https://mikhla.com',
    nodeEnv: process.env.NODE_ENV || 'production'
  },

  // التشفير (ضروري للأمان)
  encryption: {
    key: process.env.DOCUMENT_ENCRYPTION_KEY
  }
} as const;
