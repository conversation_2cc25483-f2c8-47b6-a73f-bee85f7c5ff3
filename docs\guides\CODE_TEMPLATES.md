# 📝 قوالب الكود الجاهزة للميزات المفقودة

> **مجموعة من قوالب الكود الجاهزة لتسريع تطوير الميزات المفقودة**

## 📊 **حالة القوالب**

### ✅ **مكتمل ومطبق (14 يونيو 2025)**
- ✅ **نظام الكوبونات** - مطبق بالكامل مع جميع المكونات
- ✅ **نظام الولاء** - مطبق بالكامل مع جميع المكونات

### 🚧 **قيد التطوير**
- 🔄 نظام CRM
- 🔄 إدارة الفروع
- 🔄 نظام الأتمتة

---

## 📋 **فهرس القوالب**

1. [✅ نظام الكوبونات (مكتمل)](#coupon-system)
2. [✅ نظام الولاء (مكتمل)](#loyalty-system)
3. [🚧 نظام CRM](#crm-system)
4. [🚧 إدارة الفروع](#branch-management)
5. [🚧 نظام الأتمتة](#automation-system)

---

## 🎟️ **نظام الكوبونات** {#coupon-system}

> ✅ **مكتمل ومطبق بالكامل** - 14 يونيو 2025

**الملفات المطبقة:**
- ✅ `src/types/coupon.ts` - نموذج بيانات شامل
- ✅ `src/services/couponService.ts` - خدمة إدارة متقدمة
- ✅ `src/components/merchant/CreateCouponForm.tsx` - نموذج إنشاء
- ✅ `src/components/merchant/CouponsList.tsx` - قائمة إدارة
- ✅ `src/components/customer/CouponApply.tsx` - تطبيق للعملاء
- ✅ `src/app/[locale]/merchant/coupons/page.tsx` - صفحة إدارة

**الميزات المطبقة:**
- ✅ أنواع خصومات متعددة (نسبة، مبلغ ثابت، شحن مجاني)
- ✅ شروط تطبيق متقدمة (منتجات، فئات، عملاء)
- ✅ تحليلات وإحصائيات شاملة
- ✅ نظام انتهاء صلاحية وحدود الاستخدام

### 1. **نموذج البيانات (Types)**

```typescript
// src/types/coupon.ts
export interface CouponDocument {
  id: string;
  merchantId: string;
  code: string;
  type: 'percentage' | 'fixed' | 'free_shipping';
  value: number;
  minOrderAmount?: number;
  maxDiscount?: number;
  usageLimit: number;
  usedCount: number;
  validFrom: Timestamp;
  validUntil: Timestamp;
  isActive: boolean;
  applicableProducts?: string[];
  applicableCategories?: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface CouponValidation {
  isValid: boolean;
  discount: number;
  message: string;
  coupon?: CouponDocument;
}

export interface DiscountResult {
  originalAmount: number;
  discountAmount: number;
  finalAmount: number;
  couponCode: string;
}
```

### 2. **خدمة الكوبونات (Service)**

```typescript
// src/services/couponService.ts
import { db } from '@/lib/firebase';
import { collection, doc, addDoc, updateDoc, query, where, getDocs } from 'firebase/firestore';
import { CouponDocument, CouponValidation, DiscountResult } from '@/types/coupon';

export class CouponService {
  private couponsCollection = collection(db, 'coupons');

  async createCoupon(merchantId: string, couponData: Partial<CouponDocument>): Promise<string> {
    try {
      const newCoupon: Partial<CouponDocument> = {
        ...couponData,
        merchantId,
        usedCount: 0,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const docRef = await addDoc(this.couponsCollection, newCoupon);
      return docRef.id;
    } catch (error) {
      console.error('Error creating coupon:', error);
      throw new Error('فشل في إنشاء الكوبون');
    }
  }

  async validateCoupon(code: string, cartTotal: number, productIds: string[]): Promise<CouponValidation> {
    try {
      const q = query(this.couponsCollection, where('code', '==', code), where('isActive', '==', true));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        return { isValid: false, discount: 0, message: 'كوبون غير صالح' };
      }

      const couponDoc = querySnapshot.docs[0];
      const coupon = { id: couponDoc.id, ...couponDoc.data() } as CouponDocument;

      // التحقق من تاريخ الانتهاء
      if (new Date() > coupon.validUntil.toDate()) {
        return { isValid: false, discount: 0, message: 'انتهت صلاحية الكوبون' };
      }

      // التحقق من حد الاستخدام
      if (coupon.usedCount >= coupon.usageLimit) {
        return { isValid: false, discount: 0, message: 'تم استنفاد استخدامات الكوبون' };
      }

      // التحقق من الحد الأدنى للطلب
      if (coupon.minOrderAmount && cartTotal < coupon.minOrderAmount) {
        return { 
          isValid: false, 
          discount: 0, 
          message: `الحد الأدنى للطلب ${coupon.minOrderAmount} ريال` 
        };
      }

      // حساب الخصم
      let discount = 0;
      if (coupon.type === 'percentage') {
        discount = (cartTotal * coupon.value) / 100;
        if (coupon.maxDiscount && discount > coupon.maxDiscount) {
          discount = coupon.maxDiscount;
        }
      } else if (coupon.type === 'fixed') {
        discount = Math.min(coupon.value, cartTotal);
      } else if (coupon.type === 'free_shipping') {
        discount = 0; // يتم التعامل مع الشحن المجاني بشكل منفصل
      }

      return {
        isValid: true,
        discount,
        message: 'تم تطبيق الكوبون بنجاح',
        coupon
      };
    } catch (error) {
      console.error('Error validating coupon:', error);
      return { isValid: false, discount: 0, message: 'خطأ في التحقق من الكوبون' };
    }
  }

  async applyCoupon(couponId: string): Promise<void> {
    try {
      const couponRef = doc(this.couponsCollection, couponId);
      await updateDoc(couponRef, {
        usedCount: increment(1),
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error applying coupon:', error);
      throw new Error('فشل في تطبيق الكوبون');
    }
  }
}

export const couponService = new CouponService();
```

### 3. **مكون إنشاء الكوبون (Component)**

```typescript
// src/components/merchant/CreateCouponForm.tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { couponService } from '@/services/couponService';
import { toast } from 'sonner';

const couponSchema = z.object({
  code: z.string().min(3, 'كود الكوبون يجب أن يكون 3 أحرف على الأقل'),
  type: z.enum(['percentage', 'fixed', 'free_shipping']),
  value: z.number().min(0, 'القيمة يجب أن تكون أكبر من صفر'),
  minOrderAmount: z.number().optional(),
  maxDiscount: z.number().optional(),
  usageLimit: z.number().min(1, 'حد الاستخدام يجب أن يكون 1 على الأقل'),
  validFrom: z.string(),
  validUntil: z.string(),
});

type CouponFormData = z.infer<typeof couponSchema>;

interface CreateCouponFormProps {
  merchantId: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export function CreateCouponForm({ merchantId, onSuccess, onCancel }: CreateCouponFormProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<CouponFormData>({
    resolver: zodResolver(couponSchema),
    defaultValues: {
      code: '',
      type: 'percentage',
      value: 0,
      usageLimit: 100,
      validFrom: new Date().toISOString().split('T')[0],
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    },
  });

  const onSubmit = async (data: CouponFormData) => {
    setIsLoading(true);
    try {
      await couponService.createCoupon(merchantId, {
        ...data,
        validFrom: new Date(data.validFrom),
        validUntil: new Date(data.validUntil),
      });
      
      toast.success('تم إنشاء الكوبون بنجاح');
      onSuccess();
    } catch (error) {
      toast.error('فشل في إنشاء الكوبون');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel>كود الكوبون</FormLabel>
              <FormControl>
                <Input placeholder="مثال: SAVE10" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>نوع الخصم</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر نوع الخصم" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="percentage">نسبة مئوية</SelectItem>
                  <SelectItem value="fixed">مبلغ ثابت</SelectItem>
                  <SelectItem value="free_shipping">شحن مجاني</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="value"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {form.watch('type') === 'percentage' ? 'نسبة الخصم (%)' : 'مبلغ الخصم (ريال)'}
              </FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  {...field} 
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="validFrom"
            render={({ field }) => (
              <FormItem>
                <FormLabel>تاريخ البداية</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="validUntil"
            render={({ field }) => (
              <FormItem>
                <FormLabel>تاريخ الانتهاء</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="usageLimit"
          render={({ field }) => (
            <FormItem>
              <FormLabel>حد الاستخدام</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  {...field} 
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-4">
          <Button type="submit" disabled={isLoading} className="flex-1">
            {isLoading ? 'جاري الإنشاء...' : 'إنشاء الكوبون'}
          </Button>
          <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
            إلغاء
          </Button>
        </div>
      </form>
    </Form>
  );
}
```

---

## 🏆 **نظام الولاء** {#loyalty-system}

> ✅ **مكتمل ومطبق بالكامل** - 14 يونيو 2025

**الملفات المطبقة:**
- ✅ `src/types/loyalty.ts` - نموذج بيانات متقدم
- ✅ `src/services/loyaltyService.ts` - خدمة إدارة شاملة
- ✅ `src/components/customer/LoyaltyCard.tsx` - بطاقة الولاء
- ✅ `src/components/customer/LoyaltyRewards.tsx` - عرض المكافآت
- ✅ `src/components/merchant/LoyaltyProgramManager.tsx` - إدارة البرنامج
- ✅ `src/app/[locale]/loyalty/page.tsx` - صفحة العملاء
- ✅ `src/app/[locale]/merchant/loyalty/page.tsx` - صفحة التجار

**الميزات المطبقة:**
- ✅ 4 مستويات عضوية (برونزي، فضي، ذهبي، بلاتيني)
- ✅ نظام نقاط مع مضاعفات حسب المستوى
- ✅ مكافآت متنوعة وقابلة للاستبدال
- ✅ تتبع تقدم المستويات والترقية التلقائية
- ✅ تحليلات متقدمة لبرنامج الولاء

### 1. **نموذج البيانات**

```typescript
// src/types/loyalty.ts
export interface LoyaltyPointsDocument {
  id: string;
  customerId: string;
  merchantId: string;
  totalPoints: number;
  availablePoints: number;
  usedPoints: number;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  transactions: LoyaltyTransaction[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface LoyaltyTransaction {
  id: string;
  type: 'earned' | 'redeemed' | 'expired';
  points: number;
  orderId?: string;
  description: string;
  createdAt: Timestamp;
}

export interface LoyaltySettings {
  pointsPerRiyal: number;
  minimumRedemption: number;
  pointsExpiry: number; // بالأيام
  tiers: {
    bronze: { minPoints: number; multiplier: number };
    silver: { minPoints: number; multiplier: number };
    gold: { minPoints: number; multiplier: number };
    platinum: { minPoints: number; multiplier: number };
  };
}
```

### 2. **خدمة نظام الولاء**

```typescript
// src/services/loyaltyService.ts
import { db } from '@/lib/firebase';
import { collection, doc, getDoc, setDoc, updateDoc, arrayUnion } from 'firebase/firestore';
import { LoyaltyPointsDocument, LoyaltyTransaction, LoyaltySettings } from '@/types/loyalty';

export class LoyaltyService {
  private loyaltyCollection = collection(db, 'loyalty_points');
  private settingsCollection = collection(db, 'loyalty_settings');

  async addPoints(
    customerId: string, 
    merchantId: string, 
    points: number, 
    description: string,
    orderId?: string
  ): Promise<void> {
    try {
      const loyaltyDocId = `${customerId}_${merchantId}`;
      const loyaltyRef = doc(this.loyaltyCollection, loyaltyDocId);
      
      const loyaltyDoc = await getDoc(loyaltyRef);
      
      const transaction: LoyaltyTransaction = {
        id: Date.now().toString(),
        type: 'earned',
        points,
        orderId,
        description,
        createdAt: new Date(),
      };

      if (loyaltyDoc.exists()) {
        const currentData = loyaltyDoc.data() as LoyaltyPointsDocument;
        const newTotalPoints = currentData.totalPoints + points;
        const newAvailablePoints = currentData.availablePoints + points;
        const newTier = this.calculateTier(newTotalPoints);

        await updateDoc(loyaltyRef, {
          totalPoints: newTotalPoints,
          availablePoints: newAvailablePoints,
          tier: newTier,
          transactions: arrayUnion(transaction),
          updatedAt: new Date(),
        });
      } else {
        const newLoyaltyDoc: Partial<LoyaltyPointsDocument> = {
          customerId,
          merchantId,
          totalPoints: points,
          availablePoints: points,
          usedPoints: 0,
          tier: this.calculateTier(points),
          transactions: [transaction],
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        await setDoc(loyaltyRef, newLoyaltyDoc);
      }
    } catch (error) {
      console.error('Error adding loyalty points:', error);
      throw new Error('فشل في إضافة نقاط الولاء');
    }
  }

  async redeemPoints(
    customerId: string,
    merchantId: string,
    points: number,
    description: string
  ): Promise<void> {
    try {
      const loyaltyDocId = `${customerId}_${merchantId}`;
      const loyaltyRef = doc(this.loyaltyCollection, loyaltyDocId);
      
      const loyaltyDoc = await getDoc(loyaltyRef);
      
      if (!loyaltyDoc.exists()) {
        throw new Error('لا يوجد رصيد نقاط للعميل');
      }

      const currentData = loyaltyDoc.data() as LoyaltyPointsDocument;
      
      if (currentData.availablePoints < points) {
        throw new Error('رصيد النقاط غير كافي');
      }

      const transaction: LoyaltyTransaction = {
        id: Date.now().toString(),
        type: 'redeemed',
        points: -points,
        description,
        createdAt: new Date(),
      };

      await updateDoc(loyaltyRef, {
        availablePoints: currentData.availablePoints - points,
        usedPoints: currentData.usedPoints + points,
        transactions: arrayUnion(transaction),
        updatedAt: new Date(),
      });
    } catch (error) {
      console.error('Error redeeming loyalty points:', error);
      throw new Error('فشل في استبدال نقاط الولاء');
    }
  }

  private calculateTier(totalPoints: number): 'bronze' | 'silver' | 'gold' | 'platinum' {
    if (totalPoints >= 10000) return 'platinum';
    if (totalPoints >= 5000) return 'gold';
    if (totalPoints >= 1000) return 'silver';
    return 'bronze';
  }

  async getPointsBalance(customerId: string, merchantId: string): Promise<LoyaltyPointsDocument | null> {
    try {
      const loyaltyDocId = `${customerId}_${merchantId}`;
      const loyaltyRef = doc(this.loyaltyCollection, loyaltyDocId);
      const loyaltyDoc = await getDoc(loyaltyRef);
      
      if (loyaltyDoc.exists()) {
        return { id: loyaltyDoc.id, ...loyaltyDoc.data() } as LoyaltyPointsDocument;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting points balance:', error);
      return null;
    }
  }
}

export const loyaltyService = new LoyaltyService();
```

---

## 🤝 **نظام CRM** {#crm-system}

### 1. **نموذج البيانات**

```typescript
// src/types/crm.ts
export interface CRMCustomerDocument {
  id: string;
  merchantId: string;
  customerId: string;
  profile: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth?: Timestamp;
    gender?: 'male' | 'female';
    location?: GeoPoint;
  };
  preferences: {
    categories: string[];
    brands: string[];
    priceRange: { min: number; max: number };
    communicationChannels: string[];
  };
  behavior: {
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate?: Timestamp;
    favoriteProducts: string[];
    abandonedCarts: number;
  };
  interactions: CRMInteraction[];
  tags: string[];
  notes: string;
  riskScore: number; // 0-100
  lifetimeValue: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface CRMInteraction {
  id: string;
  type: 'email' | 'sms' | 'call' | 'chat' | 'order' | 'support' | 'complaint';
  subject: string;
  description: string;
  status: 'pending' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdBy: string;
  assignedTo?: string;
  createdAt: Timestamp;
  completedAt?: Timestamp;
}
```

### 2. **خدمة CRM**

```typescript
// src/services/crmService.ts
import { db } from '@/lib/firebase';
import { collection, doc, getDoc, setDoc, updateDoc, query, where, getDocs } from 'firebase/firestore';
import { CRMCustomerDocument, CRMInteraction } from '@/types/crm';

export class CRMService {
  private crmCollection = collection(db, 'crm_customers');

  async createCustomerProfile(
    customerId: string,
    merchantId: string,
    profileData: Partial<CRMCustomerDocument>
  ): Promise<void> {
    try {
      const crmDocId = `${customerId}_${merchantId}`;
      const crmRef = doc(this.crmCollection, crmDocId);

      const newProfile: Partial<CRMCustomerDocument> = {
        ...profileData,
        customerId,
        merchantId,
        interactions: [],
        tags: [],
        riskScore: 0,
        lifetimeValue: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await setDoc(crmRef, newProfile);
    } catch (error) {
      console.error('Error creating CRM customer profile:', error);
      throw new Error('فشل في إنشاء ملف العميل');
    }
  }

  async addInteraction(
    customerId: string,
    merchantId: string,
    interaction: Partial<CRMInteraction>
  ): Promise<void> {
    try {
      const crmDocId = `${customerId}_${merchantId}`;
      const crmRef = doc(this.crmCollection, crmDocId);
      
      const newInteraction: CRMInteraction = {
        id: Date.now().toString(),
        type: interaction.type || 'chat',
        subject: interaction.subject || '',
        description: interaction.description || '',
        status: interaction.status || 'pending',
        priority: interaction.priority || 'medium',
        createdBy: interaction.createdBy || '',
        createdAt: new Date(),
        ...interaction,
      };

      const crmDoc = await getDoc(crmRef);
      if (crmDoc.exists()) {
        const currentData = crmDoc.data() as CRMCustomerDocument;
        await updateDoc(crmRef, {
          interactions: [...currentData.interactions, newInteraction],
          updatedAt: new Date(),
        });
      }
    } catch (error) {
      console.error('Error adding CRM interaction:', error);
      throw new Error('فشل في إضافة التفاعل');
    }
  }

  async updateCustomerBehavior(
    customerId: string,
    merchantId: string,
    behaviorData: Partial<CRMCustomerDocument['behavior']>
  ): Promise<void> {
    try {
      const crmDocId = `${customerId}_${merchantId}`;
      const crmRef = doc(this.crmCollection, crmDocId);
      
      await updateDoc(crmRef, {
        behavior: behaviorData,
        updatedAt: new Date(),
      });
    } catch (error) {
      console.error('Error updating customer behavior:', error);
      throw new Error('فشل في تحديث سلوك العميل');
    }
  }

  async getCustomerProfile(customerId: string, merchantId: string): Promise<CRMCustomerDocument | null> {
    try {
      const crmDocId = `${customerId}_${merchantId}`;
      const crmRef = doc(this.crmCollection, crmDocId);
      const crmDoc = await getDoc(crmRef);
      
      if (crmDoc.exists()) {
        return { id: crmDoc.id, ...crmDoc.data() } as CRMCustomerDocument;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting customer profile:', error);
      return null;
    }
  }

  async getMerchantCustomers(merchantId: string): Promise<CRMCustomerDocument[]> {
    try {
      const q = query(this.crmCollection, where('merchantId', '==', merchantId));
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CRMCustomerDocument[];
    } catch (error) {
      console.error('Error getting merchant customers:', error);
      return [];
    }
  }
}

export const crmService = new CRMService();
```

---

*تم إنشاء هذا الملف في 14 يونيو 2025*  
*آخر تحديث: 14 يونيو 2025*
